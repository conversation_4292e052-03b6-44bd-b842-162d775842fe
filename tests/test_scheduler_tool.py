"""
定时任务调度工具测试

测试定时任务调度和管理功能。
"""

import asyncio
import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.scheduler_tool import (
    SchedulerTool, SchedulerConfig, TaskScheduler, ScheduledTask,
    TaskStatus, TriggerType, CronParser
)


class TestSchedulerTool(unittest.TestCase):
    """调度器工具测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.scheduler_config = SchedulerConfig(
            auto_start=False,  # 测试时不自动启动
            max_concurrent_tasks=3
        )
        self.scheduler_tool = SchedulerTool(self.scheduler_config)


class TestCronParser(TestSchedulerTool):
    """Cron表达式解析器测试"""
    
    def test_parse_simple_cron(self):
        """测试简单Cron表达式解析"""
        # 每分钟
        result = CronParser.parse_cron("* * * * *")
        self.assertEqual(result["minute"], list(range(0, 60)))
        self.assertEqual(result["hour"], list(range(0, 24)))
        
        # 每天午夜
        result = CronParser.parse_cron("0 0 * * *")
        self.assertEqual(result["minute"], [0])
        self.assertEqual(result["hour"], [0])
        
        # 每周一上午9点
        result = CronParser.parse_cron("0 9 * * 1")
        self.assertEqual(result["minute"], [0])
        self.assertEqual(result["hour"], [9])
        self.assertEqual(result["weekday"], [1])
    
    def test_parse_range_cron(self):
        """测试范围Cron表达式解析"""
        # 工作日上午9-17点
        result = CronParser.parse_cron("0 9-17 * * 1-5")
        self.assertEqual(result["hour"], list(range(9, 18)))
        self.assertEqual(result["weekday"], list(range(1, 6)))
    
    def test_parse_step_cron(self):
        """测试步长Cron表达式解析"""
        # 每5分钟
        result = CronParser.parse_cron("*/5 * * * *")
        self.assertEqual(result["minute"], list(range(0, 60, 5)))
        
        # 每2小时
        result = CronParser.parse_cron("0 */2 * * *")
        self.assertEqual(result["hour"], list(range(0, 24, 2)))
    
    def test_parse_list_cron(self):
        """测试列表Cron表达式解析"""
        # 特定分钟
        result = CronParser.parse_cron("0,15,30,45 * * * *")
        self.assertEqual(result["minute"], [0, 15, 30, 45])
    
    def test_invalid_cron(self):
        """测试无效Cron表达式"""
        with self.assertRaises(ValueError):
            CronParser.parse_cron("* * *")  # 缺少字段
        
        with self.assertRaises(ValueError):
            CronParser.parse_cron("60 * * * *")  # 分钟超出范围
    
    def test_next_run_time(self):
        """测试下次执行时间计算"""
        # 每分钟执行
        cron_config = CronParser.parse_cron("* * * * *")
        from_time = datetime(2025, 1, 1, 12, 30, 30)
        next_time = CronParser.next_run_time(cron_config, from_time)
        
        # 应该是下一分钟的开始
        expected = datetime(2025, 1, 1, 12, 31, 0)
        self.assertEqual(next_time, expected)


class TestScheduledTask(TestSchedulerTool):
    """定时任务模型测试"""
    
    def test_task_creation(self):
        """测试任务创建"""
        task = ScheduledTask(
            name="测试任务",
            description="这是一个测试任务",
            trigger_type=TriggerType.CRON,
            trigger_config={"expression": "0 9 * * *"},
            task_config={"type": "custom", "action": "test"}
        )
        
        self.assertEqual(task.name, "测试任务")
        self.assertEqual(task.trigger_type, TriggerType.CRON)
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertEqual(task.run_count, 0)
        self.assertIsNotNone(task.id)
    
    def test_task_status_update(self):
        """测试任务状态更新"""
        task = ScheduledTask(
            name="测试任务",
            trigger_type=TriggerType.INTERVAL,
            trigger_config={"seconds": 60},
            task_config={"type": "custom"}
        )
        
        original_time = task.updated_at
        task.update_status(TaskStatus.RUNNING)
        
        self.assertEqual(task.status, TaskStatus.RUNNING)
        self.assertGreater(task.updated_at, original_time)


class TestTaskScheduler(TestSchedulerTool):
    """任务调度器测试"""
    
    async def test_scheduler_start_stop(self):
        """测试调度器启动和停止"""
        scheduler = TaskScheduler()
        
        # 启动调度器
        await scheduler.start()
        self.assertTrue(scheduler._running)
        
        # 停止调度器
        await scheduler.stop()
        self.assertFalse(scheduler._running)
    
    async def test_add_remove_task(self):
        """测试添加和移除任务"""
        scheduler = TaskScheduler()
        
        task = ScheduledTask(
            name="测试任务",
            trigger_type=TriggerType.INTERVAL,
            trigger_config={"seconds": 60},
            task_config={"type": "custom"}
        )
        
        # 添加任务
        task_id = scheduler.add_task(task)
        self.assertEqual(task_id, task.id)
        self.assertIn(task_id, scheduler._tasks)
        
        # 获取任务
        retrieved_task = scheduler.get_task(task_id)
        self.assertEqual(retrieved_task.id, task_id)
        
        # 移除任务
        success = scheduler.remove_task(task_id)
        self.assertTrue(success)
        self.assertNotIn(task_id, scheduler._tasks)
        
        # 移除不存在的任务
        success = scheduler.remove_task("nonexistent")
        self.assertFalse(success)
    
    async def test_list_tasks(self):
        """测试列出任务"""
        scheduler = TaskScheduler()
        
        # 添加不同状态的任务
        task1 = ScheduledTask(
            name="任务1",
            trigger_type=TriggerType.INTERVAL,
            trigger_config={"seconds": 60},
            task_config={"type": "custom"}
        )
        task1.status = TaskStatus.PENDING
        
        task2 = ScheduledTask(
            name="任务2",
            trigger_type=TriggerType.INTERVAL,
            trigger_config={"seconds": 120},
            task_config={"type": "custom"}
        )
        task2.status = TaskStatus.RUNNING
        
        scheduler.add_task(task1)
        scheduler.add_task(task2)
        
        # 列出所有任务
        all_tasks = scheduler.list_tasks()
        self.assertEqual(len(all_tasks), 2)
        
        # 按状态过滤
        pending_tasks = scheduler.list_tasks(TaskStatus.PENDING)
        self.assertEqual(len(pending_tasks), 1)
        self.assertEqual(pending_tasks[0].name, "任务1")
        
        running_tasks = scheduler.list_tasks(TaskStatus.RUNNING)
        self.assertEqual(len(running_tasks), 1)
        self.assertEqual(running_tasks[0].name, "任务2")


class TestSchedulerToolOperations(TestSchedulerTool):
    """调度器工具操作测试"""
    
    async def test_create_task_action(self):
        """测试创建任务操作"""
        result = await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务",
                "description": "这是一个测试任务",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 60},
                "task_config": {"type": "custom", "action": "test"},
                "timeout_seconds": 30
            }
        })
        
        self.assertTrue(result.success)
        self.assertIn("task_id", result.result)
        self.assertIn("message", result.result)
    
    async def test_create_task_missing_params(self):
        """测试创建任务缺少参数"""
        result = await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务"
                # 缺少必需参数
            }
        })
        
        self.assertFalse(result.success)
        self.assertIn("缺少必需参数", result.error)
    
    async def test_list_tasks_action(self):
        """测试列出任务操作"""
        # 先创建一个任务
        await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 60},
                "task_config": {"type": "custom"}
            }
        })
        
        # 列出任务
        result = await self.scheduler_tool.execute({
            "action": "list_tasks"
        })
        
        self.assertTrue(result.success)
        self.assertIn("tasks", result.result)
        self.assertIn("count", result.result)
        self.assertGreater(result.result["count"], 0)
    
    async def test_get_task_action(self):
        """测试获取任务操作"""
        # 先创建一个任务
        create_result = await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 60},
                "task_config": {"type": "custom"}
            }
        })
        
        task_id = create_result.result["task_id"]
        
        # 获取任务
        result = await self.scheduler_tool.execute({
            "action": "get_task",
            "task_id": task_id
        })
        
        self.assertTrue(result.success)
        self.assertIn("task", result.result)
        self.assertEqual(result.result["task"]["id"], task_id)
    
    async def test_delete_task_action(self):
        """测试删除任务操作"""
        # 先创建一个任务
        create_result = await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 60},
                "task_config": {"type": "custom"}
            }
        })
        
        task_id = create_result.result["task_id"]
        
        # 删除任务
        result = await self.scheduler_tool.execute({
            "action": "delete_task",
            "task_id": task_id
        })
        
        self.assertTrue(result.success)
        self.assertIn("删除成功", result.result["message"])
        
        # 验证任务已删除
        get_result = await self.scheduler_tool.execute({
            "action": "get_task",
            "task_id": task_id
        })
        
        self.assertFalse(get_result.success)
    
    async def test_pause_resume_task(self):
        """测试暂停和恢复任务"""
        # 创建任务
        create_result = await self.scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "测试任务",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 60},
                "task_config": {"type": "custom"}
            }
        })
        
        task_id = create_result.result["task_id"]
        
        # 暂停任务
        pause_result = await self.scheduler_tool.execute({
            "action": "pause_task",
            "task_id": task_id
        })
        
        self.assertTrue(pause_result.success)
        
        # 验证任务状态
        get_result = await self.scheduler_tool.execute({
            "action": "get_task",
            "task_id": task_id
        })
        
        self.assertEqual(get_result.result["task"]["status"], "paused")
        
        # 恢复任务
        resume_result = await self.scheduler_tool.execute({
            "action": "resume_task",
            "task_id": task_id
        })
        
        self.assertTrue(resume_result.success)
        
        # 验证任务状态
        get_result = await self.scheduler_tool.execute({
            "action": "get_task",
            "task_id": task_id
        })
        
        self.assertEqual(get_result.result["task"]["status"], "pending")
    
    async def test_scheduler_status_action(self):
        """测试调度器状态操作"""
        result = await self.scheduler_tool.execute({
            "action": "scheduler_status"
        })
        
        self.assertTrue(result.success)
        self.assertIn("running", result.result)
        self.assertIn("total_tasks", result.result)
        self.assertIn("running_tasks", result.result)
        self.assertIn("pending_tasks", result.result)
    
    async def test_invalid_action(self):
        """测试无效操作"""
        result = await self.scheduler_tool.execute({
            "action": "invalid_action"
        })
        
        self.assertFalse(result.success)
        self.assertIn("不支持的操作", result.error)
    
    async def test_missing_action(self):
        """测试缺少操作参数"""
        result = await self.scheduler_tool.execute({})
        
        self.assertFalse(result.success)
        self.assertIn("缺少action参数", result.error)


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestTaskScheduler, TestSchedulerToolOperations]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
