"""
Redis适配器测试

测试Redis消息队列和缓存适配器的功能。
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from ai_agent_framework.tools import MessageQueueTool, CacheTool, MessageQueueType, CacheType
from ai_agent_framework.tools.message_queue_tool import QueueMessage, MessagePriority


class TestRedisAdapters:
    """Redis适配器测试"""
    
    @pytest.mark.asyncio
    async def test_redis_queue_adapter_import_error(self):
        """测试Redis适配器导入错误处理"""
        with patch('ai_agent_framework.tools.adapters.redis_adapters.REDIS_AVAILABLE', False):
            with pytest.raises(ImportError, match="Redis适配器需要redis库"):
                MessageQueueTool(
                    queue_type=MessageQueueType.REDIS,
                    connection_config={"host": "localhost"}
                )
    
    @pytest.mark.asyncio
    async def test_redis_cache_adapter_import_error(self):
        """测试Redis缓存适配器导入错误处理"""
        with patch('ai_agent_framework.tools.adapters.redis_adapters.REDIS_AVAILABLE', False):
            with pytest.raises(ImportError, match="Redis适配器需要redis库"):
                CacheTool(
                    cache_type=CacheType.REDIS,
                    connection_config={"host": "localhost"}
                )
    
    @pytest.mark.asyncio
    async def test_redis_queue_adapter_with_mock(self):
        """使用Mock测试Redis队列适配器"""
        # Mock Redis连接
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        mock_redis.lpush.return_value = 1
        mock_redis.brpop.return_value = ("queue:test:priority:2", b'{"message_id": "test", "content": "test message", "priority": 2, "delay_seconds": 0, "ttl_seconds": null, "metadata": {}, "created_at": "2025-08-24T00:00:00", "attempts": 0}')
        mock_redis.llen.return_value = 1
        mock_redis.hset.return_value = 1
        mock_redis.hgetall.return_value = {
            b"name": b"test_queue",
            b"durable": b"true",
            b"max_size": b"-1",
            b"created_at": b"2025-08-24T00:00:00"
        }
        mock_redis.delete.return_value = 1
        mock_redis.zcard.return_value = 0
        mock_redis.close.return_value = None
        
        with patch('redis.asyncio.Redis', return_value=mock_redis):
            # 创建消息队列工具
            mq_tool = MessageQueueTool(
                queue_type=MessageQueueType.REDIS,
                connection_config={"host": "localhost", "port": 6379}
            )
            
            # 测试连接
            result = await mq_tool.execute({"action": "connect"})
            assert result.success
            assert result.result["connected"]
            
            # 测试创建队列
            result = await mq_tool.execute({
                "action": "create_queue",
                "queue_name": "test_queue"
            })
            assert result.success
            assert result.result["created"]
            
            # 测试发送消息
            result = await mq_tool.execute({
                "action": "send",
                "queue_name": "test_queue",
                "message": {
                    "content": "test message",
                    "priority": "normal"
                }
            })
            assert result.success
            assert result.result["sent"]
            
            # 测试接收消息
            result = await mq_tool.execute({
                "action": "receive",
                "queue_name": "test_queue",
                "timeout_seconds": 1
            })
            assert result.success
            assert result.result["received"]
            assert result.result["message"]["content"] == "test message"
            
            # 测试队列信息
            result = await mq_tool.execute({
                "action": "queue_info",
                "queue_name": "test_queue"
            })
            assert result.success
            assert result.result["name"] == "test_queue"
            assert result.result["type"] == "redis"
            
            # 测试断开连接
            result = await mq_tool.execute({"action": "disconnect"})
            assert result.success
            assert result.result["disconnected"]
    
    @pytest.mark.asyncio
    async def test_redis_cache_adapter_with_mock(self):
        """使用Mock测试Redis缓存适配器"""
        # Mock Redis连接
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        mock_redis.get.return_value = b'"test value"'
        mock_redis.set.return_value = True
        mock_redis.setex.return_value = True
        mock_redis.delete.return_value = 1
        mock_redis.exists.return_value = 1
        mock_redis.keys.return_value = [b"cache:test_key"]
        mock_redis.touch.return_value = 1
        mock_redis.info.return_value = {
            "redis_version": "6.2.0",
            "used_memory": 1024,
            "connected_clients": 1,
            "total_commands_processed": 100
        }
        mock_redis.close.return_value = None
        
        with patch('redis.asyncio.Redis', return_value=mock_redis):
            # 创建缓存工具
            cache_tool = CacheTool(
                cache_type=CacheType.REDIS,
                connection_config={"host": "localhost", "port": 6379}
            )
            
            # 测试连接
            result = await cache_tool.execute({"action": "connect"})
            assert result.success
            assert result.result["connected"]
            
            # 测试设置缓存
            result = await cache_tool.execute({
                "action": "set",
                "key": "test_key",
                "value": "test value",
                "ttl_seconds": 3600
            })
            assert result.success
            assert result.result["set"]
            
            # 测试获取缓存
            result = await cache_tool.execute({
                "action": "get",
                "key": "test_key"
            })
            assert result.success
            assert result.result["value"] == "test value"
            assert result.result["found"]
            
            # 测试检查存在
            result = await cache_tool.execute({
                "action": "exists",
                "key": "test_key"
            })
            assert result.success
            assert result.result["exists"]
            
            # 测试获取统计信息
            result = await cache_tool.execute({"action": "stats"})
            assert result.success
            assert result.result["type"] == "redis"
            assert "redis_info" in result.result
            
            # 测试删除缓存
            result = await cache_tool.execute({
                "action": "delete",
                "key": "test_key"
            })
            assert result.success
            assert result.result["deleted"]
            
            # 测试断开连接
            result = await cache_tool.execute({"action": "disconnect"})
            assert result.success
            assert result.result["disconnected"]
    
    @pytest.mark.asyncio
    async def test_redis_connection_failure(self):
        """测试Redis连接失败处理"""
        # Mock Redis连接失败
        mock_redis = AsyncMock()
        mock_redis.ping.side_effect = Exception("Connection failed")
        
        with patch('redis.asyncio.Redis', return_value=mock_redis):
            mq_tool = MessageQueueTool(
                queue_type=MessageQueueType.REDIS,
                connection_config={"host": "invalid_host"}
            )
            
            # 测试连接失败
            result = await mq_tool.execute({"action": "connect"})
            assert not result.success
    
    @pytest.mark.asyncio
    async def test_redis_cluster_mode(self):
        """测试Redis集群模式"""
        mock_redis_cluster = AsyncMock()
        mock_redis_cluster.ping.return_value = True
        mock_redis_cluster.close.return_value = None
        
        with patch('redis.asyncio.RedisCluster', return_value=mock_redis_cluster):
            mq_tool = MessageQueueTool(
                queue_type=MessageQueueType.REDIS,
                connection_config={
                    "host": "localhost",
                    "port": 6379,
                    "cluster_mode": True
                }
            )
            
            # 测试集群模式连接
            result = await mq_tool.execute({"action": "connect"})
            assert result.success
            assert result.result["connected"]
    
    @pytest.mark.asyncio
    async def test_redis_sentinel_mode(self):
        """测试Redis哨兵模式"""
        mock_sentinel = MagicMock()
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        mock_redis.close.return_value = None
        mock_sentinel.master_for.return_value = mock_redis
        
        with patch('redis.asyncio.sentinel.Sentinel', return_value=mock_sentinel):
            cache_tool = CacheTool(
                cache_type=CacheType.REDIS,
                connection_config={
                    "sentinel_hosts": [("localhost", 26379)],
                    "sentinel_service": "mymaster"
                }
            )
            
            # 测试哨兵模式连接
            result = await cache_tool.execute({"action": "connect"})
            assert result.success
            assert result.result["connected"]


if __name__ == "__main__":
    # 运行简单测试
    async def run_basic_tests():
        """运行基础测试"""
        print("开始测试Redis适配器...")
        
        # 测试导入错误处理
        test_instance = TestRedisAdapters()
        
        try:
            await test_instance.test_redis_queue_adapter_import_error()
            print("✅ Redis队列适配器导入错误测试通过")
        except Exception as e:
            print(f"❌ Redis队列适配器导入错误测试失败: {e}")
        
        try:
            await test_instance.test_redis_cache_adapter_import_error()
            print("✅ Redis缓存适配器导入错误测试通过")
        except Exception as e:
            print(f"❌ Redis缓存适配器导入错误测试失败: {e}")
        
        print("Redis适配器基础测试完成！")
    
    asyncio.run(run_basic_tests())
