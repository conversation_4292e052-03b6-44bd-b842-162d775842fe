"""
邮件服务工具测试

测试邮件发送和管理功能。
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.email_tool import EmailTool, EmailConfig, EmailMessage, SMTPProvider


class TestEmailTool(unittest.TestCase):
    """邮件工具测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.email_config = EmailConfig(
            provider_type="smtp",
            smtp_host="smtp.test.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password="password",
            default_sender="<EMAIL>",
            default_sender_name="Test Sender"
        )
        self.email_tool = EmailTool(self.email_config)


class TestEmailMessage(TestEmailTool):
    """邮件消息模型测试"""
    
    def test_email_message_creation(self):
        """测试邮件消息创建"""
        message = EmailMessage(
            to=["<EMAIL>"],
            subject="Test Subject",
            body="Test Body",
            cc=["<EMAIL>"],
            bcc=["<EMAIL>"]
        )
        
        self.assertEqual(message.to, ["<EMAIL>"])
        self.assertEqual(message.subject, "Test Subject")
        self.assertEqual(message.body, "Test Body")
        self.assertEqual(message.cc, ["<EMAIL>"])
        self.assertEqual(message.bcc, ["<EMAIL>"])
        self.assertFalse(message.is_html)
        self.assertEqual(message.priority, "normal")
    
    def test_get_all_recipients(self):
        """测试获取所有收件人"""
        message = EmailMessage(
            to=["<EMAIL>", "<EMAIL>"],
            subject="Test",
            body="Test",
            cc=["<EMAIL>"],
            bcc=["<EMAIL>"]
        )
        
        recipients = message.get_all_recipients()
        expected = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        self.assertEqual(recipients, expected)


class TestSMTPProvider(TestEmailTool):
    """SMTP提供商测试"""
    
    @patch('smtplib.SMTP')
    async def test_smtp_send_email_success(self, mock_smtp):
        """测试SMTP发送邮件成功"""
        # 模拟SMTP服务器
        mock_server = Mock()
        mock_smtp.return_value = mock_server
        
        provider = SMTPProvider(
            host="smtp.test.com",
            port=587,
            username="<EMAIL>",
            password="password"
        )
        
        message = EmailMessage(
            to=["<EMAIL>"],
            subject="Test Subject",
            body="Test Body"
        )
        
        # 测试发送邮件
        result = await provider.send_email(message, "<EMAIL>", "Test Sender")
        
        # 验证结果
        self.assertTrue(result)
        mock_smtp.assert_called_once()
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once_with("<EMAIL>", "password")
        mock_server.send_message.assert_called_once()
        mock_server.quit.assert_called_once()
    
    @patch('smtplib.SMTP')
    async def test_smtp_send_email_failure(self, mock_smtp):
        """测试SMTP发送邮件失败"""
        # 模拟SMTP异常
        mock_smtp.side_effect = Exception("SMTP connection failed")
        
        provider = SMTPProvider(
            host="smtp.test.com",
            port=587,
            username="<EMAIL>",
            password="password"
        )
        
        message = EmailMessage(
            to=["<EMAIL>"],
            subject="Test Subject",
            body="Test Body"
        )
        
        # 测试发送邮件
        result = await provider.send_email(message, "<EMAIL>")
        
        # 验证结果
        self.assertFalse(result)
    
    @patch('smtplib.SMTP')
    async def test_smtp_test_connection(self, mock_smtp):
        """测试SMTP连接测试"""
        # 模拟SMTP服务器
        mock_server = Mock()
        mock_smtp.return_value = mock_server
        
        provider = SMTPProvider(
            host="smtp.test.com",
            port=587,
            username="<EMAIL>",
            password="password"
        )
        
        # 测试连接
        result = await provider.test_connection()
        
        # 验证结果
        self.assertTrue(result)
        mock_smtp.assert_called_once()
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once()
        mock_server.quit.assert_called_once()


class TestEmailToolOperations(TestEmailTool):
    """邮件工具操作测试"""
    
    @patch('ai_agent_framework.tools.email_tool.SMTPProvider')
    async def test_send_email_action(self, mock_provider_class):
        """测试发送邮件操作"""
        # 模拟SMTP提供商
        mock_provider = AsyncMock()
        mock_provider.send_email.return_value = True
        mock_provider_class.return_value = mock_provider
        
        # 重新初始化工具以使用模拟提供商
        email_tool = EmailTool(self.email_config)
        
        # 测试发送邮件
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "Test Subject",
            "body": "Test Body",
            "is_html": False
        })
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertIn("message", result.result)
        self.assertEqual(result.result["recipients"], ["<EMAIL>"])
        mock_provider.send_email.assert_called_once()
    
    @patch('ai_agent_framework.tools.email_tool.SMTPProvider')
    async def test_send_email_with_attachments(self, mock_provider_class):
        """测试发送带附件的邮件"""
        # 模拟SMTP提供商
        mock_provider = AsyncMock()
        mock_provider.send_email.return_value = True
        mock_provider_class.return_value = mock_provider
        
        email_tool = EmailTool(self.email_config)
        
        # 测试发送带附件的邮件
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "Test with Attachment",
            "body": "Test Body",
            "attachments": [
                {
                    "filename": "test.txt",
                    "content": "Test content",
                    "content_type": "text/plain"
                }
            ]
        })
        
        # 验证结果
        self.assertTrue(result.success)
        mock_provider.send_email.assert_called_once()
        
        # 验证传递给提供商的消息包含附件
        call_args = mock_provider.send_email.call_args
        message = call_args[0][0]
        self.assertIsNotNone(message.attachments)
        self.assertEqual(len(message.attachments), 1)
        self.assertEqual(message.attachments[0].filename, "test.txt")
    
    @patch('ai_agent_framework.tools.email_tool.SMTPProvider')
    async def test_test_connection_action(self, mock_provider_class):
        """测试连接测试操作"""
        # 模拟SMTP提供商
        mock_provider = AsyncMock()
        mock_provider.test_connection.return_value = True
        mock_provider_class.return_value = mock_provider
        
        email_tool = EmailTool(self.email_config)
        
        # 测试连接
        result = await email_tool.execute({
            "action": "test_connection"
        })
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertIn("message", result.result)
        self.assertIn("provider", result.result)
        mock_provider.test_connection.assert_called_once()
    
    async def test_validate_email_action(self):
        """测试邮箱验证操作"""
        # 测试有效邮箱
        result = await self.email_tool.execute({
            "action": "validate_email",
            "email": "<EMAIL>"
        })
        
        self.assertTrue(result.success)
        self.assertTrue(result.result["is_valid"])
        self.assertEqual(result.result["email"], "<EMAIL>")
        
        # 测试无效邮箱
        result = await self.email_tool.execute({
            "action": "validate_email",
            "email": "invalid.email"
        })
        
        self.assertTrue(result.success)
        self.assertFalse(result.result["is_valid"])
    
    async def test_invalid_action(self):
        """测试无效操作"""
        result = await self.email_tool.execute({
            "action": "invalid_action"
        })
        
        self.assertFalse(result.success)
        self.assertIn("不支持的操作", result.error)
    
    async def test_missing_action(self):
        """测试缺少操作参数"""
        result = await self.email_tool.execute({})
        
        self.assertFalse(result.success)
        self.assertIn("缺少action参数", result.error)
    
    async def test_send_email_missing_parameters(self):
        """测试发送邮件缺少必需参数"""
        # 缺少to参数
        result = await self.email_tool.execute({
            "action": "send",
            "subject": "Test",
            "body": "Test"
        })
        
        self.assertFalse(result.success)
        self.assertIn("缺少必需参数", result.error)
        
        # 缺少subject参数
        result = await self.email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "body": "Test"
        })
        
        self.assertFalse(result.success)
        self.assertIn("缺少必需参数", result.error)


class TestEmailValidation(TestEmailTool):
    """邮箱验证测试"""
    
    def test_email_format_validation(self):
        """测试邮箱格式验证"""
        # 有效邮箱
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            self.assertTrue(
                self.email_tool._validate_email_format(email),
                f"应该验证为有效: {email}"
            )
        
        # 无效邮箱
        invalid_emails = [
            "invalid.email",
            "@example.com",
            "user@",
            "user@@example.com",
            "user@.com",
            "user@example.",
            ""
        ]
        
        for email in invalid_emails:
            self.assertFalse(
                self.email_tool._validate_email_format(email),
                f"应该验证为无效: {email}"
            )
    
    async def test_argument_validation(self):
        """测试参数验证"""
        # 有效的发送邮件参数
        valid_args = {
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "Test",
            "body": "Test body"
        }
        
        self.assertTrue(await self.email_tool.validate_arguments(valid_args))
        
        # 无效的邮箱地址
        invalid_args = {
            "action": "send",
            "to": ["invalid.email"],
            "subject": "Test",
            "body": "Test body"
        }
        
        self.assertFalse(await self.email_tool.validate_arguments(invalid_args))
        
        # 缺少必需参数
        incomplete_args = {
            "action": "send",
            "subject": "Test"
        }
        
        self.assertFalse(await self.email_tool.validate_arguments(incomplete_args))


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestSMTPProvider, TestEmailToolOperations, TestEmailValidation]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
