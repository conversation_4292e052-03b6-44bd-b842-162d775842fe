"""
OAuth2.0认证工具测试

测试OAuth2.0认证工具的功能。
"""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta

import pytest

from ai_agent_framework.tools import OAuth2Tool, OAuth2Config, OAuth2Token, OAuth2Provider, OAuth2GrantType


class TestOAuth2Tool:
    """OAuth2.0认证工具测试"""
    
    @pytest.mark.asyncio
    async def test_oauth2_tool_import_error(self):
        """测试OAuth2工具导入错误处理"""
        with patch('ai_agent_framework.tools.oauth2_tool.HTTPX_AVAILABLE', False):
            with pytest.raises(ImportError, match="OAuth2工具需要httpx库"):
                OAuth2Tool()
    
    @pytest.mark.asyncio
    async def test_oauth2_config_creation(self):
        """测试OAuth2配置创建"""
        # 测试自定义配置
        config = OAuth2Config(
            client_id="test_client_id",
            client_secret="test_client_secret",
            authorization_url="https://example.com/auth",
            token_url="https://example.com/token",
            redirect_uri="https://example.com/callback",
            scope=["read", "write"]
        )
        
        assert config.client_id == "test_client_id"
        assert config.client_secret == "test_client_secret"
        assert config.scope == ["read", "write"]
        
        # 测试从提供商创建配置
        google_config = OAuth2Config.from_provider(
            provider=OAuth2Provider.GOOGLE,
            client_id="google_client_id",
            client_secret="google_client_secret"
        )
        
        assert google_config.provider == OAuth2Provider.GOOGLE
        assert "accounts.google.com" in google_config.authorization_url
        assert "googleapis.com" in google_config.token_url
    
    @pytest.mark.asyncio
    async def test_oauth2_token_creation(self):
        """测试OAuth2令牌创建"""
        token = OAuth2Token(
            access_token="test_access_token",
            token_type="Bearer",
            expires_in=3600,
            refresh_token="test_refresh_token",
            scope="read write"
        )
        
        assert token.access_token == "test_access_token"
        assert token.token_type == "Bearer"
        assert token.expires_in == 3600
        assert token.refresh_token == "test_refresh_token"
        assert token.scope == "read write"
        assert not token.is_expired()
        assert token.is_valid()
        
        # 测试过期令牌
        expired_token = OAuth2Token(
            access_token="expired_token",
            expires_in=-1  # 已过期
        )
        assert expired_token.is_expired()
        assert not expired_token.is_valid()
    
    @pytest.mark.asyncio
    async def test_oauth2_configure(self):
        """测试OAuth2配置操作"""
        oauth2_tool = OAuth2Tool()
        
        # 测试自定义配置
        result = await oauth2_tool.execute({
            "action": "configure",
            "config": {
                "client_id": "test_client",
                "client_secret": "test_secret",
                "authorization_url": "https://example.com/auth",
                "token_url": "https://example.com/token",
                "provider": "custom"
            }
        })
        
        assert result.success
        assert result.result["configured"]
        assert result.result["provider"] == "custom"
        assert result.result["client_id"] == "test_client"
        
        # 测试提供商配置
        result = await oauth2_tool.execute({
            "action": "configure",
            "provider": "google",
            "client_id": "google_client",
            "client_secret": "google_secret"
        })
        
        assert result.success
        assert result.result["provider"] == "google"
    
    @pytest.mark.asyncio
    async def test_oauth2_get_auth_url(self):
        """测试获取授权URL"""
        oauth2_tool = OAuth2Tool()
        
        # 先配置
        await oauth2_tool.execute({
            "action": "configure",
            "provider": "google",
            "client_id": "test_client",
            "client_secret": "test_secret",
            "redirect_uri": "https://example.com/callback",
            "scope": ["openid", "email"]
        })
        
        # 获取授权URL
        result = await oauth2_tool.execute({
            "action": "get_auth_url"
        })
        
        assert result.success
        assert "auth_url" in result.result
        assert "state" in result.result
        assert "code_verifier" in result.result
        assert "accounts.google.com" in result.result["auth_url"]
        assert "client_id=test_client" in result.result["auth_url"]
    
    @pytest.mark.asyncio
    async def test_oauth2_exchange_code_with_mock(self):
        """使用Mock测试授权码交换"""
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "access_token": "mock_access_token",
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": "mock_refresh_token",
            "scope": "openid email"
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client.aclose.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            oauth2_tool = OAuth2Tool()
            
            # 配置
            await oauth2_tool.execute({
                "action": "configure",
                "provider": "google",
                "client_id": "test_client",
                "client_secret": "test_secret",
                "redirect_uri": "https://example.com/callback"
            })
            
            # 交换授权码
            result = await oauth2_tool.execute({
                "action": "exchange_code",
                "code": "test_auth_code",
                "state": "test_state"
            })
            
            assert result.success
            assert result.result["token_exchanged"]
            assert "token_id" in result.result
            assert result.result["token"]["access_token"] == "mock_access_token"
    
    @pytest.mark.asyncio
    async def test_oauth2_client_credentials_with_mock(self):
        """使用Mock测试客户端凭证流程"""
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "access_token": "client_credentials_token",
            "token_type": "Bearer",
            "expires_in": 7200
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client.aclose.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            oauth2_tool = OAuth2Tool()
            
            # 配置
            await oauth2_tool.execute({
                "action": "configure",
                "provider": "custom",
                "config": {
                    "client_id": "api_client",
                    "client_secret": "api_secret",
                    "authorization_url": "https://api.example.com/auth",
                    "token_url": "https://api.example.com/token",
                    "provider": "custom"
                }
            })
            
            # 客户端凭证流程
            result = await oauth2_tool.execute({
                "action": "client_credentials"
            })
            
            assert result.success
            assert result.result["client_credentials_granted"]
            assert "token_id" in result.result
            assert result.result["token"]["access_token"] == "client_credentials_token"
    
    @pytest.mark.asyncio
    async def test_oauth2_validate_token(self):
        """测试令牌验证"""
        oauth2_tool = OAuth2Tool()
        
        # 测试有效令牌
        result = await oauth2_tool.execute({
            "action": "validate_token",
            "access_token": "valid_token"
        })
        
        assert result.success
        assert "valid" in result.result
        assert "validation_info" in result.result
    
    @pytest.mark.asyncio
    async def test_oauth2_refresh_token_with_mock(self):
        """使用Mock测试令牌刷新"""
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "access_token": "new_access_token",
            "token_type": "Bearer",
            "expires_in": 3600,
            "refresh_token": "new_refresh_token"
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client.aclose.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            oauth2_tool = OAuth2Tool()
            
            # 配置
            await oauth2_tool.execute({
                "action": "configure",
                "provider": "google",
                "client_id": "test_client",
                "client_secret": "test_secret"
            })
            
            # 刷新令牌
            result = await oauth2_tool.execute({
                "action": "refresh_token",
                "refresh_token": "old_refresh_token"
            })
            
            assert result.success
            assert result.result["token_refreshed"]
            assert result.result["token"]["access_token"] == "new_access_token"
    
    @pytest.mark.asyncio
    async def test_oauth2_get_user_info_with_mock(self):
        """使用Mock测试获取用户信息"""
        # Mock HTTP响应
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "id": "123456789",
            "name": "Test User",
            "email": "<EMAIL>",
            "picture": "https://example.com/avatar.jpg"
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response
        mock_client.aclose.return_value = None
        
        with patch('httpx.AsyncClient', return_value=mock_client):
            oauth2_tool = OAuth2Tool()
            
            # 配置
            await oauth2_tool.execute({
                "action": "configure",
                "provider": "google",
                "client_id": "test_client",
                "client_secret": "test_secret"
            })
            
            # 获取用户信息
            result = await oauth2_tool.execute({
                "action": "get_user_info",
                "access_token": "valid_access_token"
            })
            
            assert result.success
            assert result.result["user_info_retrieved"]
            assert result.result["user_info"]["email"] == "<EMAIL>"
            assert result.result["provider"] == "google"
    
    @pytest.mark.asyncio
    async def test_oauth2_argument_validation(self):
        """测试参数验证"""
        oauth2_tool = OAuth2Tool()
        
        # 测试有效参数
        assert await oauth2_tool.validate_arguments({
            "action": "configure",
            "provider": "google"
        })
        
        assert await oauth2_tool.validate_arguments({
            "action": "exchange_code",
            "code": "test_code"
        })
        
        # 测试无效参数
        assert not await oauth2_tool.validate_arguments({
            "action": "invalid_action"
        })
        
        assert not await oauth2_tool.validate_arguments({
            "action": "exchange_code"
            # 缺少code参数
        })
        
        assert not await oauth2_tool.validate_arguments({})  # 缺少action


if __name__ == "__main__":
    # 运行简单测试
    async def run_basic_tests():
        """运行基础测试"""
        print("开始测试OAuth2.0认证工具...")
        
        test_instance = TestOAuth2Tool()
        
        try:
            await test_instance.test_oauth2_config_creation()
            print("✅ OAuth2配置创建测试通过")
        except Exception as e:
            print(f"❌ OAuth2配置创建测试失败: {e}")
        
        try:
            await test_instance.test_oauth2_token_creation()
            print("✅ OAuth2令牌创建测试通过")
        except Exception as e:
            print(f"❌ OAuth2令牌创建测试失败: {e}")
        
        try:
            await test_instance.test_oauth2_configure()
            print("✅ OAuth2配置操作测试通过")
        except Exception as e:
            print(f"❌ OAuth2配置操作测试失败: {e}")
        
        try:
            await test_instance.test_oauth2_validate_token()
            print("✅ OAuth2令牌验证测试通过")
        except Exception as e:
            print(f"❌ OAuth2令牌验证测试失败: {e}")
        
        print("OAuth2.0认证工具基础测试完成！")
    
    asyncio.run(run_basic_tests())
