"""
企业级消息队列适配器测试

测试RabbitMQ和Kafka适配器的功能。
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.message_queue_tool import MessageQueueTool, MessageQueueType, Message


class TestEnterpriseQueueAdapters(unittest.TestCase):
    """企业级消息队列适配器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_message = Message(
            content={"test": "data", "value": 123},
            metadata={"source": "test", "priority": "high"}
        )


class TestRabbitMQAdapter(TestEnterpriseQueueAdapters):
    """RabbitMQ适配器测试"""
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.aio_pika')
    async def test_rabbitmq_adapter_connection(self, mock_aio_pika):
        """测试RabbitMQ适配器连接"""
        # 模拟aio_pika
        mock_connection = AsyncMock()
        mock_channel = AsyncMock()
        mock_exchange = AsyncMock()
        
        mock_aio_pika.connect_robust.return_value = mock_connection
        mock_connection.channel.return_value = mock_channel
        mock_channel.declare_exchange.return_value = mock_exchange
        
        # 创建RabbitMQ消息队列工具
        rabbitmq_config = {
            "connection_url": "amqp://test:test@localhost:5672/",
            "exchange_name": "test_exchange",
            "exchange_type": "direct"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.RABBITMQ,
            connection_config=rabbitmq_config
        )
        
        # 测试连接
        result = await queue_tool.execute({"action": "connect"})
        self.assertTrue(result.success)
        
        # 验证aio_pika调用
        mock_aio_pika.connect_robust.assert_called_once()
        mock_connection.channel.assert_called_once()
        mock_channel.declare_exchange.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.aio_pika')
    async def test_rabbitmq_adapter_send_message(self, mock_aio_pika):
        """测试RabbitMQ适配器发送消息"""
        # 模拟aio_pika
        mock_connection = AsyncMock()
        mock_channel = AsyncMock()
        mock_exchange = AsyncMock()
        mock_queue = AsyncMock()
        
        mock_aio_pika.connect_robust.return_value = mock_connection
        mock_connection.channel.return_value = mock_channel
        mock_channel.declare_exchange.return_value = mock_exchange
        mock_channel.declare_queue.return_value = mock_queue
        
        # 创建RabbitMQ消息队列工具
        rabbitmq_config = {
            "connection_url": "amqp://test:test@localhost:5672/",
            "exchange_name": "test_exchange"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.RABBITMQ,
            connection_config=rabbitmq_config
        )
        
        # 连接
        await queue_tool.execute({"action": "connect"})
        
        # 测试发送消息
        result = await queue_tool.execute({
            "action": "send",
            "queue_name": "test_queue",
            "message": self.test_message.to_dict(),
            "routing_key": "test_key"
        })
        
        self.assertTrue(result.success)
        
        # 验证队列声明和消息发送
        mock_channel.declare_queue.assert_called_once()
        mock_queue.bind.assert_called_once()
        mock_exchange.publish.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.aio_pika')
    async def test_rabbitmq_adapter_receive_message(self, mock_aio_pika):
        """测试RabbitMQ适配器接收消息"""
        # 模拟aio_pika
        mock_connection = AsyncMock()
        mock_channel = AsyncMock()
        mock_exchange = AsyncMock()
        mock_queue = AsyncMock()
        mock_incoming_message = AsyncMock()
        
        # 模拟接收到的消息
        import json
        message_body = json.dumps({
            "id": self.test_message.id,
            "content": self.test_message.content,
            "metadata": self.test_message.metadata,
            "timestamp": self.test_message.timestamp.isoformat()
        }).encode('utf-8')
        
        mock_incoming_message.body = message_body
        mock_queue.get.return_value = mock_incoming_message
        
        mock_aio_pika.connect_robust.return_value = mock_connection
        mock_connection.channel.return_value = mock_channel
        mock_channel.declare_exchange.return_value = mock_exchange
        mock_channel.declare_queue.return_value = mock_queue
        
        # 创建RabbitMQ消息队列工具
        rabbitmq_config = {
            "connection_url": "amqp://test:test@localhost:5672/",
            "exchange_name": "test_exchange"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.RABBITMQ,
            connection_config=rabbitmq_config
        )
        
        # 连接
        await queue_tool.execute({"action": "connect"})
        
        # 测试接收消息
        result = await queue_tool.execute({
            "action": "receive",
            "queue_name": "test_queue",
            "timeout": 10
        })
        
        self.assertTrue(result.success)
        self.assertIsNotNone(result.result["message"])
        
        # 验证消息确认
        mock_incoming_message.ack.assert_called_once()


class TestKafkaAdapter(TestEnterpriseQueueAdapters):
    """Kafka适配器测试"""
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaProducer')
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaConsumer')
    async def test_kafka_adapter_connection(self, mock_consumer_class, mock_producer_class):
        """测试Kafka适配器连接"""
        # 模拟Kafka
        mock_producer = AsyncMock()
        mock_consumer = AsyncMock()
        
        mock_producer_class.return_value = mock_producer
        mock_consumer_class.return_value = mock_consumer
        
        # 创建Kafka消息队列工具
        kafka_config = {
            "bootstrap_servers": "localhost:9092",
            "client_id": "test_client",
            "group_id": "test_group"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.KAFKA,
            connection_config=kafka_config
        )
        
        # 测试连接
        result = await queue_tool.execute({"action": "connect"})
        self.assertTrue(result.success)
        
        # 验证Kafka客户端启动
        mock_producer.start.assert_called_once()
        mock_consumer.start.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaProducer')
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaConsumer')
    async def test_kafka_adapter_send_message(self, mock_consumer_class, mock_producer_class):
        """测试Kafka适配器发送消息"""
        # 模拟Kafka
        mock_producer = AsyncMock()
        mock_consumer = AsyncMock()
        mock_future = AsyncMock()
        mock_record_metadata = Mock()
        mock_record_metadata.partition = 0
        mock_record_metadata.offset = 123
        
        mock_producer_class.return_value = mock_producer
        mock_consumer_class.return_value = mock_consumer
        mock_producer.send.return_value = mock_future
        mock_future.__await__ = AsyncMock(return_value=mock_record_metadata)
        
        # 创建Kafka消息队列工具
        kafka_config = {
            "bootstrap_servers": "localhost:9092",
            "client_id": "test_client",
            "group_id": "test_group"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.KAFKA,
            connection_config=kafka_config
        )
        
        # 连接
        await queue_tool.execute({"action": "connect"})
        
        # 测试发送消息
        result = await queue_tool.execute({
            "action": "send",
            "queue_name": "test_topic",
            "message": self.test_message.to_dict(),
            "routing_key": "test_key"
        })
        
        self.assertTrue(result.success)
        
        # 验证消息发送
        mock_producer.send.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaProducer')
    @patch('ai_agent_framework.tools.adapters.enterprise_queue_adapters.AIOKafkaConsumer')
    async def test_kafka_adapter_receive_message(self, mock_consumer_class, mock_producer_class):
        """测试Kafka适配器接收消息"""
        # 模�拟Kafka
        mock_producer = AsyncMock()
        mock_consumer = AsyncMock()
        mock_kafka_message = Mock()
        
        # 模拟接收到的消息
        message_data = {
            "id": self.test_message.id,
            "content": self.test_message.content,
            "metadata": self.test_message.metadata,
            "timestamp": self.test_message.timestamp.isoformat()
        }
        
        mock_kafka_message.value = message_data
        mock_consumer.getmany.return_value = {
            Mock(): [mock_kafka_message]
        }
        
        mock_producer_class.return_value = mock_producer
        mock_consumer_class.return_value = mock_consumer
        
        # 创建Kafka消息队列工具
        kafka_config = {
            "bootstrap_servers": "localhost:9092",
            "client_id": "test_client",
            "group_id": "test_group"
        }
        
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.KAFKA,
            connection_config=kafka_config
        )
        
        # 连接
        await queue_tool.execute({"action": "connect"})
        
        # 测试接收消息
        result = await queue_tool.execute({
            "action": "receive",
            "queue_name": "test_topic",
            "timeout": 10
        })
        
        self.assertTrue(result.success)
        self.assertIsNotNone(result.result["message"])
        
        # 验证消费者订阅
        mock_consumer.subscribe.assert_called_once()


class TestEnterpriseQueueIntegration(TestEnterpriseQueueAdapters):
    """企业级消息队列集成测试"""
    
    def test_queue_type_validation(self):
        """测试队列类型验证"""
        # 测试无效队列类型
        with self.assertRaises(ValueError):
            MessageQueueTool(
                queue_type="invalid_type",
                connection_config={}
            )
    
    def test_missing_dependencies(self):
        """测试缺失依赖的处理"""
        # 这个测试需要在没有安装相应依赖的环境中运行
        # 在CI/CD环境中可以通过mock来模拟ImportError
        pass
    
    async def test_adapter_error_handling(self):
        """测试适配器错误处理"""
        # 测试连接失败的情况
        queue_tool = MessageQueueTool(
            queue_type=MessageQueueType.MEMORY,
            connection_config={}
        )
        
        # 测试在未连接状态下执行操作
        result = await queue_tool.execute({
            "action": "send",
            "queue_name": "test_queue",
            "message": self.test_message.to_dict()
        })
        
        # 内存队列应该能正常工作
        self.assertTrue(result.success)


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestRabbitMQAdapter, TestKafkaAdapter, TestEnterpriseQueueIntegration]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
