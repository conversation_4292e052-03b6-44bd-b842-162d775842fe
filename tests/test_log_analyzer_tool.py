"""
日志分析和告警工具测试

测试智能日志分析、模式识别和告警功能。
"""

import asyncio
import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.log_analyzer_tool import (
    LogAnalyzerTool, LogAnalyzerConfig, LogAnalyzer, AlertManager,
    LogEntry, LogLevel, AlertRule, AlertSeverity, LogPattern
)


class TestLogAnalyzerTool(unittest.TestCase):
    """日志分析工具测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer_config = LogAnalyzerConfig(
            buffer_size=1000,
            enable_anomaly_detection=True,
            enable_pattern_matching=True
        )
        self.log_analyzer = LogAnalyzerTool(self.analyzer_config)


class TestLogEntry(TestLogAnalyzerTool):
    """日志条目模型测试"""
    
    def test_log_entry_creation(self):
        """测试日志条目创建"""
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=LogLevel.ERROR,
            message="Test error message",
            source="test_app",
            component="test_component",
            metadata={"key": "value"}
        )
        
        self.assertEqual(log_entry.level, LogLevel.ERROR)
        self.assertEqual(log_entry.message, "Test error message")
        self.assertEqual(log_entry.source, "test_app")
        self.assertEqual(log_entry.component, "test_component")
        self.assertIsNotNone(log_entry.id)
    
    def test_log_entry_to_dict(self):
        """测试日志条目转换为字典"""
        timestamp = datetime.now()
        log_entry = LogEntry(
            timestamp=timestamp,
            level=LogLevel.INFO,
            message="Test message",
            source="test_source",
            component="test_component"
        )
        
        log_dict = log_entry.to_dict()
        
        self.assertEqual(log_dict["level"], "INFO")
        self.assertEqual(log_dict["message"], "Test message")
        self.assertEqual(log_dict["timestamp"], timestamp.isoformat())
        self.assertIn("id", log_dict)


class TestLogPattern(TestLogAnalyzerTool):
    """日志模式测试"""
    
    def test_pattern_creation(self):
        """测试模式创建"""
        pattern = LogPattern(
            name="数据库错误",
            pattern=r"database.*error",
            category="database",
            severity=LogLevel.ERROR
        )
        
        self.assertEqual(pattern.name, "数据库错误")
        self.assertEqual(pattern.category, "database")
        self.assertEqual(pattern.severity, LogLevel.ERROR)
        self.assertIsNotNone(pattern.regex)
    
    def test_pattern_matching(self):
        """测试模式匹配"""
        pattern = LogPattern(
            name="连接错误",
            pattern=r"connection.*failed",
            category="network"
        )
        
        # 测试匹配
        self.assertTrue(pattern.matches("Database connection failed"))
        self.assertTrue(pattern.matches("Connection to server failed"))
        
        # 测试不匹配
        self.assertFalse(pattern.matches("Connection successful"))
        self.assertFalse(pattern.matches("User login completed"))
    
    def test_pattern_case_insensitive(self):
        """测试模式大小写不敏感匹配"""
        pattern = LogPattern(
            name="错误模式",
            pattern="ERROR",
            category="general"
        )
        
        self.assertTrue(pattern.matches("Error occurred"))
        self.assertTrue(pattern.matches("error in processing"))
        self.assertTrue(pattern.matches("SYSTEM ERROR"))


class TestLogAnalyzer(TestLogAnalyzerTool):
    """日志分析器测试"""
    
    def test_analyzer_initialization(self):
        """测试分析器初始化"""
        analyzer = LogAnalyzer()
        
        self.assertEqual(len(analyzer._patterns), 0)
        self.assertEqual(len(analyzer._log_buffer), 0)
        self.assertIsInstance(analyzer._error_counts, dict)
    
    def test_add_remove_pattern(self):
        """测试添加和移除模式"""
        analyzer = LogAnalyzer()
        
        pattern = LogPattern(
            name="测试模式",
            pattern="test.*error",
            category="test"
        )
        
        # 添加模式
        analyzer.add_pattern(pattern)
        self.assertIn(pattern.id, analyzer._patterns)
        
        # 移除模式
        success = analyzer.remove_pattern(pattern.id)
        self.assertTrue(success)
        self.assertNotIn(pattern.id, analyzer._patterns)
        
        # 移除不存在的模式
        success = analyzer.remove_pattern("nonexistent")
        self.assertFalse(success)
    
    def test_analyze_log_basic(self):
        """测试基础日志分析"""
        analyzer = LogAnalyzer()
        
        # 添加测试模式
        pattern = LogPattern(
            name="错误模式",
            pattern="error",
            category="general"
        )
        analyzer.add_pattern(pattern)
        
        # 创建测试日志
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=LogLevel.ERROR,
            message="System error occurred",
            component="test_component"
        )
        
        # 分析日志
        result = analyzer.analyze_log(log_entry)
        
        self.assertEqual(result["log_id"], log_entry.id)
        self.assertEqual(result["level"], LogLevel.ERROR)
        self.assertEqual(len(result["matched_patterns"]), 1)
        self.assertEqual(result["matched_patterns"][0]["pattern_name"], "错误模式")
    
    def test_performance_metrics_extraction(self):
        """测试性能指标提取"""
        analyzer = LogAnalyzer()
        
        # 测试响应时间提取
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=LogLevel.INFO,
            message="Request completed in 250ms",
            component="api_server"
        )
        
        result = analyzer.analyze_log(log_entry)
        
        # 检查是否提取到响应时间
        if "response_time" in result["metrics"]:
            self.assertEqual(result["metrics"]["response_time"], 250.0)
    
    def test_error_counting(self):
        """测试错误计数"""
        analyzer = LogAnalyzer()
        
        # 添加多个错误日志
        for i in range(5):
            log_entry = LogEntry(
                timestamp=datetime.now(),
                level=LogLevel.ERROR,
                message=f"Error {i}",
                component="test_component"
            )
            analyzer.analyze_log(log_entry)
        
        # 检查错误计数
        self.assertEqual(analyzer._error_counts["test_component"], 5)
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        analyzer = LogAnalyzer()
        
        # 添加一些测试日志
        for level in [LogLevel.INFO, LogLevel.WARNING, LogLevel.ERROR]:
            log_entry = LogEntry(
                timestamp=datetime.now(),
                level=level,
                message=f"Test {level.value} message",
                component="test_component"
            )
            analyzer.analyze_log(log_entry)
        
        # 获取统计信息
        stats = analyzer.get_statistics()
        
        self.assertEqual(stats["total_logs"], 3)
        self.assertIn("level_distribution", stats)
        self.assertIn("component_distribution", stats)
        self.assertEqual(stats["level_distribution"]["INFO"], 1)
        self.assertEqual(stats["level_distribution"]["WARNING"], 1)
        self.assertEqual(stats["level_distribution"]["ERROR"], 1)


class TestAlertManager(TestLogAnalyzerTool):
    """告警管理器测试"""
    
    def test_alert_manager_initialization(self):
        """测试告警管理器初始化"""
        alert_manager = AlertManager()
        
        self.assertEqual(len(alert_manager._rules), 0)
        self.assertEqual(len(alert_manager._alerts), 0)
    
    def test_add_remove_rule(self):
        """测试添加和移除规则"""
        alert_manager = AlertManager()
        
        rule = AlertRule(
            name="测试规则",
            description="测试告警规则",
            severity=AlertSeverity.HIGH,
            conditions={"log_level": ["ERROR"]},
            actions=[{"type": "log"}]
        )
        
        # 添加规则
        alert_manager.add_rule(rule)
        self.assertIn(rule.id, alert_manager._rules)
        self.assertIn(rule.id, alert_manager._rule_states)
        
        # 移除规则
        success = alert_manager.remove_rule(rule.id)
        self.assertTrue(success)
        self.assertNotIn(rule.id, alert_manager._rules)
    
    def test_rule_evaluation(self):
        """测试规则评估"""
        alert_manager = AlertManager()
        
        # 添加测试规则
        rule = AlertRule(
            name="错误告警",
            description="检测错误日志",
            severity=AlertSeverity.HIGH,
            conditions={"log_level": ["ERROR"]},
            actions=[{"type": "log"}]
        )
        alert_manager.add_rule(rule)
        
        # 创建测试日志和分析结果
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=LogLevel.ERROR,
            message="Test error",
            component="test"
        )
        
        analysis_result = {
            "log_id": log_entry.id,
            "level": LogLevel.ERROR,
            "matched_patterns": [],
            "anomalies": [],
            "metrics": {}
        }
        
        # 评估规则
        triggered_alerts = alert_manager.evaluate_rules(analysis_result, log_entry)
        
        self.assertEqual(len(triggered_alerts), 1)
        self.assertEqual(triggered_alerts[0].rule_name, "错误告警")
        self.assertEqual(triggered_alerts[0].severity, AlertSeverity.HIGH)
    
    def test_alert_acknowledgment(self):
        """测试告警确认"""
        alert_manager = AlertManager()
        
        # 创建测试告警
        rule = AlertRule(
            name="测试规则",
            description="测试",
            severity=AlertSeverity.MEDIUM,
            conditions={"log_level": ["WARNING"]},
            actions=[]
        )
        alert_manager.add_rule(rule)
        
        log_entry = LogEntry(
            timestamp=datetime.now(),
            level=LogLevel.WARNING,
            message="Test warning",
            component="test"
        )
        
        analysis_result = {
            "log_id": log_entry.id,
            "level": LogLevel.WARNING,
            "matched_patterns": [],
            "anomalies": [],
            "metrics": {}
        }
        
        alerts = alert_manager.evaluate_rules(analysis_result, log_entry)
        alert = alerts[0]
        
        # 确认告警
        success = alert_manager.acknowledge_alert(alert.id, "test_user")
        self.assertTrue(success)
        
        # 检查告警状态
        stored_alert = alert_manager._alerts[alert.id]
        self.assertEqual(stored_alert.status.value, "acknowledged")
        self.assertEqual(stored_alert.acknowledged_by, "test_user")
        self.assertIsNotNone(stored_alert.acknowledged_at)


class TestLogAnalyzerToolOperations(TestLogAnalyzerTool):
    """日志分析工具操作测试"""
    
    async def test_analyze_log_action(self):
        """测试分析日志操作"""
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "level": "ERROR",
            "message": "Database connection failed",
            "component": "database",
            "source": "api_server"
        }
        
        result = await self.log_analyzer.execute({
            "action": "analyze_log",
            "log_data": log_data
        })
        
        self.assertTrue(result.success)
        self.assertIn("analysis", result.result)
        self.assertIn("triggered_alerts", result.result)
    
    async def test_analyze_log_missing_params(self):
        """测试分析日志缺少参数"""
        result = await self.log_analyzer.execute({
            "action": "analyze_log",
            "log_data": {
                "level": "ERROR"
                # 缺少必需参数
            }
        })
        
        self.assertFalse(result.success)
        self.assertIn("缺少必需参数", result.error)
    
    async def test_add_pattern_action(self):
        """测试添加模式操作"""
        pattern_config = {
            "name": "测试模式",
            "pattern": "test.*error",
            "description": "测试用的错误模式",
            "category": "test",
            "severity": "ERROR"
        }
        
        result = await self.log_analyzer.execute({
            "action": "add_pattern",
            "pattern_config": pattern_config
        })
        
        self.assertTrue(result.success)
        self.assertIn("pattern_id", result.result)
        self.assertIn("添加成功", result.result["message"])
    
    async def test_list_patterns_action(self):
        """测试列出模式操作"""
        result = await self.log_analyzer.execute({
            "action": "list_patterns"
        })
        
        self.assertTrue(result.success)
        self.assertIn("patterns", result.result)
        self.assertIn("count", result.result)
        self.assertGreater(result.result["count"], 0)  # 应该有默认模式
    
    async def test_get_statistics_action(self):
        """测试获取统计操作"""
        # 先分析一些日志
        log_data = {
            "timestamp": datetime.now().isoformat(),
            "level": "INFO",
            "message": "Test log message",
            "component": "test"
        }
        
        await self.log_analyzer.execute({
            "action": "analyze_log",
            "log_data": log_data
        })
        
        # 获取统计
        result = await self.log_analyzer.execute({
            "action": "get_statistics",
            "time_window": 3600
        })
        
        self.assertTrue(result.success)
        self.assertIn("total_logs", result.result)
        self.assertIn("level_distribution", result.result)
    
    async def test_get_alerts_action(self):
        """测试获取告警操作"""
        result = await self.log_analyzer.execute({
            "action": "get_alerts"
        })
        
        self.assertTrue(result.success)
        self.assertIn("alerts", result.result)
        self.assertIn("count", result.result)
    
    async def test_invalid_action(self):
        """测试无效操作"""
        result = await self.log_analyzer.execute({
            "action": "invalid_action"
        })
        
        self.assertFalse(result.success)
        self.assertIn("不支持的操作", result.error)
    
    async def test_missing_action(self):
        """测试缺少操作参数"""
        result = await self.log_analyzer.execute({})
        
        self.assertFalse(result.success)
        self.assertIn("缺少action参数", result.error)


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestLogAnalyzerToolOperations]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
