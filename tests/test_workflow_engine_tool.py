"""
工作流引擎工具测试

测试工作流编排和执行引擎功能。
"""

import asyncio
import unittest
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.workflow_engine_tool import (
    WorkflowEngineTool, WorkflowEngineConfig, WorkflowEngine, Workflow,
    WorkflowTask, TaskType, TaskStatus, WorkflowStatus,
    HttpRequestExecutor, ScriptExecutor, FunctionExecutor, ConditionExecutor, DelayExecutor
)


class TestWorkflowEngineTool(unittest.TestCase):
    """工作流引擎工具测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.engine_config = WorkflowEngineConfig(
            max_concurrent_workflows=3,
            default_task_timeout=30
        )
        self.workflow_engine = WorkflowEngineTool(self.engine_config)


class TestWorkflowTask(TestWorkflowEngineTool):
    """工作流任务模型测试"""
    
    def test_task_creation(self):
        """测试任务创建"""
        task = WorkflowTask(
            name="测试任务",
            description="这是一个测试任务",
            task_type=TaskType.HTTP_REQUEST,
            config={"url": "https://example.com"},
            dependencies=["task1", "task2"],
            max_retries=5
        )
        
        self.assertEqual(task.name, "测试任务")
        self.assertEqual(task.task_type, TaskType.HTTP_REQUEST)
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertEqual(task.max_retries, 5)
        self.assertEqual(task.dependencies, ["task1", "task2"])
        self.assertIsNotNone(task.id)
    
    def test_task_lifecycle(self):
        """测试任务生命周期"""
        task = WorkflowTask(
            name="生命周期测试",
            task_type=TaskType.DELAY,
            config={"seconds": 1}
        )
        
        # 开始任务
        task.start()
        self.assertEqual(task.status, TaskStatus.RUNNING)
        self.assertIsNotNone(task.started_at)
        
        # 完成任务
        result = {"message": "任务完成"}
        task.complete(result)
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        self.assertEqual(task.result, result)
        self.assertIsNotNone(task.completed_at)
    
    def test_task_failure(self):
        """测试任务失败"""
        task = WorkflowTask(
            name="失败测试",
            task_type=TaskType.SCRIPT,
            config={"script": "exit 1"}
        )
        
        task.start()
        task.fail("脚本执行失败")
        
        self.assertEqual(task.status, TaskStatus.FAILED)
        self.assertEqual(task.error, "脚本执行失败")
        self.assertIsNotNone(task.completed_at)
    
    def test_task_skip(self):
        """测试任务跳过"""
        task = WorkflowTask(
            name="跳过测试",
            task_type=TaskType.CONDITION,
            config={"condition": "false"}
        )
        
        task.skip()
        self.assertEqual(task.status, TaskStatus.SKIPPED)
        self.assertIsNotNone(task.completed_at)


class TestWorkflow(TestWorkflowEngineTool):
    """工作流模型测试"""
    
    def test_workflow_creation(self):
        """测试工作流创建"""
        workflow = Workflow(
            name="测试工作流",
            description="这是一个测试工作流",
            version="2.0",
            variables={"key": "value"}
        )
        
        self.assertEqual(workflow.name, "测试工作流")
        self.assertEqual(workflow.version, "2.0")
        self.assertEqual(workflow.status, WorkflowStatus.PENDING)
        self.assertEqual(workflow.variables["key"], "value")
        self.assertIsNotNone(workflow.id)
    
    def test_workflow_task_management(self):
        """测试工作流任务管理"""
        workflow = Workflow(name="任务管理测试")
        
        # 添加任务
        task1 = WorkflowTask(name="任务1", task_type=TaskType.DELAY, config={})
        task2 = WorkflowTask(name="任务2", task_type=TaskType.DELAY, config={})
        
        workflow.add_task(task1)
        workflow.add_task(task2)
        
        self.assertEqual(len(workflow.tasks), 2)
        
        # 获取任务
        retrieved_task = workflow.get_task(task1.id)
        self.assertEqual(retrieved_task.id, task1.id)
        
        # 获取不存在的任务
        nonexistent_task = workflow.get_task("nonexistent")
        self.assertIsNone(nonexistent_task)
    
    def test_workflow_ready_tasks(self):
        """测试获取准备执行的任务"""
        workflow = Workflow(name="依赖测试")
        
        # 创建有依赖关系的任务
        task1 = WorkflowTask(name="任务1", task_type=TaskType.DELAY, config={})
        task2 = WorkflowTask(name="任务2", task_type=TaskType.DELAY, config={}, dependencies=[task1.id])
        task3 = WorkflowTask(name="任务3", task_type=TaskType.DELAY, config={}, dependencies=[task1.id, task2.id])
        
        workflow.add_task(task1)
        workflow.add_task(task2)
        workflow.add_task(task3)
        
        # 初始状态：只有task1准备好
        ready_tasks = workflow.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, task1.id)
        
        # task1完成后：task2准备好
        task1.complete()
        ready_tasks = workflow.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, task2.id)
        
        # task2完成后：task3准备好
        task2.complete()
        ready_tasks = workflow.get_ready_tasks()
        self.assertEqual(len(ready_tasks), 1)
        self.assertEqual(ready_tasks[0].id, task3.id)
    
    def test_workflow_completion_check(self):
        """测试工作流完成检查"""
        workflow = Workflow(name="完成检查测试")
        
        task1 = WorkflowTask(name="任务1", task_type=TaskType.DELAY, config={})
        task2 = WorkflowTask(name="任务2", task_type=TaskType.DELAY, config={})
        
        workflow.add_task(task1)
        workflow.add_task(task2)
        
        # 初始状态：未完成
        self.assertFalse(workflow.is_completed())
        
        # 一个任务完成：仍未完成
        task1.complete()
        self.assertFalse(workflow.is_completed())
        
        # 所有任务完成：已完成
        task2.complete()
        self.assertTrue(workflow.is_completed())
    
    def test_workflow_failure_check(self):
        """测试工作流失败检查"""
        workflow = Workflow(name="失败检查测试")
        
        task1 = WorkflowTask(name="任务1", task_type=TaskType.DELAY, config={})
        task2 = WorkflowTask(name="任务2", task_type=TaskType.DELAY, config={})
        
        workflow.add_task(task1)
        workflow.add_task(task2)
        
        # 初始状态：无失败
        self.assertFalse(workflow.has_failed_tasks())
        
        # 一个任务失败：有失败
        task1.fail("测试失败")
        self.assertTrue(workflow.has_failed_tasks())


class TestTaskExecutors(TestWorkflowEngineTool):
    """任务执行器测试"""
    
    async def test_delay_executor(self):
        """测试延迟执行器"""
        executor = DelayExecutor()
        
        task = WorkflowTask(
            name="延迟任务",
            task_type=TaskType.DELAY,
            config={"seconds": 0.1}  # 短延迟用于测试
        )
        
        context = {"variables": {}}
        
        start_time = datetime.now()
        result = await executor.execute(task, context)
        end_time = datetime.now()
        
        elapsed = (end_time - start_time).total_seconds()
        
        self.assertGreaterEqual(elapsed, 0.1)
        self.assertEqual(result["delayed_seconds"], 0.1)
    
    async def test_condition_executor(self):
        """测试条件执行器"""
        executor = ConditionExecutor()
        
        # 测试真条件
        task_true = WorkflowTask(
            name="真条件",
            task_type=TaskType.CONDITION,
            config={"condition": "10 > 5"}
        )
        
        context = {"variables": {}}
        result = await executor.execute(task_true, context)
        self.assertTrue(result["condition_result"])
        
        # 测试假条件
        task_false = WorkflowTask(
            name="假条件",
            task_type=TaskType.CONDITION,
            config={"condition": "5 > 10"}
        )
        
        result = await executor.execute(task_false, context)
        self.assertFalse(result["condition_result"])
        
        # 测试变量替换
        task_var = WorkflowTask(
            name="变量条件",
            task_type=TaskType.CONDITION,
            config={"condition": "${value} > 50"}
        )
        
        context_with_var = {"variables": {"value": 75}}
        result = await executor.execute(task_var, context_with_var)
        self.assertTrue(result["condition_result"])


class TestWorkflowEngine(TestWorkflowEngineTool):
    """工作流引擎测试"""
    
    def test_engine_initialization(self):
        """测试引擎初始化"""
        engine = WorkflowEngine()
        
        self.assertEqual(len(engine._workflows), 0)
        self.assertEqual(len(engine._running_workflows), 0)
        self.assertIn(TaskType.HTTP_REQUEST, engine._executors)
        self.assertIn(TaskType.SCRIPT, engine._executors)
        self.assertIn(TaskType.DELAY, engine._executors)
    
    def test_workflow_creation(self):
        """测试工作流创建"""
        engine = WorkflowEngine()
        
        workflow = Workflow(name="测试工作流")
        workflow_id = engine.create_workflow(workflow)
        
        self.assertEqual(workflow_id, workflow.id)
        self.assertIn(workflow_id, engine._workflows)
        
        retrieved_workflow = engine.get_workflow(workflow_id)
        self.assertEqual(retrieved_workflow.id, workflow_id)
    
    def test_workflow_listing(self):
        """测试工作流列表"""
        engine = WorkflowEngine()
        
        # 创建不同状态的工作流
        workflow1 = Workflow(name="工作流1")
        workflow1.status = WorkflowStatus.PENDING
        
        workflow2 = Workflow(name="工作流2")
        workflow2.status = WorkflowStatus.COMPLETED
        
        engine.create_workflow(workflow1)
        engine.create_workflow(workflow2)
        
        # 列出所有工作流
        all_workflows = engine.list_workflows()
        self.assertEqual(len(all_workflows), 2)
        
        # 按状态过滤
        pending_workflows = engine.list_workflows(WorkflowStatus.PENDING)
        self.assertEqual(len(pending_workflows), 1)
        self.assertEqual(pending_workflows[0].name, "工作流1")
        
        completed_workflows = engine.list_workflows(WorkflowStatus.COMPLETED)
        self.assertEqual(len(completed_workflows), 1)
        self.assertEqual(completed_workflows[0].name, "工作流2")


class TestWorkflowEngineToolOperations(TestWorkflowEngineTool):
    """工作流引擎工具操作测试"""
    
    async def test_create_workflow_action(self):
        """测试创建工作流操作"""
        workflow_config = {
            "name": "测试工作流",
            "description": "这是一个测试工作流",
            "version": "1.0",
            "variables": {"test_var": "test_value"},
            "tasks": [
                {
                    "name": "测试任务",
                    "description": "测试任务描述",
                    "task_type": "delay",
                    "config": {"seconds": 1},
                    "dependencies": [],
                    "max_retries": 2
                }
            ]
        }
        
        result = await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config,
            "user_id": "test_user"
        })
        
        self.assertTrue(result.success)
        self.assertIn("workflow_id", result.result)
        self.assertIn("message", result.result)
        self.assertEqual(result.result["task_count"], 1)
    
    async def test_create_workflow_missing_name(self):
        """测试创建工作流缺少名称"""
        workflow_config = {
            "description": "缺少名称的工作流"
        }
        
        result = await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config
        })
        
        self.assertFalse(result.success)
        self.assertIn("缺少必需参数: name", result.error)
    
    async def test_list_workflows_action(self):
        """测试列出工作流操作"""
        # 先创建一个工作流
        workflow_config = {
            "name": "列表测试工作流",
            "tasks": [
                {
                    "name": "测试任务",
                    "task_type": "delay",
                    "config": {"seconds": 1}
                }
            ]
        }
        
        await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config
        })
        
        # 列出工作流
        result = await self.workflow_engine.execute({
            "action": "list_workflows"
        })
        
        self.assertTrue(result.success)
        self.assertIn("workflows", result.result)
        self.assertIn("count", result.result)
        self.assertGreater(result.result["count"], 0)
    
    async def test_get_workflow_action(self):
        """测试获取工作流操作"""
        # 先创建一个工作流
        create_result = await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": {
                "name": "获取测试工作流",
                "tasks": []
            }
        })
        
        workflow_id = create_result.result["workflow_id"]
        
        # 获取工作流
        result = await self.workflow_engine.execute({
            "action": "get_workflow",
            "workflow_id": workflow_id
        })
        
        self.assertTrue(result.success)
        self.assertIn("workflow", result.result)
        self.assertEqual(result.result["workflow"]["id"], workflow_id)
        self.assertEqual(result.result["workflow"]["name"], "获取测试工作流")
    
    async def test_start_workflow_action(self):
        """测试启动工作流操作"""
        # 创建工作流
        create_result = await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": {
                "name": "启动测试工作流",
                "tasks": [
                    {
                        "name": "简单任务",
                        "task_type": "delay",
                        "config": {"seconds": 0.1}
                    }
                ]
            }
        })
        
        workflow_id = create_result.result["workflow_id"]
        
        # 启动工作流
        result = await self.workflow_engine.execute({
            "action": "start_workflow",
            "workflow_id": workflow_id,
            "variables": {"test_var": "test_value"}
        })
        
        self.assertTrue(result.success)
        self.assertIn("启动成功", result.result["message"])
    
    async def test_get_workflow_status_action(self):
        """测试获取工作流状态操作"""
        # 创建工作流
        create_result = await self.workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": {
                "name": "状态测试工作流",
                "tasks": [
                    {
                        "name": "状态任务",
                        "task_type": "delay",
                        "config": {"seconds": 0.1}
                    }
                ]
            }
        })
        
        workflow_id = create_result.result["workflow_id"]
        
        # 获取状态
        result = await self.workflow_engine.execute({
            "action": "get_workflow_status",
            "workflow_id": workflow_id
        })
        
        self.assertTrue(result.success)
        self.assertIn("workflow_status", result.result)
        self.assertIn("task_status_counts", result.result)
        self.assertIn("total_tasks", result.result)
    
    async def test_invalid_action(self):
        """测试无效操作"""
        result = await self.workflow_engine.execute({
            "action": "invalid_action"
        })
        
        self.assertFalse(result.success)
        self.assertIn("不支持的操作", result.error)
    
    async def test_missing_action(self):
        """测试缺少操作参数"""
        result = await self.workflow_engine.execute({})
        
        self.assertFalse(result.success)
        self.assertIn("缺少action参数", result.error)


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestTaskExecutors, TestWorkflowEngineToolOperations]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
