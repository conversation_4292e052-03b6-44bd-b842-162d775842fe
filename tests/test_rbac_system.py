"""
RBAC权限控制系统测试

测试基于角色的访问控制功能。
"""

import asyncio
import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from ai_agent_framework.security.rbac import (
    RBACManager, MemoryRBACStore, RBACUser, Role, Permission,
    AccessRequest, AccessResult, ResourceType, PermissionType
)
from ai_agent_framework.tools.rbac_tool import RB<PERSON>Tool, RBACToolConfig


class TestRBACSystem(unittest.TestCase):
    """RBAC系统测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.store = MemoryRBACStore()
        self.rbac_manager = RBACManager(self.store)
        self.rbac_tool = RBACTool()


class TestRBACModels(TestRBACSystem):
    """RBAC模型测试"""
    
    def test_permission_model(self):
        """测试权限模型"""
        permission = Permission(
            id="test_read",
            name="测试读取",
            description="测试读取权限",
            resource_type=ResourceType.DATA,
            permission_type=PermissionType.READ,
            resource_pattern="test:*"
        )
        
        # 测试资源匹配
        self.assertTrue(permission.matches_resource("test:data1", ResourceType.DATA))
        self.assertTrue(permission.matches_resource("test:data2", ResourceType.DATA))
        self.assertFalse(permission.matches_resource("prod:data1", ResourceType.DATA))
        self.assertFalse(permission.matches_resource("test:data1", ResourceType.USER))
    
    def test_role_model(self):
        """测试角色模型"""
        role = Role(
            id="test_role",
            name="测试角色",
            description="测试角色描述",
            permissions=["perm1", "perm2"]
        )
        
        # 测试添加权限
        role.add_permission("perm3")
        self.assertIn("perm3", role.permissions)
        
        # 测试重复添加
        role.add_permission("perm1")
        self.assertEqual(role.permissions.count("perm1"), 1)
        
        # 测试移除权限
        role.remove_permission("perm2")
        self.assertNotIn("perm2", role.permissions)
    
    def test_rbac_user_model(self):
        """测试RBAC用户模型"""
        user = RBACUser(
            id="test_user",
            username="测试用户",
            email="<EMAIL>",
            roles=["role1"]
        )
        
        # 测试添加角色
        user.add_role("role2")
        self.assertIn("role2", user.roles)
        
        # 测试添加直接权限
        user.add_direct_permission("direct_perm")
        self.assertIn("direct_perm", user.direct_permissions)


class TestMemoryRBACStore(TestRBACSystem):
    """内存RBAC存储测试"""
    
    async def test_user_operations(self):
        """测试用户操作"""
        user = RBACUser(
            id="test_user",
            username="测试用户",
            email="<EMAIL>"
        )
        
        # 测试保存用户
        success = await self.store.save_user(user)
        self.assertTrue(success)
        
        # 测试获取用户
        retrieved_user = await self.store.get_user("test_user")
        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user.username, "测试用户")
        
        # 测试列出用户
        users = await self.store.list_users()
        self.assertGreater(len(users), 0)
        
        # 测试删除用户
        success = await self.store.delete_user("test_user")
        self.assertTrue(success)
        
        # 验证删除
        deleted_user = await self.store.get_user("test_user")
        self.assertIsNone(deleted_user)
    
    async def test_role_operations(self):
        """测试角色操作"""
        role = Role(
            id="test_role",
            name="测试角色",
            description="测试角色描述"
        )
        
        # 测试保存角色
        success = await self.store.save_role(role)
        self.assertTrue(success)
        
        # 测试获取角色
        retrieved_role = await self.store.get_role("test_role")
        self.assertIsNotNone(retrieved_role)
        self.assertEqual(retrieved_role.name, "测试角色")
        
        # 测试删除非系统角色
        success = await self.store.delete_role("test_role")
        self.assertTrue(success)
        
        # 测试删除系统角色（应该失败）
        success = await self.store.delete_role("admin")
        self.assertFalse(success)
    
    async def test_permission_operations(self):
        """测试权限操作"""
        permission = Permission(
            id="test_permission",
            name="测试权限",
            description="测试权限描述",
            resource_type=ResourceType.DATA,
            permission_type=PermissionType.READ
        )
        
        # 测试保存权限
        success = await self.store.save_permission(permission)
        self.assertTrue(success)
        
        # 测试获取权限
        retrieved_permission = await self.store.get_permission("test_permission")
        self.assertIsNotNone(retrieved_permission)
        self.assertEqual(retrieved_permission.name, "测试权限")
        
        # 测试删除权限
        success = await self.store.delete_permission("test_permission")
        self.assertTrue(success)


class TestRBACManager(TestRBACSystem):
    """RBAC管理器测试"""
    
    async def test_access_control(self):
        """测试访问控制"""
        # 创建测试用户
        await self.rbac_manager.create_user(
            user_id="test_user",
            username="测试用户",
            roles=["user"]
        )
        
        # 创建访问请求
        request = AccessRequest(
            user_id="test_user",
            resource_id="test_data",
            resource_type=ResourceType.DATA,
            permission_type=PermissionType.READ
        )
        
        # 执行权限检查
        result = await self.rbac_manager.check_access(request)
        
        # 验证结果
        self.assertIsInstance(result, AccessResult)
        self.assertTrue(result.granted)  # 用户角色应该有读取权限
    
    async def test_superuser_access(self):
        """测试超级用户访问"""
        # 创建超级用户
        await self.rbac_manager.create_user(
            user_id="superuser",
            username="超级用户",
            is_superuser=True
        )
        
        # 创建访问请求
        request = AccessRequest(
            user_id="superuser",
            resource_id="any_resource",
            resource_type=ResourceType.SYSTEM,
            permission_type=PermissionType.ADMIN
        )
        
        # 执行权限检查
        result = await self.rbac_manager.check_access(request)
        
        # 超级用户应该有所有权限
        self.assertTrue(result.granted)
        self.assertIn("超级用户", result.reason)
    
    async def test_role_inheritance(self):
        """测试角色继承"""
        # 创建父角色
        await self.rbac_manager.create_role(
            role_id="parent_role",
            name="父角色",
            description="父角色描述",
            permissions=["read_all"]
        )
        
        # 创建子角色
        await self.rbac_manager.create_role(
            role_id="child_role",
            name="子角色",
            description="子角色描述",
            permissions=["write_own_data"],
            parent_roles=["parent_role"]
        )
        
        # 创建用户并分配子角色
        await self.rbac_manager.create_user(
            user_id="test_user",
            username="测试用户",
            roles=["child_role"]
        )
        
        # 获取用户权限
        user = await self.store.get_user("test_user")
        permissions = await self.rbac_manager._get_user_permissions(user)
        
        # 应该包含父角色和子角色的权限
        self.assertIn("read_all", permissions)
        self.assertIn("write_own_data", permissions)
    
    async def test_permission_caching(self):
        """测试权限缓存"""
        # 创建用户
        await self.rbac_manager.create_user(
            user_id="cache_user",
            username="缓存测试用户",
            roles=["user"]
        )
        
        user = await self.store.get_user("cache_user")
        
        # 第一次获取权限（应该缓存）
        permissions1 = await self.rbac_manager._get_user_permissions(user)
        
        # 第二次获取权限（应该从缓存获取）
        permissions2 = await self.rbac_manager._get_user_permissions(user)
        
        # 权限应该相同
        self.assertEqual(permissions1, permissions2)
        
        # 清除缓存
        self.rbac_manager.clear_cache("cache_user")
        
        # 再次获取权限
        permissions3 = await self.rbac_manager._get_user_permissions(user)
        self.assertEqual(permissions1, permissions3)


class TestRBACTool(TestRBACSystem):
    """RBAC工具测试"""
    
    async def test_check_access_action(self):
        """测试权限检查操作"""
        # 先创建用户
        result = await self.rbac_tool.execute({
            "action": "create_user",
            "user_id": "tool_test_user",
            "username": "工具测试用户",
            "roles": ["user"]
        })
        self.assertTrue(result.success)
        
        # 测试权限检查
        result = await self.rbac_tool.execute({
            "action": "check_access",
            "user_id": "tool_test_user",
            "resource_id": "test_resource",
            "resource_type": "data",
            "permission_type": "read"
        })
        
        self.assertTrue(result.success)
        self.assertIn("granted", result.result)
        self.assertIn("reason", result.result)
    
    async def test_user_management_actions(self):
        """测试用户管理操作"""
        # 创建用户
        result = await self.rbac_tool.execute({
            "action": "create_user",
            "user_id": "mgmt_test_user",
            "username": "管理测试用户",
            "email": "<EMAIL>"
        })
        self.assertTrue(result.success)
        
        # 分配角色
        result = await self.rbac_tool.execute({
            "action": "assign_role",
            "user_id": "mgmt_test_user",
            "role_id": "user"
        })
        self.assertTrue(result.success)
        
        # 获取用户信息
        result = await self.rbac_tool.execute({
            "action": "get_user_info",
            "user_id": "mgmt_test_user"
        })
        self.assertTrue(result.success)
        self.assertIn("user", result.result)
        self.assertIn("roles", result.result)
    
    async def test_invalid_actions(self):
        """测试无效操作"""
        # 测试缺少action参数
        result = await self.rbac_tool.execute({})
        self.assertFalse(result.success)
        self.assertIn("缺少action参数", result.error)
        
        # 测试不支持的操作
        result = await self.rbac_tool.execute({
            "action": "invalid_action"
        })
        self.assertFalse(result.success)
        self.assertIn("不支持的操作", result.error)
        
        # 测试缺少必要参数
        result = await self.rbac_tool.execute({
            "action": "check_access"
        })
        self.assertFalse(result.success)
        self.assertIn("缺少必要参数", result.error)


class TestRBACIntegration(TestRBACSystem):
    """RBAC集成测试"""
    
    async def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 创建权限
        result = await self.rbac_tool.execute({
            "action": "create_permission",
            "permission_id": "workflow_read",
            "name": "工作流读取",
            "description": "工作流读取权限",
            "resource_type": "data",
            "permission_type": "read",
            "resource_pattern": "workflow:*"
        })
        self.assertTrue(result.success)
        
        # 2. 创建角色
        result = await self.rbac_tool.execute({
            "action": "create_role",
            "role_id": "workflow_user",
            "name": "工作流用户",
            "description": "工作流用户角色",
            "permissions": ["workflow_read"]
        })
        self.assertTrue(result.success)
        
        # 3. 创建用户
        result = await self.rbac_tool.execute({
            "action": "create_user",
            "user_id": "workflow_test",
            "username": "工作流测试用户",
            "roles": ["workflow_user"]
        })
        self.assertTrue(result.success)
        
        # 4. 测试权限
        result = await self.rbac_tool.execute({
            "action": "check_access",
            "user_id": "workflow_test",
            "resource_id": "workflow:test_flow",
            "resource_type": "data",
            "permission_type": "read"
        })
        self.assertTrue(result.success)
        self.assertTrue(result.result["granted"])
        
        # 5. 测试无权限操作
        result = await self.rbac_tool.execute({
            "action": "check_access",
            "user_id": "workflow_test",
            "resource_id": "workflow:test_flow",
            "resource_type": "data",
            "permission_type": "delete"
        })
        self.assertTrue(result.success)
        self.assertFalse(result.result["granted"])


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestMemoryRBACStore, TestRBACManager, TestRBACTool, TestRBACIntegration]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
