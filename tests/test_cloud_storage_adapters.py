"""
云存储适配器测试

测试AWS S3、阿里云OSS、腾讯云COS适配器的功能。
"""

import asyncio
import os
import tempfile
import unittest
from unittest.mock import Mock, patch, AsyncMock
import pytest

from ai_agent_framework.tools.file_storage_tool import FileStorageTool, StorageType


class TestCloudStorageAdapters(unittest.TestCase):
    """云存储适配器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_file = None
        self.temp_content = "这是一个测试文件内容\n用于测试云存储适配器功能"
    
    def tearDown(self):
        """测试后清理"""
        if self.temp_file and os.path.exists(self.temp_file):
            os.unlink(self.temp_file)
    
    def create_temp_file(self) -> str:
        """创建临时测试文件"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write(self.temp_content)
            self.temp_file = f.name
        return self.temp_file


class TestAWSS3Adapter(TestCloudStorageAdapters):
    """AWS S3适配器测试"""
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.boto3')
    async def test_s3_adapter_connection(self, mock_boto3):
        """测试S3适配器连接"""
        # 模拟boto3
        mock_session = Mock()
        mock_client = Mock()
        mock_session.client.return_value = mock_client
        mock_boto3.Session.return_value = mock_session
        
        # 创建S3存储工具
        s3_config = {
            "access_key_id": "test-key",
            "secret_access_key": "test-secret",
            "bucket_name": "test-bucket",
            "region": "us-east-1"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.AWS_S3,
            connection_config=s3_config
        )
        
        # 测试连接
        result = await storage_tool.execute({"action": "connect"})
        self.assertTrue(result.success)
        
        # 验证boto3调用
        mock_boto3.Session.assert_called_once()
        mock_session.client.assert_called_with('s3')
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.boto3')
    async def test_s3_adapter_upload(self, mock_boto3):
        """测试S3适配器文件上传"""
        # 模拟boto3
        mock_session = Mock()
        mock_client = Mock()
        mock_session.client.return_value = mock_client
        mock_boto3.Session.return_value = mock_session
        
        # 创建临时文件
        temp_file = self.create_temp_file()
        
        # 创建S3存储工具
        s3_config = {
            "access_key_id": "test-key",
            "secret_access_key": "test-secret",
            "bucket_name": "test-bucket",
            "region": "us-east-1"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.AWS_S3,
            connection_config=s3_config
        )
        
        # 连接
        await storage_tool.execute({"action": "connect"})
        
        # 测试上传
        result = await storage_tool.execute({
            "action": "upload",
            "local_path": temp_file,
            "remote_path": "test/file.txt",
            "metadata": {"test": "value"}
        })
        
        self.assertTrue(result.success)
        
        # 验证upload_file调用
        mock_client.upload_file.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.boto3')
    async def test_s3_adapter_download(self, mock_boto3):
        """测试S3适配器文件下载"""
        # 模拟boto3
        mock_session = Mock()
        mock_client = Mock()
        mock_session.client.return_value = mock_client
        mock_boto3.Session.return_value = mock_session
        
        # 创建S3存储工具
        s3_config = {
            "access_key_id": "test-key",
            "secret_access_key": "test-secret",
            "bucket_name": "test-bucket",
            "region": "us-east-1"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.AWS_S3,
            connection_config=s3_config
        )
        
        # 连接
        await storage_tool.execute({"action": "connect"})
        
        # 测试下载
        download_path = tempfile.mktemp(suffix='.txt')
        try:
            result = await storage_tool.execute({
                "action": "download",
                "remote_path": "test/file.txt",
                "local_path": download_path
            })
            
            self.assertTrue(result.success)
            
            # 验证download_file调用
            mock_client.download_file.assert_called_once()
        
        finally:
            if os.path.exists(download_path):
                os.unlink(download_path)


class TestAliyunOSSAdapter(TestCloudStorageAdapters):
    """阿里云OSS适配器测试"""
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.oss2')
    async def test_oss_adapter_connection(self, mock_oss2):
        """测试OSS适配器连接"""
        # 模拟oss2
        mock_auth = Mock()
        mock_bucket = Mock()
        mock_oss2.Auth.return_value = mock_auth
        mock_oss2.Bucket.return_value = mock_bucket
        
        # 创建OSS存储工具
        oss_config = {
            "access_key_id": "test-key",
            "access_key_secret": "test-secret",
            "bucket_name": "test-bucket",
            "endpoint": "oss-cn-hangzhou.aliyuncs.com"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.ALIYUN_OSS,
            connection_config=oss_config
        )
        
        # 测试连接
        result = await storage_tool.execute({"action": "connect"})
        self.assertTrue(result.success)
        
        # 验证oss2调用
        mock_oss2.Auth.assert_called_once()
        mock_oss2.Bucket.assert_called_once()
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.oss2')
    async def test_oss_adapter_file_operations(self, mock_oss2):
        """测试OSS适配器文件操作"""
        # 模拟oss2
        mock_auth = Mock()
        mock_bucket = Mock()
        mock_oss2.Auth.return_value = mock_auth
        mock_oss2.Bucket.return_value = mock_bucket
        
        # 模拟文件存在检查
        mock_bucket.object_exists.return_value = True
        
        # 创建临时文件
        temp_file = self.create_temp_file()
        
        # 创建OSS存储工具
        oss_config = {
            "access_key_id": "test-key",
            "access_key_secret": "test-secret",
            "bucket_name": "test-bucket",
            "endpoint": "oss-cn-hangzhou.aliyuncs.com"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.ALIYUN_OSS,
            connection_config=oss_config
        )
        
        # 连接
        await storage_tool.execute({"action": "connect"})
        
        # 测试上传
        result = await storage_tool.execute({
            "action": "upload",
            "local_path": temp_file,
            "remote_path": "test/file.txt"
        })
        self.assertTrue(result.success)
        
        # 测试文件存在检查
        result = await storage_tool.execute({
            "action": "exists",
            "remote_path": "test/file.txt"
        })
        self.assertTrue(result.success)
        self.assertTrue(result.result["exists"])
        
        # 测试删除
        result = await storage_tool.execute({
            "action": "delete",
            "remote_path": "test/file.txt"
        })
        self.assertTrue(result.success)


class TestTencentCOSAdapter(TestCloudStorageAdapters):
    """腾讯云COS适配器测试"""
    
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.CosConfig')
    @patch('ai_agent_framework.tools.adapters.cloud_storage_adapters.CosS3Client')
    async def test_cos_adapter_connection(self, mock_cos_client_class, mock_cos_config_class):
        """测试COS适配器连接"""
        # 模拟COS
        mock_config = Mock()
        mock_client = Mock()
        mock_cos_config_class.return_value = mock_config
        mock_cos_client_class.return_value = mock_client
        
        # 创建COS存储工具
        cos_config = {
            "secret_id": "test-id",
            "secret_key": "test-key",
            "bucket_name": "test-bucket-1234567890",
            "region": "ap-beijing"
        }
        
        storage_tool = FileStorageTool(
            storage_type=StorageType.TENCENT_COS,
            connection_config=cos_config
        )
        
        # 测试连接
        result = await storage_tool.execute({"action": "connect"})
        self.assertTrue(result.success)
        
        # 验证COS调用
        mock_cos_config_class.assert_called_once()
        mock_cos_client_class.assert_called_once()


class TestCloudStorageIntegration(TestCloudStorageAdapters):
    """云存储集成测试"""
    
    def test_storage_type_validation(self):
        """测试存储类型验证"""
        # 测试无效存储类型
        with self.assertRaises(ValueError):
            FileStorageTool(
                storage_type="invalid_type",
                connection_config={}
            )
    
    def test_missing_dependencies(self):
        """测试缺失依赖的处理"""
        # 这个测试需要在没有安装相应依赖的环境中运行
        # 在CI/CD环境中可以通过mock来模拟ImportError
        pass
    
    async def test_adapter_error_handling(self):
        """测试适配器错误处理"""
        # 测试连接失败的情况
        storage_tool = FileStorageTool(
            storage_type=StorageType.LOCAL,
            connection_config={"base_path": "/invalid/path"}
        )
        
        # 测试在未连接状态下执行操作
        result = await storage_tool.execute({
            "action": "upload",
            "local_path": "nonexistent.txt",
            "remote_path": "test.txt"
        })
        
        # 应该返回错误
        self.assertFalse(result.success)


# 异步测试运行器
def run_async_test(coro):
    """运行异步测试"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


# 将异步测试方法转换为同步
for cls in [TestAWSS3Adapter, TestAliyunOSSAdapter, TestTencentCOSAdapter, TestCloudStorageIntegration]:
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
            # 创建同步包装器
            def make_sync_test(async_method):
                def sync_test(self):
                    return run_async_test(async_method(self))
                return sync_test
            
            setattr(cls, attr_name, make_sync_test(attr))


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
