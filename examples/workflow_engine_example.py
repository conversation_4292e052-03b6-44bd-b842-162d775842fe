#!/usr/bin/env python3
"""
工作流引擎工具使用示例

演示如何使用工作流编排和执行引擎进行复杂业务流程自动化。
"""

import asyncio
from datetime import datetime

from ai_agent_framework.tools import WorkflowEngineTool, WorkflowEngineConfig
from ai_agent_framework.utils.logging_system import logging_system


async def demo_simple_workflow():
    """演示简单工作流"""
    print("\n=== 简单工作流演示 ===")
    
    # 创建工作流引擎工具
    engine_config = WorkflowEngineConfig(
        max_concurrent_workflows=5,
        default_task_timeout=60
    )
    
    workflow_engine = WorkflowEngineTool(engine_config)
    
    try:
        # 创建简单的数据处理工作流
        print("📋 创建数据处理工作流...")
        
        workflow_config = {
            "name": "数据处理工作流",
            "description": "演示数据获取、处理和存储的完整流程",
            "version": "1.0",
            "variables": {
                "api_url": "https://jsonplaceholder.typicode.com/posts/1",
                "output_file": "/tmp/processed_data.json"
            },
            "tasks": [
                {
                    "name": "获取数据",
                    "description": "从API获取数据",
                    "task_type": "http_request",
                    "config": {
                        "url": "${api_url}",
                        "method": "GET",
                        "headers": {
                            "Content-Type": "application/json"
                        }
                    },
                    "dependencies": [],
                    "timeout_seconds": 30
                },
                {
                    "name": "等待处理",
                    "description": "模拟数据处理延迟",
                    "task_type": "delay",
                    "config": {
                        "seconds": 2
                    },
                    "dependencies": ["获取数据"]
                },
                {
                    "name": "保存数据",
                    "description": "将处理后的数据保存到文件",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'Data processed at $(date)' > ${output_file}"
                    },
                    "dependencies": ["等待处理"],
                    "timeout_seconds": 10
                }
            ]
        }
        
        # 创建工作流
        result = await workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config,
            "user_id": "demo_user"
        })
        
        if result.success:
            workflow_id = result.result["workflow_id"]
            print(f"✅ 工作流创建成功: {workflow_id}")
            print(f"📊 任务数量: {result.result['task_count']}")
            
            # 启动工作流
            print("\n🚀 启动工作流...")
            start_result = await workflow_engine.execute({
                "action": "start_workflow",
                "workflow_id": workflow_id,
                "variables": {
                    "processing_mode": "batch"
                }
            })
            
            if start_result.success:
                print("✅ 工作流启动成功")
                
                # 监控工作流执行
                print("\n📊 监控工作流执行...")
                for i in range(10):  # 最多等待10次
                    await asyncio.sleep(2)
                    
                    status_result = await workflow_engine.execute({
                        "action": "get_workflow_status",
                        "workflow_id": workflow_id
                    })
                    
                    if status_result.success:
                        status_info = status_result.result
                        print(f"状态检查 {i+1}: {status_info['workflow_status']}")
                        print(f"  任务状态: {status_info['task_status_counts']}")
                        
                        if status_info['workflow_status'] in ['completed', 'failed', 'cancelled']:
                            break
                
                # 获取最终状态和任务详情
                print("\n📋 获取工作流详情...")
                detail_result = await workflow_engine.execute({
                    "action": "list_tasks",
                    "workflow_id": workflow_id
                })
                
                if detail_result.success:
                    tasks = detail_result.result["tasks"]
                    print(f"📊 任务执行结果:")
                    for task in tasks:
                        status_icon = "✅" if task["status"] == "completed" else "❌" if task["status"] == "failed" else "⏳"
                        print(f"  {status_icon} {task['name']}: {task['status']}")
                        if task.get("error"):
                            print(f"    错误: {task['error']}")
            else:
                print(f"❌ 工作流启动失败: {start_result.error}")
        else:
            print(f"❌ 工作流创建失败: {result.error}")
    
    except Exception as e:
        print(f"❌ 简单工作流演示过程中发生错误: {e}")


async def demo_conditional_workflow():
    """演示条件工作流"""
    print("\n=== 条件工作流演示 ===")
    
    workflow_engine = WorkflowEngineTool()
    
    try:
        # 创建包含条件判断的工作流
        print("🔀 创建条件判断工作流...")
        
        workflow_config = {
            "name": "条件处理工作流",
            "description": "根据条件执行不同的处理分支",
            "variables": {
                "threshold": 50,
                "current_value": 75
            },
            "tasks": [
                {
                    "name": "检查条件",
                    "description": "检查当前值是否超过阈值",
                    "task_type": "condition",
                    "config": {
                        "condition": "${current_value} > ${threshold}"
                    },
                    "dependencies": []
                },
                {
                    "name": "高值处理",
                    "description": "当值超过阈值时的处理",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'High value detected: ${current_value}'"
                    },
                    "dependencies": ["检查条件"],
                    "conditions": {
                        "condition_result": True
                    }
                },
                {
                    "name": "低值处理",
                    "description": "当值未超过阈值时的处理",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'Normal value: ${current_value}'"
                    },
                    "dependencies": ["检查条件"],
                    "conditions": {
                        "condition_result": False
                    }
                },
                {
                    "name": "发送通知",
                    "description": "发送处理完成通知",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'Processing completed for value: ${current_value}'"
                    },
                    "dependencies": ["高值处理", "低值处理"]
                }
            ]
        }
        
        # 创建并启动工作流
        result = await workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config
        })
        
        if result.success:
            workflow_id = result.result["workflow_id"]
            print(f"✅ 条件工作流创建成功: {workflow_id}")
            
            # 启动工作流
            await workflow_engine.execute({
                "action": "start_workflow",
                "workflow_id": workflow_id
            })
            
            # 等待执行完成
            await asyncio.sleep(5)
            
            # 查看结果
            tasks_result = await workflow_engine.execute({
                "action": "list_tasks",
                "workflow_id": workflow_id
            })
            
            if tasks_result.success:
                tasks = tasks_result.result["tasks"]
                print("📊 条件工作流执行结果:")
                for task in tasks:
                    status_icon = "✅" if task["status"] == "completed" else "⏭️" if task["status"] == "skipped" else "❌"
                    print(f"  {status_icon} {task['name']}: {task['status']}")
    
    except Exception as e:
        print(f"❌ 条件工作流演示过程中发生错误: {e}")


async def demo_parallel_workflow():
    """演示并行工作流"""
    print("\n=== 并行工作流演示 ===")
    
    workflow_engine = WorkflowEngineTool()
    
    try:
        # 创建包含并行任务的工作流
        print("⚡ 创建并行处理工作流...")
        
        workflow_config = {
            "name": "并行处理工作流",
            "description": "演示多个任务并行执行",
            "variables": {
                "batch_size": 3
            },
            "tasks": [
                {
                    "name": "初始化",
                    "description": "初始化处理环境",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'Initializing parallel processing...'"
                    },
                    "dependencies": []
                },
                {
                    "name": "并行任务1",
                    "description": "并行处理任务1",
                    "task_type": "delay",
                    "config": {
                        "seconds": 2
                    },
                    "dependencies": ["初始化"]
                },
                {
                    "name": "并行任务2",
                    "description": "并行处理任务2",
                    "task_type": "delay",
                    "config": {
                        "seconds": 3
                    },
                    "dependencies": ["初始化"]
                },
                {
                    "name": "并行任务3",
                    "description": "并行处理任务3",
                    "task_type": "delay",
                    "config": {
                        "seconds": 1
                    },
                    "dependencies": ["初始化"]
                },
                {
                    "name": "汇总结果",
                    "description": "汇总所有并行任务的结果",
                    "task_type": "script",
                    "config": {
                        "type": "shell",
                        "script": "echo 'All parallel tasks completed. Aggregating results...'"
                    },
                    "dependencies": ["并行任务1", "并行任务2", "并行任务3"]
                }
            ]
        }
        
        # 创建并启动工作流
        result = await workflow_engine.execute({
            "action": "create_workflow",
            "workflow_config": workflow_config
        })
        
        if result.success:
            workflow_id = result.result["workflow_id"]
            print(f"✅ 并行工作流创建成功: {workflow_id}")
            
            start_time = datetime.now()
            
            # 启动工作流
            await workflow_engine.execute({
                "action": "start_workflow",
                "workflow_id": workflow_id
            })
            
            # 监控执行进度
            print("📊 监控并行执行进度...")
            while True:
                await asyncio.sleep(1)
                
                status_result = await workflow_engine.execute({
                    "action": "get_workflow_status",
                    "workflow_id": workflow_id
                })
                
                if status_result.success:
                    status_info = status_result.result
                    elapsed = (datetime.now() - start_time).total_seconds()
                    print(f"  {elapsed:.1f}s - 状态: {status_info['workflow_status']}")
                    print(f"    任务进度: {status_info['task_status_counts']}")
                    
                    if status_info['workflow_status'] in ['completed', 'failed']:
                        break
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            print(f"✅ 并行工作流执行完成，总耗时: {total_time:.1f}秒")
    
    except Exception as e:
        print(f"❌ 并行工作流演示过程中发生错误: {e}")


async def demo_workflow_management():
    """演示工作流管理功能"""
    print("\n=== 工作流管理功能演示 ===")
    
    workflow_engine = WorkflowEngineTool()
    
    try:
        # 创建多个测试工作流
        print("📋 创建多个测试工作流...")
        
        workflow_ids = []
        for i in range(3):
            workflow_config = {
                "name": f"测试工作流 {i+1}",
                "description": f"第{i+1}个测试工作流",
                "tasks": [
                    {
                        "name": f"任务 {i+1}",
                        "task_type": "delay",
                        "config": {"seconds": 1},
                        "dependencies": []
                    }
                ]
            }
            
            result = await workflow_engine.execute({
                "action": "create_workflow",
                "workflow_config": workflow_config
            })
            
            if result.success:
                workflow_id = result.result["workflow_id"]
                workflow_ids.append(workflow_id)
                print(f"✅ 创建工作流 {i+1}: {workflow_id[:8]}...")
        
        # 列出所有工作流
        print("\n📋 列出所有工作流...")
        list_result = await workflow_engine.execute({
            "action": "list_workflows"
        })
        
        if list_result.success:
            workflows = list_result.result["workflows"]
            print(f"📊 共有 {list_result.result['count']} 个工作流:")
            for workflow in workflows:
                print(f"  - {workflow['name']} ({workflow['id'][:8]}...)")
                print(f"    状态: {workflow['status']}")
                print(f"    任务数: {workflow['task_count']}")
                print(f"    创建时间: {workflow['created_at']}")
        
        # 启动第一个工作流
        if workflow_ids:
            print(f"\n🚀 启动第一个工作流...")
            await workflow_engine.execute({
                "action": "start_workflow",
                "workflow_id": workflow_ids[0]
            })
            
            await asyncio.sleep(2)
            
            # 获取工作流详情
            detail_result = await workflow_engine.execute({
                "action": "get_workflow",
                "workflow_id": workflow_ids[0]
            })
            
            if detail_result.success:
                workflow_info = detail_result.result["workflow"]
                print(f"📊 工作流详情:")
                print(f"  名称: {workflow_info['name']}")
                print(f"  状态: {workflow_info['status']}")
                print(f"  开始时间: {workflow_info['started_at']}")
                print(f"  完成时间: {workflow_info['completed_at']}")
        
        # 按状态过滤工作流
        print("\n🔍 按状态过滤工作流...")
        for status in ["pending", "completed"]:
            filter_result = await workflow_engine.execute({
                "action": "list_workflows",
                "status_filter": status
            })
            
            if filter_result.success:
                count = filter_result.result["count"]
                print(f"  {status.upper()} 状态的工作流: {count} 个")
    
    except Exception as e:
        print(f"❌ 工作流管理演示过程中发生错误: {e}")


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("🔄 AI Agent Framework 工作流引擎工具演示")
    print("=" * 60)
    
    # 演示各个功能
    await demo_simple_workflow()
    await demo_conditional_workflow()
    await demo_parallel_workflow()
    await demo_workflow_management()
    
    print("\n✨ 演示完成！")
    print("\n💡 工作流引擎工具特性总结：")
    print("1. ✅ 多种任务类型支持")
    print("   - HTTP请求任务")
    print("   - 脚本执行任务")
    print("   - 函数调用任务")
    print("   - 条件判断任务")
    print("   - 延迟任务")
    print("2. ✅ 复杂流程控制")
    print("   - 任务依赖管理")
    print("   - 条件执行")
    print("   - 并行处理")
    print("   - 变量传递")
    print("3. ✅ 工作流管理")
    print("   - 创建和配置")
    print("   - 启动和停止")
    print("   - 状态监控")
    print("   - 结果查看")
    print("4. ✅ 错误处理")
    print("   - 任务重试机制")
    print("   - 超时控制")
    print("   - 失败恢复")
    
    print("\n📚 支持的任务类型：")
    print("- http_request: HTTP请求任务")
    print("- script: 脚本执行任务")
    print("- function: 函数调用任务")
    print("- condition: 条件判断任务")
    print("- delay: 延迟任务")
    print("- parallel: 并行任务组")
    print("- sequential: 顺序任务组")
    print("- approval: 人工审批任务")
    print("- notification: 通知任务")


if __name__ == "__main__":
    asyncio.run(main())
