#!/usr/bin/env python3
"""
服务验证示例

展示如何使用服务验证工具来测试注册的服务。
"""

import asyncio
from pathlib import Path

from ai_agent_framework.tools.service_validator import ServiceValidator
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager


async def demo_basic_validation():
    """基础验证示例"""
    print("🔍 基础服务验证示例")
    print("-" * 30)
    
    # 创建验证工具
    validator = ServiceValidator()
    
    # 定义一个简单的测试服务配置
    test_service_config = {
        "name": "httpbin_test",
        "description": "HTTPBin测试服务",
        "base_url": "https://httpbin.org",
        "service_type": "rest_api",
        "auth": {"type": "none"},
        "endpoints": [
            {
                "name": "get_ip",
                "path": "/ip",
                "method": "GET",
                "description": "获取IP地址",
                "requires_auth": False,
                "timeout": 10.0,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_status",
                "path": "/status/200",
                "method": "GET",
                "description": "获取状态",
                "requires_auth": False,
                "timeout": 10.0,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        ],
        "health_check_path": "/status/200"
    }
    
    # 执行服务验证
    result = await validator.execute({
        "action": "validate_service",
        "service_config": test_service_config,
        "options": {
            "timeout": 15,
            "generate_report": True
        }
    })
    
    if result.success:
        print("✅ 服务验证成功!")
        summary = result.result.get("summary", {})
        print(f"   总测试数: {summary.get('total_tests', 0)}")
        print(f"   通过测试: {summary.get('passed_tests', 0)}")
        print(f"   失败测试: {summary.get('failed_tests', 0)}")
        print(f"   成功率: {summary.get('success_rate', 0)}%")
        print(f"   总耗时: {summary.get('total_duration', 0):.2f}秒")
        
        report_file = result.result.get("report_file")
        if report_file:
            print(f"   详细报告: {report_file}")
    else:
        print(f"❌ 服务验证失败: {result.error}")


async def demo_endpoint_validation():
    """端点验证示例"""
    print("\n🎯 单个端点验证示例")
    print("-" * 30)
    
    validator = ServiceValidator()
    
    # 测试单个端点
    result = await validator.execute({
        "action": "validate_endpoint",
        "service_config": {
            "name": "httpbin_test",
            "description": "HTTPBin测试服务",
            "base_url": "https://httpbin.org",
            "service_type": "rest_api",
            "auth": {"type": "none"},
            "endpoints": [
                {
                    "name": "post_data",
                    "path": "/post",
                    "method": "POST",
                    "description": "发送POST数据",
                    "requires_auth": False,
                    "timeout": 15.0,
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "message": {"type": "string", "description": "消息"},
                            "data": {"type": "object", "description": "数据"}
                        },
                        "required": ["message"]
                    }
                }
            ]
        },
        "endpoint_name": "post_data",
        "options": {
            "test_parameters": {
                "message": "Hello from validator!",
                "data": {"timestamp": "2024-01-01", "source": "validation_test"}
            }
        }
    })
    
    if result.success:
        print("✅ 端点验证成功!")
        test_result = result.result.get("test_result", {})
        print(f"   端点名称: {test_result.get('test_name', 'N/A')}")
        print(f"   执行时间: {test_result.get('duration', 0):.2f}秒")
        print(f"   详细信息: {test_result.get('details', {})}")
    else:
        print(f"❌ 端点验证失败: {result.error}")


async def demo_health_check():
    """健康检查示例"""
    print("\n💓 健康检查示例")
    print("-" * 30)
    
    validator = ServiceValidator()
    
    # 执行健康检查
    result = await validator.execute({
        "action": "health_check",
        "service_config": {
            "name": "httpbin_health",
            "description": "HTTPBin健康检查",
            "base_url": "https://httpbin.org",
            "service_type": "rest_api",
            "auth": {"type": "none"},
            "health_check_path": "/status/200",
            "endpoints": []
        }
    })
    
    if result.success:
        print("✅ 健康检查通过!")
        health_status = result.result.get("health_status", "unknown")
        print(f"   健康状态: {health_status}")
        
        check_result = result.result.get("check_result", {})
        print(f"   检查耗时: {check_result.get('duration', 0):.2f}秒")
        print(f"   状态码: {check_result.get('details', {}).get('status_code', 'N/A')}")
    else:
        print(f"❌ 健康检查失败: {result.error}")


async def demo_performance_test():
    """性能测试示例"""
    print("\n⚡ 性能测试示例")
    print("-" * 30)
    
    validator = ServiceValidator()
    
    # 执行性能测试
    result = await validator.execute({
        "action": "performance_test",
        "service_config": {
            "name": "httpbin_performance",
            "description": "HTTPBin性能测试",
            "base_url": "https://httpbin.org",
            "service_type": "rest_api",
            "auth": {"type": "none"},
            "health_check_path": "/status/200",
            "endpoints": []
        },
        "options": {
            "performance_iterations": 10,
            "timeout": 30
        }
    })
    
    if result.success:
        print("✅ 性能测试完成!")
        metrics = result.result.get("performance_metrics", {})
        print(f"   测试次数: {metrics.get('iterations', 0)}")
        print(f"   平均响应时间: {metrics.get('avg_response_time', 0)}ms")
        print(f"   最小响应时间: {metrics.get('min_response_time', 0)}ms")
        print(f"   最大响应时间: {metrics.get('max_response_time', 0)}ms")
        print(f"   成功率: {metrics.get('success_rate', 0)}%")
        print(f"   性能评级: {result.result.get('performance_grade', 'N/A')}")
    else:
        print(f"❌ 性能测试失败: {result.error}")


async def demo_full_validation():
    """完整验证示例"""
    print("\n🔬 完整验证示例")
    print("-" * 30)
    
    # 创建认证管理器（可选）
    auth_manager = ServiceAuthManager()
    validator = ServiceValidator(auth_manager=auth_manager)
    
    # 定义一个更复杂的服务配置
    complex_service_config = {
        "name": "jsonplaceholder_full",
        "description": "JSONPlaceholder完整测试",
        "base_url": "https://jsonplaceholder.typicode.com",
        "service_type": "rest_api",
        "auth": {"type": "none"},
        "endpoints": [
            {
                "name": "get_posts",
                "path": "/posts",
                "method": "GET",
                "description": "获取所有文章",
                "requires_auth": False,
                "timeout": 15.0,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_post",
                "path": "/posts/{id}",
                "method": "GET",
                "description": "获取指定文章",
                "requires_auth": False,
                "timeout": 15.0,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer", "description": "文章ID", "in": "path"}
                    },
                    "required": ["id"]
                }
            },
            {
                "name": "create_post",
                "path": "/posts",
                "method": "POST",
                "description": "创建文章",
                "requires_auth": False,
                "timeout": 20.0,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "标题", "in": "body"},
                        "body": {"type": "string", "description": "内容", "in": "body"},
                        "userId": {"type": "integer", "description": "用户ID", "in": "body"}
                    },
                    "required": ["title", "body", "userId"]
                }
            }
        ]
    }
    
    # 执行完整验证
    result = await validator.execute({
        "action": "full_validation",
        "service_config": complex_service_config,
        "options": {
            "timeout": 30,
            "performance_iterations": 5,
            "generate_report": True,
            "test_parameters": {
                "id": 1,  # 用于测试get_post端点
                "title": "测试文章",
                "body": "这是一篇测试文章",
                "userId": 1
            }
        }
    })
    
    if result.success:
        print("✅ 完整验证成功!")
        
        # 显示基础验证结果
        summary = result.result.get("summary", {})
        print(f"   基础验证 - 成功率: {summary.get('success_rate', 0)}%")
        print(f"   基础验证 - 总耗时: {summary.get('total_duration', 0):.2f}秒")
        
        # 显示性能测试结果
        performance = result.result.get("performance_test", {})
        if "performance_metrics" in performance:
            metrics = performance["performance_metrics"]
            print(f"   性能测试 - 平均响应时间: {metrics.get('avg_response_time', 0)}ms")
            print(f"   性能测试 - 成功率: {metrics.get('success_rate', 0)}%")
        
        report_file = result.result.get("report_file")
        if report_file:
            print(f"   详细报告: {report_file}")
    else:
        print(f"❌ 完整验证失败: {result.error}")


async def demo_config_file_validation():
    """配置文件验证示例"""
    print("\n📄 配置文件验证示例")
    print("-" * 30)
    
    # 检查是否有示例配置文件
    config_file = Path("configs/services/weather_service.yaml")
    if config_file.exists():
        validator = ServiceValidator()
        
        result = await validator.execute({
            "action": "validate_service",
            "config_file": str(config_file),
            "options": {
                "timeout": 20,
                "generate_report": True
            }
        })
        
        if result.success:
            print("✅ 配置文件验证成功!")
            print(f"   服务名称: {result.result.get('service_name', 'N/A')}")
            summary = result.result.get("summary", {})
            print(f"   成功率: {summary.get('success_rate', 0)}%")
        else:
            print(f"❌ 配置文件验证失败: {result.error}")
    else:
        print("⚠️  未找到示例配置文件，跳过此测试")


async def main():
    """主函数"""
    print("🔍 AI Agent Framework 服务验证示例")
    print("=" * 50)
    
    # 运行各种验证示例
    await demo_basic_validation()
    await demo_endpoint_validation()
    await demo_health_check()
    await demo_performance_test()
    await demo_full_validation()
    await demo_config_file_validation()
    
    print("\n🎉 服务验证示例完成!")
    print("=" * 50)
    print("💡 总结:")
    print("   1. 基础服务验证：连接性、认证、端点测试")
    print("   2. 单个端点验证：针对性测试")
    print("   3. 健康检查：快速状态检查")
    print("   4. 性能测试：响应时间和成功率")
    print("   5. 完整验证：综合所有测试类型")
    print("   6. 配置文件验证：从文件加载配置")
    
    print("\n📊 验证报告:")
    print("   - 详细的JSON格式报告")
    print("   - 保存在 reports/service_validation/ 目录")
    print("   - 包含所有测试结果和性能指标")


if __name__ == "__main__":
    asyncio.run(main())
