#!/usr/bin/env python3
"""
RBAC权限控制系统使用示例

演示如何使用基于角色的访问控制功能。
"""

import asyncio
from datetime import datetime

from ai_agent_framework.tools import RBACTool, RBACToolConfig
from ai_agent_framework.security.rbac import ResourceType, PermissionType
from ai_agent_framework.utils.logging_system import logging_system


async def demo_basic_rbac():
    """演示基础RBAC功能"""
    print("\n=== 基础RBAC功能演示 ===")
    
    # 创建RBAC工具
    rbac_config = RBACToolConfig(
        store_type="memory",
        cache_ttl=300,
        enable_audit_log=True
    )
    
    rbac_tool = RBACTool(rbac_config)
    
    try:
        # 1. 创建用户
        print("📝 创建用户...")
        result = await rbac_tool.execute({
            "action": "create_user",
            "user_id": "alice",
            "username": "<PERSON>",
            "email": "<EMAIL>",
            "roles": ["user"]
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        else:
            print(f"❌ 创建用户失败: {result.error}")
        
        # 2. 创建管理员用户
        result = await rbac_tool.execute({
            "action": "create_user",
            "user_id": "admin",
            "username": "System Admin",
            "email": "<EMAIL>",
            "roles": ["admin"],
            "is_superuser": False
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 3. 创建自定义角色
        print("\n🎭 创建自定义角色...")
        result = await rbac_tool.execute({
            "action": "create_role",
            "role_id": "data_analyst",
            "name": "数据分析师",
            "description": "可以读取和分析数据，但不能修改",
            "permissions": ["read_all"],
            "parent_roles": ["user"]
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 4. 创建自定义权限
        print("\n🔑 创建自定义权限...")
        result = await rbac_tool.execute({
            "action": "create_permission",
            "permission_id": "analyze_data",
            "name": "分析数据",
            "description": "允许执行数据分析操作",
            "resource_type": "data",
            "permission_type": "execute",
            "resource_pattern": "analytics:*"
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 5. 为角色分配权限
        print("\n🔗 为角色分配权限...")
        result = await rbac_tool.execute({
            "action": "assign_role_permission",
            "role_id": "data_analyst",
            "permission_id": "analyze_data"
        })
        
        if result.success:
            print(f"✅ 权限分配成功")
        
        # 6. 为用户分配角色
        print("\n👤 为用户分配角色...")
        result = await rbac_tool.execute({
            "action": "assign_role",
            "user_id": "alice",
            "role_id": "data_analyst"
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 7. 权限检查
        print("\n🔍 权限检查演示...")
        
        # 检查Alice是否可以读取数据
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "alice",
            "resource_id": "user_data_001",
            "resource_type": "data",
            "permission_type": "read"
        })
        
        if result.success:
            access_result = result.result
            print(f"📊 Alice读取数据权限: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
            print(f"   原因: {access_result['reason']}")
            print(f"   用户角色: {access_result['user_roles']}")
        
        # 检查Alice是否可以分析数据
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "alice",
            "resource_id": "analytics:sales_report",
            "resource_type": "data",
            "permission_type": "execute"
        })
        
        if result.success:
            access_result = result.result
            print(f"📈 Alice分析数据权限: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
            print(f"   原因: {access_result['reason']}")
        
        # 检查Alice是否可以删除数据
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "alice",
            "resource_id": "user_data_001",
            "resource_type": "data",
            "permission_type": "delete"
        })
        
        if result.success:
            access_result = result.result
            print(f"🗑️ Alice删除数据权限: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
            print(f"   原因: {access_result['reason']}")
        
        # 8. 获取用户信息
        print("\n👤 获取用户详细信息...")
        result = await rbac_tool.execute({
            "action": "get_user_info",
            "user_id": "alice"
        })
        
        if result.success:
            user_info = result.result
            print(f"📋 用户信息:")
            print(f"   用户名: {user_info['user']['username']}")
            print(f"   邮箱: {user_info['user']['email']}")
            print(f"   角色数量: {len(user_info['roles'])}")
            print(f"   权限数量: {len(user_info['permissions'])}")
            
            print(f"   角色列表:")
            for role in user_info['roles']:
                print(f"     - {role['name']} ({role['id']})")
    
    except Exception as e:
        print(f"❌ RBAC演示过程中发生错误: {e}")


async def demo_advanced_rbac():
    """演示高级RBAC功能"""
    print("\n=== 高级RBAC功能演示 ===")
    
    rbac_tool = RBACTool()
    
    try:
        # 1. 创建具有时间限制的权限
        print("⏰ 创建具有时间限制的权限...")
        result = await rbac_tool.execute({
            "action": "create_permission",
            "permission_id": "night_access",
            "name": "夜间访问",
            "description": "只允许在夜间（18:00-06:00）访问",
            "resource_type": "system",
            "permission_type": "read",
            "resource_pattern": "night:*",
            "conditions": {
                "time_range": {
                    "start": 18,
                    "end": 6
                }
            }
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 2. 创建夜班角色
        result = await rbac_tool.execute({
            "action": "create_role",
            "role_id": "night_shift",
            "name": "夜班工作者",
            "description": "夜班工作人员角色",
            "permissions": ["night_access"]
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 3. 创建夜班用户
        result = await rbac_tool.execute({
            "action": "create_user",
            "user_id": "bob",
            "username": "Bob Night",
            "email": "<EMAIL>",
            "roles": ["night_shift"]
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
        
        # 4. 测试时间限制权限
        print("\n🕐 测试时间限制权限...")
        current_hour = datetime.now().hour
        
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "bob",
            "resource_id": "night:security_system",
            "resource_type": "system",
            "permission_type": "read",
            "context": {
                "current_time": datetime.now().isoformat()
            }
        })
        
        if result.success:
            access_result = result.result
            print(f"🌙 Bob夜间访问权限 (当前时间 {current_hour}:00): {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
            print(f"   原因: {access_result['reason']}")
        
        # 5. 列出所有用户
        print("\n👥 列出所有用户...")
        result = await rbac_tool.execute({
            "action": "list_users"
        })
        
        if result.success:
            users = result.result['users']
            print(f"📊 系统中共有 {result.result['count']} 个用户:")
            for user in users:
                print(f"   - {user['username']} ({user['id']}) - 角色: {', '.join(user['roles'])}")
        
        # 6. 缓存管理
        print("\n🗄️ 缓存管理演示...")
        result = await rbac_tool.execute({
            "action": "clear_cache",
            "user_id": "alice"
        })
        
        if result.success:
            print(f"✅ {result.result['message']}")
    
    except Exception as e:
        print(f"❌ 高级RBAC演示过程中发生错误: {e}")


async def demo_role_inheritance():
    """演示角色继承功能"""
    print("\n=== 角色继承功能演示 ===")
    
    rbac_tool = RBACTool()
    
    try:
        # 1. 创建基础角色层次结构
        print("🏗️ 创建角色层次结构...")
        
        # 创建员工基础角色
        result = await rbac_tool.execute({
            "action": "create_role",
            "role_id": "employee",
            "name": "员工",
            "description": "基础员工角色",
            "permissions": ["read_all", "write_own_data"]
        })
        
        if result.success:
            print(f"✅ 创建员工角色成功")
        
        # 创建经理角色（继承员工角色）
        result = await rbac_tool.execute({
            "action": "create_role",
            "role_id": "manager",
            "name": "经理",
            "description": "部门经理角色",
            "permissions": ["execute_tools"],
            "parent_roles": ["employee"]
        })
        
        if result.success:
            print(f"✅ 创建经理角色成功")
        
        # 创建总监角色（继承经理角色）
        result = await rbac_tool.execute({
            "action": "create_role",
            "role_id": "director",
            "name": "总监",
            "description": "部门总监角色",
            "permissions": ["manage_agents"],
            "parent_roles": ["manager"]
        })
        
        if result.success:
            print(f"✅ 创建总监角色成功")
        
        # 2. 创建用户并分配角色
        result = await rbac_tool.execute({
            "action": "create_user",
            "user_id": "charlie",
            "username": "Charlie Director",
            "email": "<EMAIL>",
            "roles": ["director"]
        })
        
        if result.success:
            print(f"✅ 创建总监用户成功")
        
        # 3. 测试角色继承权限
        print("\n🔍 测试角色继承权限...")
        
        # 测试基础员工权限（应该继承）
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "charlie",
            "resource_id": "company_data",
            "resource_type": "data",
            "permission_type": "read"
        })
        
        if result.success:
            access_result = result.result
            print(f"📖 Charlie读取权限（员工级别）: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
        
        # 测试工具执行权限（经理级别，应该继承）
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "charlie",
            "resource_id": "data_analysis_tool",
            "resource_type": "tool",
            "permission_type": "execute"
        })
        
        if result.success:
            access_result = result.result
            print(f"🔧 Charlie工具执行权限（经理级别）: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
        
        # 测试代理管理权限（总监级别）
        result = await rbac_tool.execute({
            "action": "check_access",
            "user_id": "charlie",
            "resource_id": "ai_agent_001",
            "resource_type": "agent",
            "permission_type": "manage"
        })
        
        if result.success:
            access_result = result.result
            print(f"🤖 Charlie代理管理权限（总监级别）: {'✅ 允许' if access_result['granted'] else '❌ 拒绝'}")
        
        # 4. 显示用户的完整权限
        result = await rbac_tool.execute({
            "action": "get_user_permissions",
            "user_id": "charlie"
        })
        
        if result.success:
            permissions = result.result.get('permissions', [])
            print(f"\n📋 Charlie的完整权限列表 (通过角色继承获得):")
            for perm in permissions:
                print(f"   - {perm.get('name', 'Unknown')} ({perm.get('id', 'Unknown')})")
    
    except Exception as e:
        print(f"❌ 角色继承演示过程中发生错误: {e}")


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("🚀 RBAC权限控制系统演示")
    print("=" * 50)
    
    # 演示各个功能
    await demo_basic_rbac()
    await demo_advanced_rbac()
    await demo_role_inheritance()
    
    print("\n✨ 演示完成！")
    print("\n💡 RBAC系统特性总结：")
    print("1. ✅ 基于角色的访问控制")
    print("2. ✅ 角色继承和权限组合")
    print("3. ✅ 细粒度权限控制")
    print("4. ✅ 条件权限（时间、属性等）")
    print("5. ✅ 权限缓存和性能优化")
    print("6. ✅ 审计日志和安全监控")
    print("7. ✅ 灵活的存储后端支持")


if __name__ == "__main__":
    asyncio.run(main())
