#!/usr/bin/env python3
"""
日志分析和告警工具使用示例

演示如何使用智能日志分析、模式识别和告警功能。
"""

import asyncio
from datetime import datetime, timedelta

from ai_agent_framework.tools import LogAnalyzerTool, LogAnalyzerConfig
from ai_agent_framework.utils.logging_system import logging_system


async def demo_log_analysis():
    """演示日志分析功能"""
    print("\n=== 日志分析功能演示 ===")
    
    # 创建日志分析工具
    analyzer_config = LogAnalyzerConfig(
        buffer_size=5000,
        enable_anomaly_detection=True,
        enable_pattern_matching=True,
        enable_performance_analysis=True
    )
    
    log_analyzer = LogAnalyzerTool(analyzer_config)
    
    try:
        # 模拟各种类型的日志
        sample_logs = [
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "用户登录成功 - user_id: 12345",
                "component": "auth_service",
                "source": "web_app",
                "metadata": {"user_id": "12345", "ip": "*************"}
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "Database connection failed: Connection timeout after 30s",
                "component": "database",
                "source": "api_server",
                "metadata": {"connection_pool": "main", "timeout": 30}
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "WARNING",
                "message": "High response time detected: 2500ms for /api/users",
                "component": "api_gateway",
                "source": "load_balancer",
                "metadata": {"response_time": 2500, "endpoint": "/api/users"}
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "CRITICAL",
                "message": "Out of memory error in worker process",
                "component": "worker",
                "source": "background_jobs",
                "metadata": {"memory_usage": "95%", "process_id": 1234}
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "HTTP 500 Internal Server Error - /api/orders",
                "component": "api_server",
                "source": "nginx",
                "metadata": {"status_code": 500, "endpoint": "/api/orders"}
            }
        ]
        
        print("📊 分析示例日志...")
        
        for i, log_data in enumerate(sample_logs, 1):
            print(f"\n--- 分析日志 {i} ---")
            print(f"级别: {log_data['level']}")
            print(f"消息: {log_data['message']}")
            print(f"组件: {log_data['component']}")
            
            # 分析日志
            result = await log_analyzer.execute({
                "action": "analyze_log",
                "log_data": log_data
            })
            
            if result.success:
                analysis = result.result["analysis"]
                alerts = result.result["triggered_alerts"]
                
                print(f"✅ 分析完成")
                
                # 显示匹配的模式
                if analysis["matched_patterns"]:
                    print(f"🔍 匹配模式: {len(analysis['matched_patterns'])}个")
                    for pattern in analysis["matched_patterns"]:
                        print(f"   - {pattern['pattern_name']} ({pattern['category']})")
                
                # 显示检测到的异常
                if analysis["anomalies"]:
                    print(f"⚠️ 检测到异常: {len(analysis['anomalies'])}个")
                    for anomaly in analysis["anomalies"]:
                        print(f"   - {anomaly['type']}: {anomaly['description']}")
                
                # 显示性能指标
                if analysis["metrics"]:
                    print(f"📈 性能指标:")
                    for metric, value in analysis["metrics"].items():
                        print(f"   - {metric}: {value}")
                
                # 显示触发的告警
                if alerts:
                    print(f"🚨 触发告警: {len(alerts)}个")
                    for alert in alerts:
                        print(f"   - {alert['rule_name']} ({alert['severity']}): {alert['message']}")
                
                if not analysis["matched_patterns"] and not analysis["anomalies"] and not alerts:
                    print("ℹ️ 未检测到特殊模式、异常或告警")
            else:
                print(f"❌ 分析失败: {result.error}")
        
        # 获取分析统计
        print("\n📊 获取分析统计...")
        result = await log_analyzer.execute({
            "action": "get_statistics",
            "time_window": 3600  # 最近1小时
        })
        
        if result.success:
            stats = result.result
            print(f"✅ 统计信息:")
            print(f"   总日志数: {stats['total_logs']}")
            print(f"   级别分布: {stats['level_distribution']}")
            print(f"   组件分布: {stats['component_distribution']}")
            if stats['performance_metrics']:
                print(f"   性能指标: {stats['performance_metrics']}")
    
    except Exception as e:
        print(f"❌ 日志分析演示过程中发生错误: {e}")


async def demo_pattern_management():
    """演示模式管理功能"""
    print("\n=== 模式管理功能演示 ===")
    
    log_analyzer = LogAnalyzerTool()
    
    try:
        # 添加自定义日志模式
        print("➕ 添加自定义日志模式...")
        
        custom_patterns = [
            {
                "name": "支付失败",
                "pattern": r"payment.*failed|transaction.*error|billing.*failed",
                "description": "检测支付相关的失败",
                "category": "payment",
                "severity": "ERROR"
            },
            {
                "name": "缓存未命中",
                "pattern": r"cache.*miss|cache.*not found",
                "description": "检测缓存未命中情况",
                "category": "cache",
                "severity": "INFO"
            },
            {
                "name": "API限流",
                "pattern": r"rate.*limit|too many requests|429",
                "description": "检测API限流情况",
                "category": "api",
                "severity": "WARNING"
            }
        ]
        
        pattern_ids = []
        for pattern_config in custom_patterns:
            result = await log_analyzer.execute({
                "action": "add_pattern",
                "pattern_config": pattern_config
            })
            
            if result.success:
                pattern_id = result.result["pattern_id"]
                pattern_ids.append(pattern_id)
                print(f"✅ 添加模式成功: {pattern_config['name']} ({pattern_id[:8]}...)")
            else:
                print(f"❌ 添加模式失败: {result.error}")
        
        # 列出所有模式
        print("\n📋 列出所有日志模式...")
        result = await log_analyzer.execute({
            "action": "list_patterns"
        })
        
        if result.success:
            patterns = result.result["patterns"]
            print(f"📊 共有 {result.result['count']} 个日志模式:")
            for pattern in patterns:
                print(f"   - {pattern['name']} ({pattern['category']}) - 匹配次数: {pattern['count']}")
        
        # 测试自定义模式
        print("\n🧪 测试自定义模式...")
        test_logs = [
            {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "Payment processing failed for order #12345",
                "component": "payment_service"
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "WARNING",
                "message": "Rate limit exceeded for API key abc123",
                "component": "api_gateway"
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "Cache miss for key user:profile:12345",
                "component": "cache_service"
            }
        ]
        
        for log_data in test_logs:
            result = await log_analyzer.execute({
                "action": "analyze_log",
                "log_data": log_data
            })
            
            if result.success:
                analysis = result.result["analysis"]
                if analysis["matched_patterns"]:
                    patterns = [p["pattern_name"] for p in analysis["matched_patterns"]]
                    print(f"✅ 日志匹配模式: {', '.join(patterns)}")
                    print(f"   消息: {log_data['message'][:50]}...")
        
        # 清理：删除添加的模式
        print("\n🗑️ 清理自定义模式...")
        for pattern_id in pattern_ids:
            result = await log_analyzer.execute({
                "action": "remove_pattern",
                "pattern_id": pattern_id
            })
            
            if result.success:
                print(f"✅ 删除模式成功: {pattern_id[:8]}...")
    
    except Exception as e:
        print(f"❌ 模式管理演示过程中发生错误: {e}")


async def demo_alert_management():
    """演示告警管理功能"""
    print("\n=== 告警管理功能演示 ===")
    
    log_analyzer = LogAnalyzerTool()
    
    try:
        # 生成一些会触发告警的日志
        print("🚨 生成触发告警的日志...")
        
        # 生成多个错误日志来触发高错误率告警
        error_logs = []
        for i in range(15):
            error_logs.append({
                "timestamp": (datetime.now() - timedelta(seconds=i*10)).isoformat(),
                "level": "ERROR",
                "message": f"Database query failed: Connection lost #{i}",
                "component": "database_service"
            })
        
        # 生成关键错误日志
        critical_log = {
            "timestamp": datetime.now().isoformat(),
            "level": "CRITICAL",
            "message": "System memory exhausted, shutting down services",
            "component": "system_monitor"
        }
        
        # 分析错误日志
        for log_data in error_logs[:10]:  # 只处理前10个，避免过多输出
            await log_analyzer.execute({
                "action": "analyze_log",
                "log_data": log_data
            })
        
        # 分析关键错误日志
        result = await log_analyzer.execute({
            "action": "analyze_log",
            "log_data": critical_log
        })
        
        if result.success and result.result["triggered_alerts"]:
            print(f"✅ 触发了 {len(result.result['triggered_alerts'])} 个告警")
        
        # 获取活跃告警
        print("\n📋 获取活跃告警...")
        result = await log_analyzer.execute({
            "action": "get_alerts"
        })
        
        if result.success:
            alerts = result.result["alerts"]
            print(f"🚨 当前活跃告警: {result.result['count']} 个")
            
            for alert in alerts:
                print(f"   - {alert['rule_name']} ({alert['severity']})")
                print(f"     消息: {alert['message']}")
                print(f"     状态: {alert['status']}")
                print(f"     触发时间: {alert['triggered_at']}")
                print(f"     触发次数: {alert['count']}")
                
                # 确认第一个告警
                if alert['status'] == 'active':
                    print(f"     ✅ 确认告警: {alert['id'][:8]}...")
                    ack_result = await log_analyzer.execute({
                        "action": "acknowledge_alert",
                        "alert_id": alert["id"],
                        "user_id": "admin"
                    })
                    
                    if ack_result.success:
                        print(f"     ✅ 告警已确认")
                    break
        
        # 获取告警统计
        print("\n📊 获取告警统计...")
        result = await log_analyzer.execute({
            "action": "get_alert_statistics"
        })
        
        if result.success:
            stats = result.result
            print(f"📈 告警统计:")
            print(f"   总告警数: {stats['total_alerts']}")
            print(f"   活跃告警: {stats['active_alerts']}")
            print(f"   最近24小时: {stats['recent_alerts_24h']}")
            print(f"   严重程度分布: {stats['severity_distribution']}")
            print(f"   状态分布: {stats['status_distribution']}")
    
    except Exception as e:
        print(f"❌ 告警管理演示过程中发生错误: {e}")


async def demo_real_time_monitoring():
    """演示实时监控功能"""
    print("\n=== 实时监控功能演示 ===")
    
    log_analyzer = LogAnalyzerTool()
    
    try:
        print("🔄 模拟实时日志流...")
        
        # 模拟不同类型的实时日志
        log_scenarios = [
            # 正常操作
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "User session started - session_id: abc123",
                "component": "session_manager"
            },
            # 性能问题
            {
                "timestamp": datetime.now().isoformat(),
                "level": "WARNING",
                "message": "Slow query detected: SELECT * FROM users took 3500ms",
                "component": "database",
                "metadata": {"response_time": 3500}
            },
            # 安全事件
            {
                "timestamp": datetime.now().isoformat(),
                "level": "WARNING",
                "message": "Multiple failed login attempts from IP *************",
                "component": "auth_service",
                "metadata": {"ip": "*************", "attempts": 5}
            },
            # 系统错误
            {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "File system error: No space left on device /var/log",
                "component": "file_system"
            },
            # 网络问题
            {
                "timestamp": datetime.now().isoformat(),
                "level": "ERROR",
                "message": "Network timeout connecting to external API",
                "component": "api_client"
            }
        ]
        
        for i, log_data in enumerate(log_scenarios, 1):
            print(f"\n--- 处理实时日志 {i} ---")
            print(f"时间: {log_data['timestamp']}")
            print(f"级别: {log_data['level']}")
            print(f"组件: {log_data['component']}")
            print(f"消息: {log_data['message']}")
            
            # 实时分析
            result = await log_analyzer.execute({
                "action": "analyze_log",
                "log_data": log_data
            })
            
            if result.success:
                analysis = result.result["analysis"]
                alerts = result.result["triggered_alerts"]
                
                # 显示实时分析结果
                if analysis["matched_patterns"]:
                    patterns = [p["pattern_name"] for p in analysis["matched_patterns"]]
                    print(f"🔍 匹配模式: {', '.join(patterns)}")
                
                if analysis["anomalies"]:
                    print(f"⚠️ 检测到异常:")
                    for anomaly in analysis["anomalies"]:
                        print(f"   - {anomaly['type']}: {anomaly['description']}")
                
                if alerts:
                    print(f"🚨 触发告警:")
                    for alert in alerts:
                        print(f"   - {alert['severity'].upper()}: {alert['message']}")
                
                if not analysis["matched_patterns"] and not analysis["anomalies"] and not alerts:
                    print("✅ 正常日志，无异常")
            
            # 模拟实时处理间隔
            await asyncio.sleep(0.5)
        
        print("\n📊 实时监控总结...")
        result = await log_analyzer.execute({
            "action": "get_statistics",
            "time_window": 300  # 最近5分钟
        })
        
        if result.success:
            stats = result.result
            print(f"📈 最近5分钟统计:")
            print(f"   处理日志: {stats['total_logs']} 条")
            print(f"   级别分布: {stats['level_distribution']}")
            if stats['error_counts']:
                print(f"   错误统计: {stats['error_counts']}")
    
    except Exception as e:
        print(f"❌ 实时监控演示过程中发生错误: {e}")


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("📊 AI Agent Framework 日志分析和告警工具演示")
    print("=" * 60)
    
    # 演示各个功能
    await demo_log_analysis()
    await demo_pattern_management()
    await demo_alert_management()
    await demo_real_time_monitoring()
    
    print("\n✨ 演示完成！")
    print("\n💡 日志分析和告警工具特性总结：")
    print("1. ✅ 智能日志模式识别")
    print("2. ✅ 实时异常检测")
    print("3. ✅ 性能指标提取")
    print("4. ✅ 多级别告警规则")
    print("5. ✅ 告警确认和解决")
    print("6. ✅ 统计分析和报告")
    print("7. ✅ 自定义模式和规则")
    print("8. ✅ 实时监控和处理")
    
    print("\n📚 支持的日志级别：")
    print("- DEBUG: 调试信息")
    print("- INFO: 一般信息")
    print("- WARNING: 警告信息")
    print("- ERROR: 错误信息")
    print("- CRITICAL: 关键错误")
    
    print("\n🚨 告警严重程度：")
    print("- LOW: 低优先级")
    print("- MEDIUM: 中等优先级")
    print("- HIGH: 高优先级")
    print("- CRITICAL: 关键优先级")


if __name__ == "__main__":
    asyncio.run(main())
