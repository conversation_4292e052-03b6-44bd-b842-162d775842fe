#!/usr/bin/env python3
"""
定时任务调度工具使用示例

演示如何使用定时任务调度和管理功能。
"""

import asyncio
from datetime import datetime, timedelta

from ai_agent_framework.tools import SchedulerTool, SchedulerConfig
from ai_agent_framework.utils.logging_system import logging_system


async def demo_cron_tasks():
    """演示Cron表达式任务"""
    print("\n=== Cron表达式任务演示 ===")
    
    # 创建调度器工具
    scheduler_config = SchedulerConfig(
        auto_start=True,
        max_concurrent_tasks=5
    )
    
    scheduler_tool = SchedulerTool(scheduler_config)
    
    try:
        # 1. 创建每分钟执行的任务
        print("📅 创建每分钟执行的任务...")
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "每分钟报告",
                "description": "每分钟输出当前时间",
                "trigger_type": "cron",
                "trigger_config": {
                    "expression": "* * * * *"  # 每分钟执行
                },
                "task_config": {
                    "type": "custom",
                    "message": "定时报告：当前时间"
                },
                "max_runs": 5,  # 最多执行5次
                "timeout_seconds": 30
            }
        })
        
        if result.success:
            task_id_1 = result.result["task_id"]
            print(f"✅ 任务创建成功: {task_id_1}")
            print(f"📅 下次执行时间: {result.result['next_run']}")
        else:
            print(f"❌ 任务创建失败: {result.error}")
        
        # 2. 创建每天上午9点执行的任务
        print("\n📅 创建每天上午9点执行的任务...")
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "每日报告",
                "description": "每天上午9点生成日报",
                "trigger_type": "cron",
                "trigger_config": {
                    "expression": "0 9 * * *"  # 每天上午9点
                },
                "task_config": {
                    "type": "custom",
                    "report_type": "daily",
                    "recipients": ["<EMAIL>"]
                },
                "timeout_seconds": 300
            }
        })
        
        if result.success:
            task_id_2 = result.result["task_id"]
            print(f"✅ 任务创建成功: {task_id_2}")
            print(f"📅 下次执行时间: {result.result['next_run']}")
        
        # 3. 创建每周一上午10点执行的任务
        print("\n📅 创建每周一上午10点执行的任务...")
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "周报生成",
                "description": "每周一上午10点生成周报",
                "trigger_type": "cron",
                "trigger_config": {
                    "expression": "0 10 * * 1"  # 每周一上午10点
                },
                "task_config": {
                    "type": "custom",
                    "report_type": "weekly"
                }
            }
        })
        
        if result.success:
            print(f"✅ 周报任务创建成功: {result.result['task_id']}")
        
        # 4. 列出所有任务
        print("\n📋 列出所有任务...")
        result = await scheduler_tool.execute({
            "action": "list_tasks"
        })
        
        if result.success:
            tasks = result.result["tasks"]
            print(f"📊 共有 {result.result['count']} 个任务:")
            for task in tasks:
                print(f"   - {task['name']} ({task['id'][:8]}...)")
                print(f"     状态: {task['status']}")
                print(f"     下次执行: {task['next_run'] or '无'}")
        
        # 等待一段时间观察任务执行
        print("\n⏳ 等待任务执行...")
        await asyncio.sleep(70)  # 等待70秒
        
        # 查看执行记录
        print("\n📊 查看执行记录...")
        result = await scheduler_tool.execute({
            "action": "get_executions",
            "task_id": task_id_1
        })
        
        if result.success:
            executions = result.result["executions"]
            print(f"📈 任务 {task_id_1[:8]}... 执行了 {len(executions)} 次:")
            for exec in executions[-3:]:  # 显示最近3次执行
                status = "✅ 成功" if exec["status"] == "completed" else "❌ 失败"
                print(f"   - {exec['start_time']}: {status}")
    
    except Exception as e:
        print(f"❌ Cron任务演示过程中发生错误: {e}")
    
    finally:
        # 停止调度器
        await scheduler_tool.execute({"action": "stop_scheduler"})


async def demo_interval_tasks():
    """演示间隔任务"""
    print("\n=== 间隔任务演示 ===")
    
    scheduler_tool = SchedulerTool()
    
    try:
        # 启动调度器
        await scheduler_tool.execute({"action": "start_scheduler"})
        
        # 创建每30秒执行的任务
        print("⏰ 创建每30秒执行的任务...")
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "心跳检查",
                "description": "每30秒检查系统状态",
                "trigger_type": "interval",
                "trigger_config": {
                    "seconds": 30
                },
                "task_config": {
                    "type": "custom",
                    "check_type": "heartbeat"
                },
                "max_runs": 3
            }
        })
        
        if result.success:
            task_id = result.result["task_id"]
            print(f"✅ 间隔任务创建成功: {task_id}")
        
        # 等待任务执行
        print("\n⏳ 等待任务执行...")
        await asyncio.sleep(100)  # 等待100秒
        
        # 查看任务状态
        result = await scheduler_tool.execute({
            "action": "get_task",
            "task_id": task_id
        })
        
        if result.success:
            task = result.result["task"]
            print(f"📊 任务状态: {task['status']}")
            print(f"📈 执行次数: {task['run_count']}")
    
    except Exception as e:
        print(f"❌ 间隔任务演示过程中发生错误: {e}")
    
    finally:
        await scheduler_tool.execute({"action": "stop_scheduler"})


async def demo_http_tasks():
    """演示HTTP请求任务"""
    print("\n=== HTTP请求任务演示 ===")
    
    scheduler_tool = SchedulerTool()
    
    try:
        await scheduler_tool.execute({"action": "start_scheduler"})
        
        # 创建HTTP健康检查任务
        print("🌐 创建HTTP健康检查任务...")
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "API健康检查",
                "description": "每2分钟检查API服务状态",
                "trigger_type": "interval",
                "trigger_config": {
                    "seconds": 120
                },
                "task_config": {
                    "type": "http_request",
                    "url": "https://httpbin.org/status/200",
                    "method": "GET",
                    "headers": {
                        "User-Agent": "AI-Agent-Scheduler/1.0"
                    }
                },
                "timeout_seconds": 30,
                "max_retries": 2
            }
        })
        
        if result.success:
            task_id = result.result["task_id"]
            print(f"✅ HTTP任务创建成功: {task_id}")
            
            # 立即执行一次任务进行测试
            print("\n🚀 立即执行任务进行测试...")
            # 注意：这里需要实现run_task_now功能
            
            # 等待一段时间
            await asyncio.sleep(10)
            
            # 查看执行记录
            result = await scheduler_tool.execute({
                "action": "get_executions",
                "task_id": task_id
            })
            
            if result.success:
                executions = result.result["executions"]
                if executions:
                    latest_exec = executions[-1]
                    print(f"📊 最新执行结果:")
                    print(f"   状态: {latest_exec['status']}")
                    if latest_exec.get('result'):
                        print(f"   HTTP状态码: {latest_exec['result'].get('status_code', 'N/A')}")
    
    except Exception as e:
        print(f"❌ HTTP任务演示过程中发生错误: {e}")
    
    finally:
        await scheduler_tool.execute({"action": "stop_scheduler"})


async def demo_task_management():
    """演示任务管理功能"""
    print("\n=== 任务管理功能演示 ===")
    
    scheduler_tool = SchedulerTool()
    
    try:
        await scheduler_tool.execute({"action": "start_scheduler"})
        
        # 创建一个测试任务
        result = await scheduler_tool.execute({
            "action": "create_task",
            "task_config": {
                "name": "管理测试任务",
                "description": "用于演示任务管理功能",
                "trigger_type": "interval",
                "trigger_config": {
                    "seconds": 60
                },
                "task_config": {
                    "type": "custom",
                    "action": "test"
                }
            }
        })
        
        if result.success:
            task_id = result.result["task_id"]
            print(f"✅ 测试任务创建成功: {task_id}")
            
            # 暂停任务
            print("\n⏸️ 暂停任务...")
            result = await scheduler_tool.execute({
                "action": "pause_task",
                "task_id": task_id
            })
            
            if result.success:
                print("✅ 任务已暂停")
            
            # 查看任务状态
            result = await scheduler_tool.execute({
                "action": "get_task",
                "task_id": task_id
            })
            
            if result.success:
                task = result.result["task"]
                print(f"📊 任务状态: {task['status']}")
            
            # 恢复任务
            print("\n▶️ 恢复任务...")
            result = await scheduler_tool.execute({
                "action": "resume_task",
                "task_id": task_id
            })
            
            if result.success:
                print("✅ 任务已恢复")
                print(f"📅 下次执行时间: {result.result['next_run']}")
            
            # 删除任务
            print("\n🗑️ 删除任务...")
            result = await scheduler_tool.execute({
                "action": "delete_task",
                "task_id": task_id
            })
            
            if result.success:
                print("✅ 任务已删除")
        
        # 查看调度器状态
        print("\n📊 查看调度器状态...")
        result = await scheduler_tool.execute({
            "action": "scheduler_status"
        })
        
        if result.success:
            status = result.result
            print(f"🔄 调度器运行状态: {'运行中' if status['running'] else '已停止'}")
            print(f"📋 总任务数: {status['total_tasks']}")
            print(f"🏃 运行中任务: {status['running_tasks']}")
            print(f"⏳ 等待中任务: {status['pending_tasks']}")
    
    except Exception as e:
        print(f"❌ 任务管理演示过程中发生错误: {e}")
    
    finally:
        await scheduler_tool.execute({"action": "stop_scheduler"})


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("⏰ AI Agent Framework 定时任务调度工具演示")
    print("=" * 50)
    
    # 演示各个功能
    await demo_cron_tasks()
    await demo_interval_tasks()
    await demo_http_tasks()
    await demo_task_management()
    
    print("\n✨ 演示完成！")
    print("\n💡 定时任务调度工具特性总结：")
    print("1. ✅ Cron表达式支持（分 时 日 月 周）")
    print("2. ✅ 固定间隔任务")
    print("3. ✅ 一次性和指定时间任务")
    print("4. ✅ HTTP请求任务")
    print("5. ✅ Shell命令任务")
    print("6. ✅ Python函数任务")
    print("7. ✅ 任务暂停、恢复、删除")
    print("8. ✅ 执行记录和状态跟踪")
    print("9. ✅ 重试机制和错误处理")
    print("10. ✅ 并发执行控制")
    
    print("\n📚 Cron表达式示例：")
    print("# 每分钟: * * * * *")
    print("# 每小时: 0 * * * *")
    print("# 每天午夜: 0 0 * * *")
    print("# 每周一上午9点: 0 9 * * 1")
    print("# 每月1号: 0 0 1 * *")


if __name__ == "__main__":
    asyncio.run(main())
