#!/usr/bin/env python3
"""
OAuth2.0认证工具使用示例

演示如何使用OAuth2.0认证工具进行用户认证和API访问授权。
"""

import asyncio
import json
from typing import Dict, Any

from ai_agent_framework.tools import OAuth2Tool, OAuth2Provider


async def demo_oauth2_configuration():
    """演示OAuth2.0配置"""
    print("\n🔧 OAuth2.0配置演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 使用预定义提供商配置（Google）
    result = await oauth2_tool.execute({
        "action": "configure",
        "provider": "google",
        "client_id": "your-google-client-id.apps.googleusercontent.com",
        "client_secret": "your-google-client-secret",
        "redirect_uri": "http://localhost:8080/callback",
        "scope": ["openid", "email", "profile"]
    })
    
    if result.success:
        print("✅ Google OAuth2.0配置成功")
        print(f"  提供商: {result.result['provider']}")
        print(f"  客户端ID: {result.result['client_id']}")
        print(f"  授权范围: {result.result['scope']}")
    else:
        print(f"❌ 配置失败: {result.error}")
    
    # 使用自定义配置
    result = await oauth2_tool.execute({
        "action": "configure",
        "config": {
            "client_id": "custom-api-client",
            "client_secret": "custom-api-secret",
            "authorization_url": "https://api.example.com/oauth2/authorize",
            "token_url": "https://api.example.com/oauth2/token",
            "redirect_uri": "http://localhost:8080/callback",
            "scope": ["read", "write", "admin"],
            "provider": "custom"
        }
    })
    
    if result.success:
        print("✅ 自定义OAuth2.0配置成功")
        print(f"  提供商: {result.result['provider']}")
    else:
        print(f"❌ 自定义配置失败: {result.error}")


async def demo_authorization_code_flow():
    """演示授权码流程"""
    print("\n🔐 授权码流程演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 配置GitHub OAuth2.0
    await oauth2_tool.execute({
        "action": "configure",
        "provider": "github",
        "client_id": "your-github-client-id",
        "client_secret": "your-github-client-secret",
        "redirect_uri": "http://localhost:8080/callback",
        "scope": ["user:email", "repo"]
    })
    
    # 获取授权URL
    result = await oauth2_tool.execute({
        "action": "get_auth_url"
    })
    
    if result.success:
        print("✅ 授权URL生成成功")
        print(f"  授权URL: {result.result['auth_url']}")
        print(f"  状态参数: {result.result['state']}")
        print("\n📋 用户需要访问上述URL进行授权")
        print("   授权完成后，将获得授权码用于下一步")
        
        # 模拟授权码交换（实际使用时，授权码来自回调）
        print("\n🔄 模拟授权码交换...")
        print("   （实际使用时，这里应该是从回调URL获取的真实授权码）")
        
        # 注意：这里使用模拟的授权码，实际使用时会失败
        # 真实场景中，用户完成授权后会重定向到redirect_uri，携带授权码
        mock_auth_code = "mock_authorization_code_from_callback"
        
        result = await oauth2_tool.execute({
            "action": "exchange_code",
            "code": mock_auth_code,
            "state": result.result['state']
        })
        
        if result.success:
            print("✅ 授权码交换成功")
            print(f"  令牌ID: {result.result['token_id']}")
            print(f"  访问令牌: {result.result['token']['access_token'][:20]}...")
            print(f"  令牌类型: {result.result['token']['token_type']}")
            print(f"  过期时间: {result.result['token']['expires_in']} 秒")
        else:
            print(f"⚠️ 授权码交换失败（预期的，因为使用了模拟授权码）: {result.error}")
    else:
        print(f"❌ 授权URL生成失败: {result.error}")


async def demo_client_credentials_flow():
    """演示客户端凭证流程"""
    print("\n🤖 客户端凭证流程演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 配置API服务的OAuth2.0
    await oauth2_tool.execute({
        "action": "configure",
        "config": {
            "client_id": "api-service-client",
            "client_secret": "api-service-secret",
            "authorization_url": "https://auth.api-service.com/oauth2/authorize",
            "token_url": "https://auth.api-service.com/oauth2/token",
            "scope": ["api:read", "api:write"],
            "provider": "custom"
        }
    })
    
    # 执行客户端凭证流程
    result = await oauth2_tool.execute({
        "action": "client_credentials"
    })
    
    if result.success:
        print("✅ 客户端凭证授权成功")
        print(f"  令牌ID: {result.result['token_id']}")
        print(f"  访问令牌: {result.result['token']['access_token'][:20]}...")
        print(f"  令牌类型: {result.result['token']['token_type']}")
        print(f"  过期时间: {result.result['token']['expires_in']} 秒")
        
        # 验证令牌
        token_id = result.result['token_id']
        validation_result = await oauth2_tool.execute({
            "action": "validate_token",
            "token_id": token_id
        })
        
        if validation_result.success:
            print("✅ 令牌验证成功")
            print(f"  令牌有效: {validation_result.result['valid']}")
            print(f"  验证信息: {json.dumps(validation_result.result['validation_info'], indent=2, ensure_ascii=False)}")
        
    else:
        print(f"⚠️ 客户端凭证授权失败（预期的，因为使用了模拟配置）: {result.error}")


async def demo_token_management():
    """演示令牌管理"""
    print("\n🎫 令牌管理演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 配置
    await oauth2_tool.execute({
        "action": "configure",
        "provider": "microsoft",
        "client_id": "microsoft-client-id",
        "client_secret": "microsoft-client-secret",
        "redirect_uri": "http://localhost:8080/callback",
        "scope": ["openid", "email", "profile"]
    })
    
    # 模拟已有的令牌
    mock_access_token = "mock_access_token_for_validation"
    mock_refresh_token = "mock_refresh_token_for_refresh"
    
    # 验证令牌
    result = await oauth2_tool.execute({
        "action": "validate_token",
        "access_token": mock_access_token
    })
    
    if result.success:
        print("✅ 令牌验证完成")
        print(f"  令牌有效: {result.result['valid']}")
        print(f"  本地验证: {result.result['validation_info']['local_validation']}")
    
    # 刷新令牌
    result = await oauth2_tool.execute({
        "action": "refresh_token",
        "refresh_token": mock_refresh_token
    })
    
    if result.success:
        print("✅ 令牌刷新成功")
        print(f"  新令牌ID: {result.result['token_id']}")
        print(f"  新访问令牌: {result.result['token']['access_token'][:20]}...")
    else:
        print(f"⚠️ 令牌刷新失败（预期的，因为使用了模拟令牌）: {result.error}")
    
    # 撤销令牌
    result = await oauth2_tool.execute({
        "action": "revoke_token",
        "access_token": mock_access_token
    })
    
    if result.success:
        print("✅ 令牌撤销成功")
        print(f"  撤销方法: {result.result['method']}")
        print(f"  消息: {result.result['message']}")


async def demo_user_info_retrieval():
    """演示用户信息获取"""
    print("\n👤 用户信息获取演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 配置不同的提供商
    providers = [
        ("google", "Google"),
        ("github", "GitHub"),
        ("microsoft", "Microsoft"),
        ("facebook", "Facebook")
    ]
    
    for provider_key, provider_name in providers:
        print(f"\n📋 {provider_name} 用户信息端点:")
        
        await oauth2_tool.execute({
            "action": "configure",
            "provider": provider_key,
            "client_id": f"{provider_key}-client-id",
            "client_secret": f"{provider_key}-client-secret"
        })
        
        # 模拟获取用户信息
        result = await oauth2_tool.execute({
            "action": "get_user_info",
            "access_token": f"mock_{provider_key}_access_token"
        })
        
        if result.success:
            print(f"  ✅ {provider_name} 用户信息获取成功")
            print(f"     用户信息: {result.result['user_info']}")
        else:
            print(f"  ⚠️ {provider_name} 用户信息获取失败（预期的）: {result.error}")


async def demo_device_flow():
    """演示设备流程"""
    print("\n📱 设备流程演示")
    print("-" * 40)
    
    oauth2_tool = OAuth2Tool()
    
    # 配置支持设备流程的提供商
    await oauth2_tool.execute({
        "action": "configure",
        "config": {
            "client_id": "device-flow-client",
            "client_secret": "device-flow-secret",
            "authorization_url": "https://auth.example.com/oauth2/authorize",
            "token_url": "https://auth.example.com/oauth2/token",
            "scope": ["device:read", "device:control"],
            "provider": "custom",
            "device_auth_url": "https://auth.example.com/oauth2/device/code"
        }
    })
    
    # 启动设备流程
    result = await oauth2_tool.execute({
        "action": "device_flow"
    })
    
    if result.success:
        print("✅ 设备流程启动成功")
        print(f"  设备码: {result.result['device_code']}")
        print(f"  用户码: {result.result['user_code']}")
        print(f"  验证URL: {result.result['verification_uri']}")
        print(f"  过期时间: {result.result['expires_in']} 秒")
        print(f"  轮询间隔: {result.result['interval']} 秒")
        
        print("\n📋 用户需要:")
        print(f"   1. 访问 {result.result['verification_uri']}")
        print(f"   2. 输入用户码: {result.result['user_code']}")
        print("   3. 完成授权")
        
        # 模拟轮询令牌
        device_code = result.result['device_code']
        
        print("\n🔄 模拟轮询令牌...")
        poll_result = await oauth2_tool.execute({
            "action": "device_flow",
            "device_code": device_code
        })
        
        if poll_result.success:
            if "authorization_pending" in poll_result.result:
                print("  ⏳ 用户尚未完成授权，需要继续轮询")
            elif "device_flow_completed" in poll_result.result:
                print("  ✅ 设备流程完成")
                print(f"     令牌ID: {poll_result.result['token_id']}")
        else:
            print(f"  ⚠️ 轮询失败（预期的）: {poll_result.error}")
    else:
        print(f"⚠️ 设备流程启动失败（预期的）: {result.error}")


async def main():
    """主函数"""
    print("🚀 OAuth2.0认证工具使用示例")
    print("=" * 50)
    
    print("📋 使用前请注意:")
    print("1. 需要安装httpx库: pip install httpx")
    print("2. 需要在各OAuth2.0提供商处注册应用获取客户端ID和密钥")
    print("3. 示例中的配置为演示用途，实际使用时请替换为真实配置")
    print("4. 某些操作会因为使用模拟数据而失败，这是正常的")
    
    try:
        # 运行演示
        await demo_oauth2_configuration()
        await demo_authorization_code_flow()
        await demo_client_credentials_flow()
        await demo_token_management()
        await demo_user_info_retrieval()
        await demo_device_flow()
        
        print("\n🎉 所有OAuth2.0认证工具演示完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
