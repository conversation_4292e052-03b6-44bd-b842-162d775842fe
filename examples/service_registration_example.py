#!/usr/bin/env python3
"""
AI Agent Framework 服务注册示例

这个示例展示了如何将现有的微服务或API快速注册为AI Agent可调用的工具。
包括服务发现、认证配置、工具注册和使用的完整流程。
"""

import asyncio
import os
from pathlib import Path

from ai_agent_framework.core.agent import Agent
from ai_agent_framework.core.config import FrameworkConfig
from ai_agent_framework.models.openai_adapter import OpenAIAdapter
from ai_agent_framework.tools.service_registry_tool import ServiceRegistryTool
from ai_agent_framework.tools.service_discovery import ServiceDiscoveryTool
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager
from ai_agent_framework.tools.service_adapter import ServiceToolFactory
from ai_agent_framework.utils.tool_registry import ToolRegistry


async def main():
    """主函数：演示完整的服务注册和使用流程"""
    
    print("🚀 AI Agent Framework 服务注册示例")
    print("=" * 50)
    
    # 1. 初始化框架组件
    print("\n📋 1. 初始化框架组件...")
    
    # 创建配置
    config = FrameworkConfig()
    
    # 创建工具注册表
    tool_registry = ToolRegistry()
    
    # 创建认证管理器
    auth_manager = ServiceAuthManager()
    
    # 创建服务注册工具
    service_registry = ServiceRegistryTool()
    
    # 创建服务发现工具
    service_discovery = ServiceDiscoveryTool()
    
    # 创建服务工具工厂
    tool_factory = ServiceToolFactory(auth_manager)
    
    # 注册管理工具到工具注册表
    tool_registry.register_tool(service_registry)
    tool_registry.register_tool(service_discovery)
    tool_registry.register_tool(auth_manager)
    
    print("✅ 框架组件初始化完成")
    
    # 2. 服务发现示例
    print("\n🔍 2. 服务发现示例...")
    
    # 发现本地服务（如果有的话）
    discovery_result = await service_discovery.execute({
        "action": "scan",
        "target": "localhost",
        "options": {
            "port_range": "8000-8010",
            "timeout": 2
        }
    })
    
    if discovery_result.success:
        discovered_services = discovery_result.result.get("discovered_services", [])
        print(f"✅ 发现了 {len(discovered_services)} 个本地服务")
        for service in discovered_services:
            print(f"   - {service['url']} (状态: {service['status']})")
    else:
        print("⚠️  未发现本地服务，将使用预配置的示例服务")
    
    # 3. 注册示例服务
    print("\n📝 3. 注册示例服务...")
    
    # 注册天气服务（使用预配置的配置文件）
    weather_config_file = Path("configs/services/weather_service.yaml")
    if weather_config_file.exists():
        register_result = await service_registry.execute({
            "action": "register",
            "config_file": str(weather_config_file)
        })
        
        if register_result.success:
            print("✅ 天气服务注册成功")
        else:
            print(f"❌ 天气服务注册失败: {register_result.error}")
    
    # 注册一个简单的REST API服务示例
    simple_service_config = {
        "name": "httpbin_service",
        "description": "HTTPBin测试服务，用于API测试",
        "base_url": "https://httpbin.org",
        "service_type": "rest_api",
        "version": "1.0.0",
        "auth": {
            "type": "none"
        },
        "endpoints": [
            {
                "name": "get_ip",
                "path": "/ip",
                "method": "GET",
                "description": "获取客户端IP地址",
                "requires_auth": False,
                "timeout": 10.0,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "post_data",
                "path": "/post",
                "method": "POST",
                "description": "发送POST数据并返回请求信息",
                "requires_auth": False,
                "timeout": 15.0,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "要发送的消息"
                        },
                        "data": {
                            "type": "object",
                            "description": "要发送的数据"
                        }
                    },
                    "required": ["message"]
                }
            }
        ],
        "timeout": 30.0,
        "max_retries": 3,
        "headers": {
            "User-Agent": "AI-Agent-Framework/1.0"
        },
        "health_check_path": "/status/200",
        "tags": ["test", "api", "example"]
    }
    
    register_result = await service_registry.execute({
        "action": "register",
        "service_config": simple_service_config,
        "test_url": "https://httpbin.org/status/200"
    })
    
    if register_result.success:
        print("✅ HTTPBin测试服务注册成功")
        print(f"   测试结果: {register_result.result.get('test_result', {})}")
    else:
        print(f"❌ HTTPBin测试服务注册失败: {register_result.error}")
    
    # 4. 列出已注册的服务
    print("\n📋 4. 列出已注册的服务...")
    
    list_result = await service_registry.execute({"action": "list"})
    if list_result.success:
        services = list_result.result.get("services", [])
        print(f"✅ 找到 {len(services)} 个已注册的服务:")
        for service in services:
            status = "🟢 健康" if service.get("is_healthy") else "🔴 不健康"
            print(f"   - {service['name']}: {service['description']} ({status})")
            print(f"     URL: {service['base_url']}")
            print(f"     端点数量: {service['endpoints_count']}")
    
    # 5. 创建动态服务工具
    print("\n🔧 5. 创建动态服务工具...")
    
    # 从配置文件创建工具
    if weather_config_file.exists():
        weather_tools = tool_factory.create_tools_from_config_file(weather_config_file)
        print(f"✅ 为天气服务创建了 {len(weather_tools)} 个工具")
        
        # 注册工具到工具注册表
        for tool in weather_tools:
            tool_registry.register_tool(tool)
            print(f"   - 注册工具: {tool.name}")
    
    # 为HTTPBin服务创建工具
    from ai_agent_framework.tools.service_registry_tool import ServiceConfig
    httpbin_config = ServiceConfig(**simple_service_config)
    httpbin_tools = tool_factory.create_tools_from_config(httpbin_config)
    print(f"✅ 为HTTPBin服务创建了 {len(httpbin_tools)} 个工具")
    
    # 注册工具到工具注册表
    for tool in httpbin_tools:
        tool_registry.register_tool(tool)
        print(f"   - 注册工具: {tool.name}")
    
    # 6. 创建AI Agent并测试服务调用
    print("\n🤖 6. 创建AI Agent并测试服务调用...")
    
    # 检查是否有OpenAI API密钥
    if os.getenv("OPENAI_API_KEY"):
        try:
            # 创建模型适配器
            model = OpenAIAdapter(
                api_key=os.getenv("OPENAI_API_KEY"),
                model_name="gpt-3.5-turbo"
            )
            
            # 创建Agent
            agent = Agent(
                model=model,
                tool_registry=tool_registry,
                config=config
            )
            
            print("✅ AI Agent创建成功")
            
            # 测试服务调用
            print("\n🧪 测试服务调用...")
            
            # 测试获取IP地址
            response = await agent.process_message(
                "请帮我获取当前的IP地址",
                session_id="test_session"
            )
            
            print(f"Agent响应: {response.content}")
            
            # 测试发送POST数据
            response = await agent.process_message(
                "请向HTTPBin发送一条测试消息：'Hello from AI Agent!'",
                session_id="test_session"
            )
            
            print(f"Agent响应: {response.content}")
            
        except Exception as e:
            print(f"❌ AI Agent测试失败: {str(e)}")
            print("💡 提示: 请设置OPENAI_API_KEY环境变量")
    else:
        print("⚠️  未设置OPENAI_API_KEY，跳过AI Agent测试")
        print("💡 提示: 设置环境变量后可以测试完整的AI Agent功能")
    
    # 7. 直接测试工具调用
    print("\n🔧 7. 直接测试工具调用...")
    
    # 获取HTTPBin IP工具
    ip_tool = tool_registry.get_tool("httpbin_service_get_ip")
    if ip_tool:
        try:
            result = await ip_tool.execute({})
            if result.success:
                print("✅ 获取IP地址成功:")
                print(f"   结果: {result.result}")
            else:
                print(f"❌ 获取IP地址失败: {result.error}")
        except Exception as e:
            print(f"❌ 工具调用异常: {str(e)}")
    
    # 测试POST工具
    post_tool = tool_registry.get_tool("httpbin_service_post_data")
    if post_tool:
        try:
            result = await post_tool.execute({
                "message": "Hello from AI Agent Framework!",
                "data": {"timestamp": "2024-01-01", "source": "service_registration_example"}
            })
            if result.success:
                print("✅ 发送POST数据成功:")
                print(f"   状态码: {result.result.get('status_code')}")
                print(f"   响应数据: {result.result.get('data', {}).get('json', {})}")
            else:
                print(f"❌ 发送POST数据失败: {result.error}")
        except Exception as e:
            print(f"❌ 工具调用异常: {str(e)}")
    
    print("\n🎉 服务注册示例完成!")
    print("=" * 50)
    print("💡 总结:")
    print("   1. 成功演示了服务发现功能")
    print("   2. 注册了多个外部服务")
    print("   3. 创建了动态服务工具")
    print("   4. 测试了工具调用功能")
    print("   5. 展示了完整的集成流程")
    print("\n📚 更多信息请参考:")
    print("   - 配置文件模板: configs/services/service_template.yaml")
    print("   - 天气服务示例: configs/services/weather_service.yaml")
    print("   - API文档: docs/api-reference.md")


if __name__ == "__main__":
    asyncio.run(main())
