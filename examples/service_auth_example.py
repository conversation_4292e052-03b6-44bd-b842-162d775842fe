#!/usr/bin/env python3
"""
服务认证配置示例

展示如何为不同类型的服务配置认证，包括API Key、OAuth2、JWT等。
"""

import asyncio
import os
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager
from ai_agent_framework.tools.service_registry_tool import ServiceRegistryTool


async def demo_api_key_auth():
    """演示API Key认证"""
    print("🔑 API Key认证示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 存储API Key认证凭据
    result = await auth_manager.execute({
        "action": "store",
        "service_name": "weather_api",
        "auth_type": "api_key",
        "credentials": {
            "api_key": "your-api-key-here",
            "api_key_header": "X-API-Key"
        }
    })
    
    if result.success:
        print("✅ API Key认证凭据存储成功")
    else:
        print(f"❌ 存储失败: {result.error}")
    
    # 获取认证头部
    get_result = await auth_manager.execute({
        "action": "get",
        "service_name": "weather_api"
    })
    
    if get_result.success:
        auth_headers = get_result.result.get("auth_headers", {})
        print(f"✅ 生成的认证头部: {auth_headers}")
    else:
        print(f"❌ 获取认证头部失败: {get_result.error}")


async def demo_oauth2_auth():
    """演示OAuth2认证"""
    print("\n🔐 OAuth2认证示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 存储OAuth2认证凭据
    result = await auth_manager.execute({
        "action": "store",
        "service_name": "github_api",
        "auth_type": "oauth2",
        "credentials": {
            "client_id": "your-client-id",
            "client_secret": "your-client-secret",
            "token_url": "https://github.com/login/oauth/access_token",
            "scope": "repo user"
        }
    })
    
    if result.success:
        print("✅ OAuth2认证凭据存储成功")
        
        # 尝试刷新令牌（获取访问令牌）
        refresh_result = await auth_manager.execute({
            "action": "refresh",
            "service_name": "github_api"
        })
        
        if refresh_result.success:
            print("✅ OAuth2令牌获取成功")
            token_info = refresh_result.result.get("new_token", {})
            print(f"   访问令牌: {token_info.get('access_token', 'N/A')[:20]}...")
        else:
            print(f"⚠️  OAuth2令牌获取失败: {refresh_result.error}")
            print("💡 这是正常的，因为需要真实的客户端凭据")
    else:
        print(f"❌ 存储失败: {result.error}")


async def demo_basic_auth():
    """演示Basic认证"""
    print("\n🔒 Basic认证示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 存储Basic认证凭据
    result = await auth_manager.execute({
        "action": "store",
        "service_name": "private_api",
        "auth_type": "basic_auth",
        "credentials": {
            "username": "admin",
            "password": "secret123"
        }
    })
    
    if result.success:
        print("✅ Basic认证凭据存储成功")
        
        # 获取认证头部
        get_result = await auth_manager.execute({
            "action": "get",
            "service_name": "private_api"
        })
        
        if get_result.success:
            auth_headers = get_result.result.get("auth_headers", {})
            print(f"✅ 生成的认证头部: {list(auth_headers.keys())}")
            print(f"   Authorization头部已生成（Base64编码）")
        else:
            print(f"❌ 获取认证头部失败: {get_result.error}")
    else:
        print(f"❌ 存储失败: {result.error}")


async def demo_jwt_auth():
    """演示JWT认证"""
    print("\n🎫 JWT认证示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 存储JWT认证凭据
    result = await auth_manager.execute({
        "action": "store",
        "service_name": "microservice_api",
        "auth_type": "jwt",
        "credentials": {
            "jwt_secret": "your-jwt-secret-key",
            "jwt_algorithm": "HS256",
            "jwt_payload": {
                "sub": "ai-agent",
                "role": "service"
            }
        }
    })
    
    if result.success:
        print("✅ JWT认证凭据存储成功")
        
        # 获取认证头部（会生成JWT令牌）
        get_result = await auth_manager.execute({
            "action": "get",
            "service_name": "microservice_api"
        })
        
        if get_result.success:
            auth_headers = get_result.result.get("auth_headers", {})
            if "Authorization" in auth_headers:
                jwt_token = auth_headers["Authorization"].replace("Bearer ", "")
                print(f"✅ JWT令牌生成成功: {jwt_token[:30]}...")
            else:
                print("⚠️  JWT令牌生成失败")
        else:
            print(f"❌ 获取认证头部失败: {get_result.error}")
    else:
        print(f"❌ 存储失败: {result.error}")


async def demo_custom_auth():
    """演示自定义认证"""
    print("\n🛠️ 自定义认证示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 存储自定义认证凭据
    result = await auth_manager.execute({
        "action": "store",
        "service_name": "custom_api",
        "auth_type": "custom",
        "credentials": {
            "custom_headers": {
                "X-Custom-Auth": "custom-token-value",
                "X-Client-ID": "ai-agent-client",
                "X-Timestamp": "2024-01-01T00:00:00Z"
            }
        }
    })
    
    if result.success:
        print("✅ 自定义认证凭据存储成功")
        
        # 获取认证头部
        get_result = await auth_manager.execute({
            "action": "get",
            "service_name": "custom_api"
        })
        
        if get_result.success:
            auth_headers = get_result.result.get("auth_headers", {})
            print(f"✅ 生成的自定义认证头部:")
            for header, value in auth_headers.items():
                print(f"   {header}: {value}")
        else:
            print(f"❌ 获取认证头部失败: {get_result.error}")
    else:
        print(f"❌ 存储失败: {result.error}")


async def demo_auth_with_service():
    """演示带认证的服务注册"""
    print("\n🔗 带认证的服务注册示例")
    print("-" * 30)
    
    auth_manager = ServiceAuthManager()
    service_registry = ServiceRegistryTool()
    
    # 1. 先配置认证
    auth_result = await auth_manager.execute({
        "action": "store",
        "service_name": "protected_api",
        "auth_type": "api_key",
        "credentials": {
            "api_key": "demo-api-key-12345",
            "api_key_header": "Authorization"
        }
    })
    
    if auth_result.success:
        print("✅ 认证配置完成")
    else:
        print(f"❌ 认证配置失败: {auth_result.error}")
        return
    
    # 2. 注册需要认证的服务
    service_config = {
        "name": "protected_api",
        "description": "需要API Key认证的受保护API",
        "base_url": "https://api.example.com",
        "service_type": "rest_api",
        "auth": {
            "type": "api_key",
            "api_key": "${API_KEY}",
            "api_key_header": "Authorization"
        },
        "endpoints": [
            {
                "name": "get_protected_data",
                "path": "/protected/data",
                "method": "GET",
                "description": "获取受保护的数据",
                "requires_auth": True,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        ]
    }
    
    register_result = await service_registry.execute({
        "action": "register",
        "service_config": service_config
    })
    
    if register_result.success:
        print("✅ 带认证的服务注册成功")
        print("💡 现在可以创建工具并使用认证管理器来处理认证")
    else:
        print(f"❌ 服务注册失败: {register_result.error}")


async def demo_list_and_manage_credentials():
    """演示凭据管理"""
    print("\n📋 凭据管理示例")
    print("-" * 20)
    
    auth_manager = ServiceAuthManager()
    
    # 列出所有凭据
    list_result = await auth_manager.execute({"action": "list"})
    
    if list_result.success:
        credentials = list_result.result.get("credentials", [])
        print(f"✅ 找到 {len(credentials)} 个已存储的认证凭据:")
        
        for cred in credentials:
            service_name = cred.get("service_name")
            auth_type = cred.get("auth_type")
            is_expired = cred.get("is_expired", False)
            status = "🔴 已过期" if is_expired else "🟢 有效"
            
            print(f"   - {service_name}: {auth_type} ({status})")
    else:
        print(f"❌ 列出凭据失败: {list_result.error}")
    
    # 验证特定服务的凭据
    if credentials:
        first_service = credentials[0].get("service_name")
        validate_result = await auth_manager.execute({
            "action": "validate",
            "service_name": first_service
        })
        
        if validate_result.success:
            validation_info = validate_result.result.get("validation_result", {})
            print(f"✅ 服务 {first_service} 凭据验证: {validation_info}")
        else:
            print(f"❌ 凭据验证失败: {validate_result.error}")


async def main():
    """主函数"""
    print("🔐 AI Agent Framework 服务认证示例")
    print("=" * 50)
    
    # 演示各种认证类型
    await demo_api_key_auth()
    await demo_oauth2_auth()
    await demo_basic_auth()
    await demo_jwt_auth()
    await demo_custom_auth()
    
    # 演示带认证的服务注册
    await demo_auth_with_service()
    
    # 演示凭据管理
    await demo_list_and_manage_credentials()
    
    print("\n🎉 认证示例完成!")
    print("=" * 50)
    print("💡 总结:")
    print("   1. 支持多种认证类型：API Key、OAuth2、Basic、JWT、自定义")
    print("   2. 认证凭据安全存储和管理")
    print("   3. 自动生成认证头部")
    print("   4. 与服务注册无缝集成")
    print("   5. 支持令牌刷新和验证")
    
    print("\n📚 更多信息:")
    print("   - 认证配置文档: docs/authentication.md")
    print("   - 安全最佳实践: docs/security.md")


if __name__ == "__main__":
    asyncio.run(main())
