#!/usr/bin/env python3
"""
邮件服务工具使用示例

演示如何使用邮件发送和管理功能。
"""

import asyncio
from datetime import datetime

from ai_agent_framework.tools import EmailTool, EmailConfig
from ai_agent_framework.utils.logging_system import logging_system


async def demo_smtp_email():
    """演示SMTP邮件发送"""
    print("\n=== SMTP邮件发送演示 ===")
    
    # 配置SMTP邮件服务
    email_config = EmailConfig(
        provider_type="smtp",
        smtp_host="smtp.gmail.com",
        smtp_port=587,
        smtp_username="<EMAIL>",
        smtp_password="your_app_password",  # 使用应用专用密码
        smtp_use_tls=True,
        default_sender="<EMAIL>",
        default_sender_name="AI Agent Framework"
    )
    
    # 创建邮件工具
    email_tool = EmailTool(email_config)
    
    try:
        # 1. 测试连接
        print("🔗 测试邮件服务连接...")
        result = await email_tool.execute({"action": "test_connection"})
        
        if result.success:
            print(f"✅ 连接测试成功: {result.result['message']}")
        else:
            print(f"❌ 连接测试失败: {result.error}")
            return
        
        # 2. 发送简单文本邮件
        print("\n📧 发送简单文本邮件...")
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "AI Agent Framework 测试邮件",
            "body": "这是一封来自AI Agent Framework的测试邮件。\n\n发送时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "is_html": False
        })
        
        if result.success:
            print(f"✅ 邮件发送成功: {result.result['message']}")
            print(f"📋 收件人: {result.result['recipients']}")
        else:
            print(f"❌ 邮件发送失败: {result.error}")
        
        # 3. 发送HTML邮件
        print("\n🎨 发送HTML格式邮件...")
        html_body = """
        <html>
        <body>
            <h2>AI Agent Framework 测试邮件</h2>
            <p>这是一封<strong>HTML格式</strong>的测试邮件。</p>
            <ul>
                <li>支持富文本格式</li>
                <li>支持链接: <a href="https://github.com">GitHub</a></li>
                <li>支持图片和样式</li>
            </ul>
            <p><em>发送时间: {}</em></p>
        </body>
        </html>
        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "cc": ["<EMAIL>"],
            "subject": "AI Agent Framework HTML测试邮件",
            "body": html_body,
            "is_html": True,
            "priority": "high"
        })
        
        if result.success:
            print(f"✅ HTML邮件发送成功")
        else:
            print(f"❌ HTML邮件发送失败: {result.error}")
        
        # 4. 发送带附件的邮件
        print("\n📎 发送带附件的邮件...")
        
        # 创建示例附件
        attachment_content = "这是一个测试附件的内容。\n包含一些示例数据。"
        
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "AI Agent Framework 附件测试",
            "body": "这封邮件包含一个测试附件，请查收。",
            "attachments": [
                {
                    "filename": "test_document.txt",
                    "content": attachment_content,
                    "content_type": "text/plain"
                }
            ]
        })
        
        if result.success:
            print(f"✅ 带附件邮件发送成功")
        else:
            print(f"❌ 带附件邮件发送失败: {result.error}")
    
    except Exception as e:
        print(f"❌ SMTP邮件演示过程中发生错误: {e}")


async def demo_sendgrid_email():
    """演示SendGrid邮件发送"""
    print("\n=== SendGrid邮件发送演示 ===")
    
    # 配置SendGrid邮件服务
    email_config = EmailConfig(
        provider_type="sendgrid",
        api_key="your_sendgrid_api_key",
        default_sender="<EMAIL>",
        default_sender_name="AI Agent Framework"
    )
    
    # 创建邮件工具
    email_tool = EmailTool(email_config)
    
    try:
        # 测试连接
        print("🔗 测试SendGrid API连接...")
        result = await email_tool.execute({"action": "test_connection"})
        
        if result.success:
            print(f"✅ SendGrid连接测试成功")
        else:
            print(f"❌ SendGrid连接测试失败: {result.error}")
            return
        
        # 发送邮件
        print("\n📧 通过SendGrid发送邮件...")
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "SendGrid API 测试邮件",
            "body": "<h2>通过SendGrid API发送</h2><p>这是一封通过SendGrid API发送的测试邮件。</p>",
            "is_html": True
        })
        
        if result.success:
            print(f"✅ SendGrid邮件发送成功")
        else:
            print(f"❌ SendGrid邮件发送失败: {result.error}")
    
    except Exception as e:
        print(f"❌ SendGrid邮件演示过程中发生错误: {e}")


async def demo_amazon_ses_email():
    """演示Amazon SES邮件发送"""
    print("\n=== Amazon SES邮件发送演示 ===")
    
    # 配置Amazon SES邮件服务
    email_config = EmailConfig(
        provider_type="amazon_ses",
        api_key="your_aws_access_key_id",
        api_secret="your_aws_secret_access_key",
        region="us-east-1",
        default_sender="<EMAIL>",
        default_sender_name="AI Agent Framework"
    )
    
    # 创建邮件工具
    email_tool = EmailTool(email_config)
    
    try:
        # 测试连接
        print("🔗 测试Amazon SES连接...")
        result = await email_tool.execute({"action": "test_connection"})
        
        if result.success:
            print(f"✅ Amazon SES连接测试成功")
        else:
            print(f"❌ Amazon SES连接测试失败: {result.error}")
            return
        
        # 发送邮件
        print("\n📧 通过Amazon SES发送邮件...")
        result = await email_tool.execute({
            "action": "send",
            "to": ["<EMAIL>"],
            "subject": "Amazon SES 测试邮件",
            "body": "这是一封通过Amazon SES发送的测试邮件。",
            "reply_to": "<EMAIL>"
        })
        
        if result.success:
            print(f"✅ Amazon SES邮件发送成功")
        else:
            print(f"❌ Amazon SES邮件发送失败: {result.error}")
    
    except Exception as e:
        print(f"❌ Amazon SES邮件演示过程中发生错误: {e}")


async def demo_email_validation():
    """演示邮箱地址验证"""
    print("\n=== 邮箱地址验证演示 ===")
    
    # 创建邮件工具（使用默认配置）
    email_tool = EmailTool()
    
    # 测试邮箱地址列表
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "invalid.email",
        "@invalid.com",
        "user@",
        "<EMAIL>"
    ]
    
    print("📧 验证邮箱地址格式...")
    for email in test_emails:
        result = await email_tool.execute({
            "action": "validate_email",
            "email": email
        })
        
        if result.success:
            is_valid = result.result["is_valid"]
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"   {email}: {status}")
        else:
            print(f"   {email}: ❌ 验证失败 - {result.error}")


async def demo_batch_email_sending():
    """演示批量邮件发送"""
    print("\n=== 批量邮件发送演示 ===")
    
    # 配置邮件服务（使用SMTP示例）
    email_config = EmailConfig(
        provider_type="smtp",
        smtp_host="smtp.example.com",
        smtp_port=587,
        smtp_username="<EMAIL>",
        smtp_password="password",
        default_sender="<EMAIL>",
        default_sender_name="Batch Sender"
    )
    
    email_tool = EmailTool(email_config)
    
    # 收件人列表
    recipients = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print(f"📤 批量发送邮件给 {len(recipients)} 个收件人...")
    
    # 并发发送邮件
    tasks = []
    for i, recipient in enumerate(recipients, 1):
        task = email_tool.execute({
            "action": "send",
            "to": [recipient],
            "subject": f"批量邮件 #{i}",
            "body": f"这是发送给 {recipient} 的第 {i} 封批量邮件。",
            "priority": "normal"
        })
        tasks.append(task)
    
    # 等待所有邮件发送完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计结果
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"   邮件 #{i+1}: ❌ 异常 - {result}")
        elif result.success:
            print(f"   邮件 #{i+1}: ✅ 发送成功")
            success_count += 1
        else:
            print(f"   邮件 #{i+1}: ❌ 发送失败 - {result.error}")
    
    print(f"\n📊 批量发送结果: {success_count}/{len(recipients)} 成功")


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("📧 AI Agent Framework 邮件服务工具演示")
    print("=" * 50)
    
    # 注意：以下演示需要配置相应的邮件服务
    print("⚠️  注意：此演示需要配置相应的邮件服务凭据")
    print("请在代码中更新邮件服务配置后运行")
    
    # 演示各个功能
    # await demo_smtp_email()
    # await demo_sendgrid_email()
    # await demo_amazon_ses_email()
    await demo_email_validation()
    # await demo_batch_email_sending()
    
    print("\n✨ 演示完成！")
    print("\n💡 邮件服务工具特性总结：")
    print("1. ✅ 多种邮件服务提供商支持（SMTP、SendGrid、Amazon SES、Mailgun）")
    print("2. ✅ 文本和HTML格式邮件")
    print("3. ✅ 附件支持")
    print("4. ✅ 抄送、密送、回复地址")
    print("5. ✅ 邮件优先级设置")
    print("6. ✅ 邮箱地址格式验证")
    print("7. ✅ 批量邮件发送")
    print("8. ✅ 连接测试和错误处理")
    
    print("\n📚 配置示例：")
    print("# SMTP配置")
    print("EmailConfig(provider_type='smtp', smtp_host='smtp.gmail.com', ...)")
    print("# SendGrid配置")
    print("EmailConfig(provider_type='sendgrid', api_key='your_api_key', ...)")
    print("# Amazon SES配置")
    print("EmailConfig(provider_type='amazon_ses', api_key='access_key', api_secret='secret', ...)")


if __name__ == "__main__":
    asyncio.run(main())
