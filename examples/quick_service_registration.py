#!/usr/bin/env python3
"""
快速服务注册示例

这个示例展示了如何快速将一个REST API注册为AI Agent工具的最简流程。
"""

import asyncio
from ai_agent_framework.tools.service_registry_tool import ServiceRegistryTool
from ai_agent_framework.tools.service_adapter import ServiceToolFactory
from ai_agent_framework.utils.tool_registry import ToolRegistry


async def quick_register_service():
    """快速注册服务的最简示例"""
    
    print("🚀 快速服务注册示例")
    print("=" * 30)
    
    # 1. 创建必要的组件
    service_registry = ServiceRegistryTool()
    tool_registry = ToolRegistry()
    tool_factory = ServiceToolFactory()
    
    # 2. 定义服务配置（以JSONPlaceholder为例）
    service_config = {
        "name": "jsonplaceholder",
        "description": "JSONPlaceholder测试API",
        "base_url": "https://jsonplaceholder.typicode.com",
        "service_type": "rest_api",
        "auth": {"type": "none"},
        "endpoints": [
            {
                "name": "get_posts",
                "path": "/posts",
                "method": "GET",
                "description": "获取所有文章",
                "requires_auth": False,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "userId": {
                            "type": "integer",
                            "description": "用户ID（可选）"
                        }
                    },
                    "required": []
                }
            },
            {
                "name": "get_post",
                "path": "/posts/{id}",
                "method": "GET",
                "description": "获取指定文章",
                "requires_auth": False,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "integer",
                            "description": "文章ID",
                            "in": "path"
                        }
                    },
                    "required": ["id"]
                }
            },
            {
                "name": "create_post",
                "path": "/posts",
                "method": "POST",
                "description": "创建新文章",
                "requires_auth": False,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "文章标题",
                            "in": "body"
                        },
                        "body": {
                            "type": "string",
                            "description": "文章内容",
                            "in": "body"
                        },
                        "userId": {
                            "type": "integer",
                            "description": "用户ID",
                            "in": "body"
                        }
                    },
                    "required": ["title", "body", "userId"]
                }
            }
        ]
    }
    
    # 3. 注册服务
    print("📝 注册服务...")
    register_result = await service_registry.execute({
        "action": "register",
        "service_config": service_config
    })
    
    if register_result.success:
        print("✅ 服务注册成功!")
    else:
        print(f"❌ 服务注册失败: {register_result.error}")
        return
    
    # 4. 创建动态工具
    print("🔧 创建动态工具...")
    from ai_agent_framework.tools.service_registry_tool import ServiceConfig
    config = ServiceConfig(**service_config)
    tools = tool_factory.create_tools_from_config(config)
    
    print(f"✅ 创建了 {len(tools)} 个工具:")
    for tool in tools:
        print(f"   - {tool.name}: {tool.description}")
    
    # 5. 注册工具到工具注册表
    print("📋 注册工具到工具注册表...")
    for tool in tools:
        tool_registry.register_tool(tool)
    
    print("✅ 所有工具已注册到工具注册表")
    
    # 6. 测试工具调用
    print("🧪 测试工具调用...")
    
    # 测试获取所有文章
    get_posts_tool = tool_registry.get_tool("jsonplaceholder_get_posts")
    if get_posts_tool:
        result = await get_posts_tool.execute({})
        if result.success:
            posts = result.result.get("data", [])
            print(f"✅ 获取文章成功，共 {len(posts)} 篇文章")
            if posts:
                print(f"   第一篇文章标题: {posts[0].get('title', 'N/A')}")
        else:
            print(f"❌ 获取文章失败: {result.error}")
    
    # 测试获取指定文章
    get_post_tool = tool_registry.get_tool("jsonplaceholder_get_post")
    if get_post_tool:
        result = await get_post_tool.execute({"id": 1})
        if result.success:
            post = result.result.get("data", {})
            print(f"✅ 获取文章1成功: {post.get('title', 'N/A')}")
        else:
            print(f"❌ 获取文章1失败: {result.error}")
    
    # 测试创建文章
    create_post_tool = tool_registry.get_tool("jsonplaceholder_create_post")
    if create_post_tool:
        result = await create_post_tool.execute({
            "title": "AI Agent测试文章",
            "body": "这是通过AI Agent Framework创建的测试文章",
            "userId": 1
        })
        if result.success:
            new_post = result.result.get("data", {})
            print(f"✅ 创建文章成功，ID: {new_post.get('id', 'N/A')}")
        else:
            print(f"❌ 创建文章失败: {result.error}")
    
    print("\n🎉 快速服务注册完成!")
    print("💡 现在你可以在AI Agent中使用这些工具了")


async def discover_and_register_service():
    """发现并注册服务的示例"""
    
    print("\n🔍 服务发现和注册示例")
    print("=" * 30)
    
    from ai_agent_framework.tools.service_discovery import ServiceDiscoveryTool
    
    # 创建服务发现工具
    discovery_tool = ServiceDiscoveryTool()
    
    # 尝试发现OpenAPI服务
    print("🔍 尝试发现OpenAPI服务...")
    
    # 这里使用一个公开的OpenAPI示例
    discovery_result = await discovery_tool.execute({
        "action": "discover_openapi",
        "target": "https://petstore.swagger.io/v2",
        "options": {
            "generate_examples": True
        }
    })
    
    if discovery_result.success:
        service_config = discovery_result.result.get("service_config", {})
        print(f"✅ 发现OpenAPI服务: {service_config.get('name', 'Unknown')}")
        print(f"   描述: {service_config.get('description', 'N/A')}")
        print(f"   端点数量: {len(service_config.get('endpoints', []))}")
        
        # 可以选择注册这个发现的服务
        print("💡 你可以使用发现的配置来注册这个服务")
    else:
        print(f"❌ 服务发现失败: {discovery_result.error}")


async def main():
    """主函数"""
    # 运行快速注册示例
    await quick_register_service()
    
    # 运行服务发现示例
    await discover_and_register_service()


if __name__ == "__main__":
    asyncio.run(main())
