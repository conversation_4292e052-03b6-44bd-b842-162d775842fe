#!/usr/bin/env python3
"""
企业级消息队列适配器使用示例

演示如何使用RabbitMQ和Kafka等企业级消息队列服务。
"""

import asyncio
import json
from datetime import datetime

from ai_agent_framework.tools import MessageQueueTool, MessageQueueType, Message
from ai_agent_framework.utils.logging_system import logging_system


async def demo_rabbitmq():
    """演示RabbitMQ消息队列"""
    print("\n=== RabbitMQ消息队列演示 ===")
    
    # 配置RabbitMQ
    rabbitmq_config = {
        "connection_url": "amqp://guest:guest@localhost:5672/",
        "exchange_name": "ai_agent_exchange",
        "exchange_type": "direct",
        "durable": True
    }
    
    # 创建RabbitMQ消息队列工具
    queue_tool = MessageQueueTool(
        queue_type=MessageQueueType.RABBITMQ,
        connection_config=rabbitmq_config
    )
    
    try:
        # 连接到RabbitMQ
        result = await queue_tool.execute({"action": "connect"})
        if not result.success:
            print(f"连接RabbitMQ失败: {result.error}")
            return
        
        print("✅ 成功连接到RabbitMQ")
        
        # 发送消息
        message = Message(
            content={"type": "task", "data": "处理用户请求", "priority": "high"},
            metadata={"source": "web_api", "user_id": "12345"}
        )
        
        result = await queue_tool.execute({
            "action": "send",
            "queue_name": "task_queue",
            "message": message.to_dict(),
            "routing_key": "task_queue",
            "priority": 5
        })
        
        if result.success:
            print(f"✅ 消息发送成功: {result.result}")
            
            # 接收消息
            result = await queue_tool.execute({
                "action": "receive",
                "queue_name": "task_queue",
                "timeout": 10
            })
            
            if result.success and result.result["message"]:
                received_message = result.result["message"]
                print(f"📨 接收到消息: {received_message['content']}")
                print(f"📋 消息元数据: {received_message['metadata']}")
            else:
                print("❌ 未接收到消息")
            
            # 获取队列信息
            result = await queue_tool.execute({
                "action": "info",
                "queue_name": "task_queue"
            })
            
            if result.success:
                queue_info = result.result["info"]
                print(f"📊 队列信息: {queue_info}")
        
        else:
            print(f"❌ 消息发送失败: {result.error}")
    
    except Exception as e:
        print(f"❌ RabbitMQ演示过程中发生错误: {e}")
    
    finally:
        # 断开连接
        await queue_tool.execute({"action": "disconnect"})


async def demo_kafka():
    """演示Kafka消息队列"""
    print("\n=== Kafka消息队列演示 ===")
    
    # 配置Kafka
    kafka_config = {
        "bootstrap_servers": "localhost:9092",
        "client_id": "ai_agent_kafka_demo",
        "group_id": "demo_group",
        "auto_offset_reset": "latest",
        "enable_auto_commit": True
    }
    
    # 创建Kafka消息队列工具
    queue_tool = MessageQueueTool(
        queue_type=MessageQueueType.KAFKA,
        connection_config=kafka_config
    )
    
    try:
        # 连接到Kafka
        result = await queue_tool.execute({"action": "connect"})
        if not result.success:
            print(f"连接Kafka失败: {result.error}")
            return
        
        print("✅ 成功连接到Kafka")
        
        # 发送消息
        message = Message(
            content={"event": "user_login", "user_id": "67890", "timestamp": datetime.now().isoformat()},
            metadata={"source": "mobile_app", "version": "1.2.3"}
        )
        
        result = await queue_tool.execute({
            "action": "send",
            "queue_name": "user_events",  # Kafka中的topic
            "message": message.to_dict(),
            "routing_key": "user_67890"  # Kafka中的partition key
        })
        
        if result.success:
            print(f"✅ 消息发送成功: {result.result}")
            
            # 接收消息
            result = await queue_tool.execute({
                "action": "receive",
                "queue_name": "user_events",
                "timeout": 10
            })
            
            if result.success and result.result["message"]:
                received_message = result.result["message"]
                print(f"📨 接收到消息: {received_message['content']}")
                print(f"📋 消息元数据: {received_message['metadata']}")
            else:
                print("❌ 未接收到消息")
            
            # 获取主题信息
            result = await queue_tool.execute({
                "action": "info",
                "queue_name": "user_events"
            })
            
            if result.success:
                topic_info = result.result["info"]
                print(f"📊 主题信息: {topic_info}")
        
        else:
            print(f"❌ 消息发送失败: {result.error}")
    
    except Exception as e:
        print(f"❌ Kafka演示过程中发生错误: {e}")
    
    finally:
        # 断开连接
        await queue_tool.execute({"action": "disconnect"})


async def demo_message_subscription():
    """演示消息订阅功能"""
    print("\n=== 消息订阅演示 ===")
    
    # 使用RabbitMQ进行订阅演示
    rabbitmq_config = {
        "connection_url": "amqp://guest:guest@localhost:5672/",
        "exchange_name": "notification_exchange",
        "exchange_type": "fanout",
        "durable": True
    }
    
    queue_tool = MessageQueueTool(
        queue_type=MessageQueueType.RABBITMQ,
        connection_config=rabbitmq_config
    )
    
    try:
        # 连接
        await queue_tool.execute({"action": "connect"})
        print("✅ 已连接到RabbitMQ")
        
        # 定义消息处理函数
        async def message_handler(message: Message):
            print(f"🔔 收到通知: {message.content}")
            print(f"📅 时间: {message.timestamp}")
        
        # 订阅消息
        result = await queue_tool.execute({
            "action": "subscribe",
            "queue_name": "notifications",
            "callback": message_handler,
            "routing_key": "notifications"
        })
        
        if result.success:
            print("✅ 已订阅通知队列")
            
            # 发送几条测试消息
            for i in range(3):
                message = Message(
                    content=f"这是第 {i+1} 条通知消息",
                    metadata={"type": "info", "sequence": i+1}
                )
                
                await queue_tool.execute({
                    "action": "send",
                    "queue_name": "notifications",
                    "message": message.to_dict(),
                    "routing_key": "notifications"
                })
                
                await asyncio.sleep(1)  # 等待消息处理
            
            print("📤 已发送测试消息")
            
            # 等待消息处理
            await asyncio.sleep(3)
            
            # 取消订阅
            result = await queue_tool.execute({
                "action": "unsubscribe",
                "queue_name": "notifications"
            })
            
            if result.success:
                print("✅ 已取消订阅")
        
        else:
            print(f"❌ 订阅失败: {result.error}")
    
    except Exception as e:
        print(f"❌ 订阅演示过程中发生错误: {e}")
    
    finally:
        await queue_tool.execute({"action": "disconnect"})


async def demo_queue_management():
    """演示队列管理功能"""
    print("\n=== 队列管理演示 ===")
    
    rabbitmq_config = {
        "connection_url": "amqp://guest:guest@localhost:5672/",
        "exchange_name": "management_exchange",
        "exchange_type": "direct",
        "durable": True
    }
    
    queue_tool = MessageQueueTool(
        queue_type=MessageQueueType.RABBITMQ,
        connection_config=rabbitmq_config
    )
    
    try:
        await queue_tool.execute({"action": "connect"})
        print("✅ 已连接到RabbitMQ")
        
        queue_name = "test_management_queue"
        
        # 发送一些测试消息
        for i in range(5):
            message = Message(content=f"测试消息 {i+1}")
            await queue_tool.execute({
                "action": "send",
                "queue_name": queue_name,
                "message": message.to_dict()
            })
        
        print(f"📤 已发送5条测试消息到队列 {queue_name}")
        
        # 获取队列信息
        result = await queue_tool.execute({
            "action": "info",
            "queue_name": queue_name
        })
        
        if result.success:
            print(f"📊 队列信息: {result.result['info']}")
        
        # 清空队列
        result = await queue_tool.execute({
            "action": "purge",
            "queue_name": queue_name
        })
        
        if result.success:
            print(f"🗑️ 已清空队列 {queue_name}")
        
        # 删除队列
        result = await queue_tool.execute({
            "action": "delete",
            "queue_name": queue_name
        })
        
        if result.success:
            print(f"❌ 已删除队列 {queue_name}")
    
    except Exception as e:
        print(f"❌ 队列管理演示过程中发生错误: {e}")
    
    finally:
        await queue_tool.execute({"action": "disconnect"})


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("🚀 企业级消息队列适配器演示")
    print("=" * 50)
    
    # 注意：以下演示需要运行相应的消息队列服务
    print("⚠️  注意：此演示需要运行相应的消息队列服务")
    print("RabbitMQ: docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management")
    print("Kafka: 请参考Kafka官方文档启动Kafka服务")
    
    # 演示各个功能
    # await demo_rabbitmq()
    # await demo_kafka()
    # await demo_message_subscription()
    # await demo_queue_management()
    
    print("\n✨ 演示完成！")
    print("\n💡 提示：")
    print("1. 取消注释相应的演示函数来测试特定的消息队列服务")
    print("2. 确保已安装相应的依赖库：")
    print("   - RabbitMQ: pip install aio-pika")
    print("   - Kafka: pip install aiokafka")
    print("3. 确保相应的消息队列服务正在运行")


if __name__ == "__main__":
    asyncio.run(main())
