#!/usr/bin/env python3
"""
云存储适配器使用示例

演示如何使用AWS S3、阿里云OSS、腾讯云COS等云存储服务。
"""

import asyncio
import tempfile
import os
from pathlib import Path

from ai_agent_framework.tools import FileStorageTool, StorageType
from ai_agent_framework.utils.logging_system import logging_system


async def demo_aws_s3():
    """演示AWS S3存储"""
    print("\n=== AWS S3存储演示 ===")
    
    # 配置AWS S3
    s3_config = {
        "access_key_id": "your-access-key-id",
        "secret_access_key": "your-secret-access-key",
        "bucket_name": "your-bucket-name",
        "region": "us-east-1"
    }
    
    # 创建S3存储工具
    storage_tool = FileStorageTool(
        storage_type=StorageType.AWS_S3,
        connection_config=s3_config
    )
    
    try:
        # 连接到S3
        result = await storage_tool.execute({"action": "connect"})
        if not result.success:
            print(f"连接S3失败: {result.error}")
            return
        
        print("✅ 成功连接到AWS S3")
        
        # 创建临时文件用于测试
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("这是一个测试文件，用于演示AWS S3上传功能。\n")
            f.write(f"创建时间: {asyncio.get_event_loop().time()}\n")
            temp_file = f.name
        
        try:
            # 上传文件
            result = await storage_tool.execute({
                "action": "upload",
                "local_path": temp_file,
                "remote_path": "demo/test_s3.txt",
                "metadata": {
                    "source": "cloud_storage_example",
                    "type": "demo"
                }
            })
            
            if result.success:
                print(f"✅ 文件上传成功: {result.result}")
                
                # 检查文件是否存在
                result = await storage_tool.execute({
                    "action": "exists",
                    "remote_path": "demo/test_s3.txt"
                })
                print(f"📁 文件存在检查: {result.result['exists']}")
                
                # 获取文件信息
                result = await storage_tool.execute({
                    "action": "info",
                    "remote_path": "demo/test_s3.txt"
                })
                if result.success and result.result["found"]:
                    file_info = result.result["file_info"]
                    print(f"📋 文件信息: {file_info['name']}, 大小: {file_info['size']} 字节")
                
                # 生成预签名URL
                result = await storage_tool.execute({
                    "action": "generate_url",
                    "remote_path": "demo/test_s3.txt",
                    "expiration": 3600,
                    "method": "GET"
                })
                if result.success:
                    print(f"🔗 预签名URL: {result.result['url'][:50]}...")
                
                # 下载文件
                download_path = temp_file + ".downloaded"
                result = await storage_tool.execute({
                    "action": "download",
                    "remote_path": "demo/test_s3.txt",
                    "local_path": download_path
                })
                
                if result.success:
                    print(f"⬇️ 文件下载成功: {download_path}")
                    
                    # 验证下载的文件
                    if os.path.exists(download_path):
                        with open(download_path, 'r') as f:
                            content = f.read()
                            print(f"📄 下载文件内容预览: {content[:50]}...")
                        os.unlink(download_path)
                
                # 清理：删除远程文件
                result = await storage_tool.execute({
                    "action": "delete",
                    "remote_path": "demo/test_s3.txt"
                })
                if result.success:
                    print("🗑️ 远程文件已删除")
            
            else:
                print(f"❌ 文件上传失败: {result.error}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    except Exception as e:
        print(f"❌ S3演示过程中发生错误: {e}")
    
    finally:
        # 断开连接
        await storage_tool.execute({"action": "disconnect"})


async def demo_aliyun_oss():
    """演示阿里云OSS存储"""
    print("\n=== 阿里云OSS存储演示 ===")
    
    # 配置阿里云OSS
    oss_config = {
        "access_key_id": "your-access-key-id",
        "access_key_secret": "your-access-key-secret",
        "bucket_name": "your-bucket-name",
        "endpoint": "oss-cn-hangzhou.aliyuncs.com"
    }
    
    # 创建OSS存储工具
    storage_tool = FileStorageTool(
        storage_type=StorageType.ALIYUN_OSS,
        connection_config=oss_config
    )
    
    try:
        # 连接到OSS
        result = await storage_tool.execute({"action": "connect"})
        if not result.success:
            print(f"连接OSS失败: {result.error}")
            return
        
        print("✅ 成功连接到阿里云OSS")
        
        # 创建临时文件用于测试
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("这是一个测试文件，用于演示阿里云OSS上传功能。\n")
            f.write(f"创建时间: {asyncio.get_event_loop().time()}\n")
            temp_file = f.name
        
        try:
            # 上传文件
            result = await storage_tool.execute({
                "action": "upload",
                "local_path": temp_file,
                "remote_path": "demo/test_oss.txt",
                "metadata": {
                    "source": "cloud_storage_example",
                    "type": "demo"
                }
            })
            
            if result.success:
                print(f"✅ 文件上传成功: {result.result}")
                
                # 列出文件
                result = await storage_tool.execute({
                    "action": "list",
                    "prefix": "demo/",
                    "max_files": 10
                })
                if result.success:
                    files = result.result["files"]
                    print(f"📂 找到 {len(files)} 个文件")
                    for file_info in files:
                        print(f"   - {file_info['name']} ({file_info['size']} 字节)")
            
            else:
                print(f"❌ 文件上传失败: {result.error}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    except Exception as e:
        print(f"❌ OSS演示过程中发生错误: {e}")
    
    finally:
        # 断开连接
        await storage_tool.execute({"action": "disconnect"})


async def demo_tencent_cos():
    """演示腾讯云COS存储"""
    print("\n=== 腾讯云COS存储演示 ===")
    
    # 配置腾讯云COS
    cos_config = {
        "secret_id": "your-secret-id",
        "secret_key": "your-secret-key",
        "bucket_name": "your-bucket-name-1234567890",  # COS桶名需要包含APPID
        "region": "ap-beijing"
    }
    
    # 创建COS存储工具
    storage_tool = FileStorageTool(
        storage_type=StorageType.TENCENT_COS,
        connection_config=cos_config
    )
    
    try:
        # 连接到COS
        result = await storage_tool.execute({"action": "connect"})
        if not result.success:
            print(f"连接COS失败: {result.error}")
            return
        
        print("✅ 成功连接到腾讯云COS")
        
        # 创建临时文件用于测试
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("这是一个测试文件，用于演示腾讯云COS上传功能。\n")
            f.write(f"创建时间: {asyncio.get_event_loop().time()}\n")
            temp_file = f.name
        
        try:
            # 上传文件
            result = await storage_tool.execute({
                "action": "upload",
                "local_path": temp_file,
                "remote_path": "demo/test_cos.txt",
                "metadata": {
                    "source": "cloud_storage_example",
                    "type": "demo"
                }
            })
            
            if result.success:
                print(f"✅ 文件上传成功: {result.result}")
                
                # 复制文件
                result = await storage_tool.execute({
                    "action": "copy",
                    "source_path": "demo/test_cos.txt",
                    "destination_path": "demo/test_cos_copy.txt"
                })
                if result.success:
                    print("📋 文件复制成功")
            
            else:
                print(f"❌ 文件上传失败: {result.error}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    except Exception as e:
        print(f"❌ COS演示过程中发生错误: {e}")
    
    finally:
        # 断开连接
        await storage_tool.execute({"action": "disconnect"})


async def main():
    """主函数"""
    # 配置日志
    logging_system.configure(level="INFO")
    
    print("🚀 云存储适配器演示")
    print("=" * 50)
    
    # 注意：以下演示需要有效的云服务凭据
    # 请根据实际情况修改配置信息
    
    print("⚠️  注意：此演示需要有效的云服务凭据")
    print("请在代码中配置正确的访问密钥和存储桶信息")
    
    # 演示各个云存储服务
    # await demo_aws_s3()
    # await demo_aliyun_oss()
    # await demo_tencent_cos()
    
    print("\n✨ 演示完成！")
    print("\n💡 提示：")
    print("1. 取消注释相应的演示函数来测试特定的云存储服务")
    print("2. 确保已安装相应的依赖库：")
    print("   - AWS S3: pip install boto3")
    print("   - 阿里云OSS: pip install oss2")
    print("   - 腾讯云COS: pip install cos-python-sdk-v5")
    print("3. 配置正确的访问凭据和存储桶信息")


if __name__ == "__main__":
    asyncio.run(main())
