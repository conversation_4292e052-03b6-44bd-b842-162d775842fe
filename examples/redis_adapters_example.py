#!/usr/bin/env python3
"""
Redis适配器使用示例

演示如何使用Redis作为消息队列和缓存的后端存储。
"""

import asyncio
import json
from typing import Dict, Any

from ai_agent_framework.tools import (
    MessageQueueTool,
    CacheTool,
    MessageQueueType,
    CacheType,
)
from ai_agent_framework.utils.tool_registry import tool_registry


async def demo_redis_message_queue():
    """演示Redis消息队列功能"""
    print("\n📨 Redis消息队列演示")
    print("-" * 40)
    
    try:
        # 创建Redis消息队列工具
        mq_tool = MessageQueueTool(
            queue_type=MessageQueueType.REDIS,
            connection_config={
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,  # 如果Redis设置了密码，在这里配置
            }
        )
        
        # 连接到Redis
        result = await mq_tool.execute({"action": "connect"})
        if not result.success:
            print("❌ 无法连接到Redis，请确保Redis服务正在运行")
            return
        
        print("✅ 已连接到Redis消息队列")
        
        # 创建队列
        result = await mq_tool.execute({
            "action": "create_queue",
            "queue_name": "redis_demo_queue",
            "durable": True
        })
        print(f"创建队列: {result.result['created']}")
        
        # 发送不同优先级的消息
        messages = [
            {"content": {"task": "低优先级任务", "data": [1, 2, 3]}, "priority": "low"},
            {"content": {"task": "普通任务", "data": [4, 5, 6]}, "priority": "normal"},
            {"content": {"task": "高优先级任务", "data": [7, 8, 9]}, "priority": "high"},
            {"content": {"task": "紧急任务", "data": [10, 11, 12]}, "priority": "urgent"},
        ]
        
        for msg in messages:
            result = await mq_tool.execute({
                "action": "send",
                "queue_name": "redis_demo_queue",
                "message": msg
            })
            print(f"发送消息 '{msg['content']['task']}': {result.result['sent']}")
        
        # 发送延迟消息
        result = await mq_tool.execute({
            "action": "send",
            "queue_name": "redis_demo_queue",
            "message": {
                "content": {"task": "延迟任务", "data": [13, 14, 15]},
                "priority": "normal",
                "delay_seconds": 5,
                "metadata": {"scheduled": True}
            }
        })
        print(f"发送延迟消息: {result.result['sent']}")
        
        # 接收消息（按优先级顺序）
        print("\n接收消息:")
        for i in range(4):  # 接收前4条消息
            result = await mq_tool.execute({
                "action": "receive",
                "queue_name": "redis_demo_queue",
                "timeout_seconds": 2
            })
            if result.result["received"]:
                msg = result.result["message"]
                print(f"  收到: {msg['content']['task']} (优先级: {msg['priority']})")
            else:
                print("  没有更多消息")
        
        # 等待延迟消息
        print("\n等待延迟消息...")
        await asyncio.sleep(6)
        result = await mq_tool.execute({
            "action": "receive",
            "queue_name": "redis_demo_queue",
            "timeout_seconds": 1
        })
        if result.result["received"]:
            msg = result.result["message"]
            print(f"  收到延迟消息: {msg['content']['task']}")
        
        # 获取队列信息
        result = await mq_tool.execute({
            "action": "queue_info",
            "queue_name": "redis_demo_queue"
        })
        print(f"\n队列信息: {json.dumps(result.result, indent=2, ensure_ascii=False)}")
        
        # 清理
        await mq_tool.execute({
            "action": "delete_queue",
            "queue_name": "redis_demo_queue"
        })
        await mq_tool.execute({"action": "disconnect"})
        print("✅ Redis消息队列演示完成")
        
    except ImportError:
        print("❌ Redis库未安装，请运行: pip install redis")
    except Exception as e:
        print(f"❌ Redis消息队列演示失败: {str(e)}")


async def demo_redis_cache():
    """演示Redis缓存功能"""
    print("\n💾 Redis缓存演示")
    print("-" * 40)
    
    try:
        # 创建Redis缓存工具
        cache_tool = CacheTool(
            cache_type=CacheType.REDIS,
            connection_config={
                "host": "localhost",
                "port": 6379,
                "db": 1,  # 使用不同的数据库
                "password": None,
                "key_prefix": "demo:",  # 键前缀
            }
        )
        
        # 连接到Redis
        result = await cache_tool.execute({"action": "connect"})
        if not result.success:
            print("❌ 无法连接到Redis，请确保Redis服务正在运行")
            return
        
        print("✅ 已连接到Redis缓存")
        
        # 设置单个缓存
        user_data = {
            "id": 123,
            "name": "张三",
            "email": "<EMAIL>",
            "role": "developer",
            "permissions": ["read", "write", "execute"]
        }
        
        result = await cache_tool.execute({
            "action": "set",
            "key": "user:123",
            "value": user_data,
            "ttl_seconds": 3600,  # 1小时过期
            "tags": ["user", "profile"]
        })
        print(f"设置用户缓存: {result.result['set']}")
        
        # 批量设置配置
        config_data = {
            "app:theme": "dark",
            "app:language": "zh-CN",
            "app:timezone": "Asia/Shanghai",
            "app:debug": False,
            "app:max_connections": 100
        }
        
        result = await cache_tool.execute({
            "action": "set_multi",
            "key_value_pairs": config_data,
            "ttl_seconds": 7200  # 2小时过期
        })
        print(f"批量设置配置: {result.result['success_count']}/{result.result['total_count']}")
        
        # 获取缓存
        result = await cache_tool.execute({
            "action": "get",
            "key": "user:123"
        })
        if result.result["found"]:
            user = result.result['value']
            print(f"获取用户数据: {user['name']} ({user['email']})")
        
        # 批量获取
        result = await cache_tool.execute({
            "action": "get_multi",
            "keys": ["app:theme", "app:language", "app:timezone"]
        })
        print(f"批量获取配置: {result.result['found_count']} 个配置项")
        for key, value in result.result['values'].items():
            if value is not None:
                print(f"  {key}: {value}")
        
        # 数值操作
        await cache_tool.execute({
            "action": "set",
            "key": "counter",
            "value": 10
        })
        
        result = await cache_tool.execute({
            "action": "increment",
            "key": "counter",
            "delta": 5
        })
        print(f"计数器增加: {result.result['old_value']} -> {result.result['new_value']}")
        
        result = await cache_tool.execute({
            "action": "decrement",
            "key": "counter",
            "delta": 3
        })
        print(f"计数器减少: {result.result['old_value']} -> {result.result['new_value']}")
        
        # 获取键列表
        result = await cache_tool.execute({
            "action": "keys",
            "pattern": "app:*"
        })
        print(f"应用配置键: {result.result['keys']}")
        
        # 获取统计信息
        result = await cache_tool.execute({"action": "stats"})
        stats = result.result
        print(f"\n缓存统计:")
        print(f"  类型: {stats['type']}")
        print(f"  连接状态: {stats['connected']}")
        print(f"  命中次数: {stats['hits']}")
        print(f"  未命中次数: {stats['misses']}")
        print(f"  命中率: {stats['hit_rate']:.2%}")
        
        if 'redis_info' in stats:
            redis_info = stats['redis_info']
            print(f"  Redis版本: {redis_info.get('redis_version', 'unknown')}")
            print(f"  内存使用: {redis_info.get('used_memory', 0)} 字节")
            print(f"  连接客户端: {redis_info.get('connected_clients', 0)}")
        
        # 清理
        await cache_tool.execute({"action": "clear"})
        await cache_tool.execute({"action": "disconnect"})
        print("✅ Redis缓存演示完成")
        
    except ImportError:
        print("❌ Redis库未安装，请运行: pip install redis")
    except Exception as e:
        print(f"❌ Redis缓存演示失败: {str(e)}")


async def demo_redis_cluster():
    """演示Redis集群模式"""
    print("\n🔗 Redis集群模式演示")
    print("-" * 40)
    
    try:
        # 创建Redis集群缓存工具
        cache_tool = CacheTool(
            cache_type=CacheType.REDIS,
            connection_config={
                "host": "localhost",
                "port": 7000,  # Redis集群端口
                "cluster_mode": True,
                "key_prefix": "cluster:",
            }
        )
        
        # 尝试连接到集群
        result = await cache_tool.execute({"action": "connect"})
        if result.success:
            print("✅ 已连接到Redis集群")
            
            # 测试基本操作
            await cache_tool.execute({
                "action": "set",
                "key": "cluster_test",
                "value": "集群测试数据"
            })
            
            result = await cache_tool.execute({
                "action": "get",
                "key": "cluster_test"
            })
            
            if result.result["found"]:
                print(f"集群数据: {result.result['value']}")
            
            await cache_tool.execute({"action": "disconnect"})
        else:
            print("⚠️ 无法连接到Redis集群（这是正常的，如果没有配置集群）")
            
    except Exception as e:
        print(f"⚠️ Redis集群演示跳过: {str(e)}")


async def main():
    """主函数"""
    print("🚀 Redis适配器使用示例")
    print("=" * 50)
    
    print("📋 使用前请确保:")
    print("1. Redis服务正在运行 (默认端口 6379)")
    print("2. 已安装redis库: pip install redis")
    print("3. Redis配置允许连接")
    
    try:
        # 运行演示
        await demo_redis_message_queue()
        await demo_redis_cache()
        await demo_redis_cluster()
        
        print("\n🎉 所有Redis适配器演示完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
