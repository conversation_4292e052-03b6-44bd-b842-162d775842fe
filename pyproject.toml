[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "ai-agent-framework"
version = "0.1.0"
description = "模型无关通用AI Agent框架 - 支持多种AI模型适配、工具调用、记忆管理等核心功能"
authors = ["AI Agent Framework Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://github.com/aier/ai-agent"
repository = "https://github.com/aier/ai-agent"
documentation = "https://ai-agent-framework.readthedocs.io"
keywords = ["ai", "agent", "framework", "llm", "openai", "claude", "anthropic"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
packages = [{include = "ai_agent_framework", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
# 核心依赖
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
asyncio-mqtt = {version = "^0.16.0", optional = true}
jinja2 = "^3.1.0"
# HTTP客户端
httpx = "^0.27.0"
# AI模型SDK
openai = "^1.68.0"
anthropic = "^0.40.0"
# 数据存储
# sqlite3 是Python标准库，不需要单独安装
# 日志和监控
structlog = "^24.0.0"
prometheus-client = "^0.21.0"
# 工具和实用程序
python-dotenv = "^1.0.0"
typer = {version = "^0.12.0", optional = true}
psutil = "^7.0.0"
aiohttp = "^3.12.15"
pyjwt = "^2.10.1"
cryptography = "<45"
# 云存储适配器（可选依赖）
boto3 = {version = "^1.26.0", optional = true}
oss2 = {version = "^2.17.0", optional = true}
cos-python-sdk-v5 = {version = "^1.9.0", optional = true}
# 企业级消息队列适配器（可选依赖）
aio-pika = {version = "^9.0.0", optional = true}
aiokafka = {version = "^0.8.0", optional = true}
# 邮件服务适配器（可选依赖）
aiohttp = {version = "^3.8.0", optional = true}
boto3 = {version = "^1.26.0", optional = true}

[tool.poetry.extras]
cli = ["typer"]
mqtt = ["asyncio-mqtt"]
# 云存储适配器
aws = ["boto3"]
aliyun = ["oss2"]
tencent = ["cos-python-sdk-v5"]
cloud-storage = ["boto3", "oss2", "cos-python-sdk-v5"]
# 企业级消息队列适配器
rabbitmq = ["aio-pika"]
kafka = ["aiokafka"]
enterprise-queue = ["aio-pika", "aiokafka"]
# 邮件服务适配器
email = ["aiohttp"]
email-aws = ["aiohttp", "boto3"]
email-full = ["aiohttp", "boto3"]
all = ["typer", "asyncio-mqtt", "boto3", "oss2", "cos-python-sdk-v5", "aio-pika", "aiokafka", "aiohttp"]

[tool.poetry.group.dev.dependencies]
# 测试框架
pytest = "^8.0.0"
pytest-asyncio = "^0.24.0"
pytest-cov = "^5.0.0"
pytest-mock = "^3.14.0"
# 代码质量
black = "^24.0.0"
isort = "^5.13.0"
flake8 = "^7.0.0"
mypy = "^1.11.0"
# 预提交钩子
pre-commit = "^3.8.0"
# 文档
mkdocs = "^1.6.0"
mkdocs-material = "^9.5.0"
mkdocs-mermaid2-plugin = "^1.1.0"

[tool.poetry.group.test.dependencies]
respx = "^0.21.0"

[tool.poetry.scripts]
ai-agent = "ai_agent_framework.cli:main"

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_agent_framework"]

# 类型检查配置
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "openai.*",
    "anthropic.*",
    "prometheus_client.*",
    "boto3.*",
    "botocore.*",
    "oss2.*",
    "qcloud_cos.*",
    "aio_pika.*",
    "aiokafka.*",
]
ignore_missing_imports = true

# 测试配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
