# 模型无关通用AI Agent框架

## 🎯 项目概述

这是一个**模型无关的通用AI Agent框架**，旨在解决当前AI Agent开发中的模型绑定问题。通过分层抽象架构，实现了对不同厂商大语言模型的统一支持，让开发者能够轻松构建可扩展、可维护的AI Agent应用。

## ✨ 核心特性

### 🔄 模型无关性
- **统一接口**：支持OpenAI GPT、Anthropic Claude、国产模型等
- **无缝切换**：一行代码即可切换不同模型
- **格式标准化**：自动处理不同模型的API格式差异

### 🧩 插件化架构
- **工具扩展**：简单易用的工具注册机制
- **模型适配**：新增模型适配器开发时间 < 2天
- **推理模式**：支持ReAct、Chain-of-Thought等多种推理模式

### 🧠 智能记忆管理
- **多层记忆**：短期、工作、长期记忆分层管理
- **智能检索**：基于相似度的记忆检索机制
- **上下文优化**：动态上下文长度管理

### 🔧 服务注册与工具生成
- **快速服务注册**：将现有微服务或API快速注册为AI Agent可调用的工具
- **自动服务发现**：支持OpenAPI/Swagger规范自动发现和解析
- **动态工具生成**：根据服务配置自动创建可调用的工具
- **多种服务类型**：支持REST API、GraphQL、gRPC、WebSocket等

### 🔐 全面认证支持
- **多种认证方式**：API Key、OAuth2、JWT、Basic Auth、自定义认证
- **安全凭据管理**：加密存储和管理认证凭据
- **自动令牌刷新**：支持OAuth2令牌自动刷新
- **权限控制**：细粒度的工具权限管理

### ✅ 完整验证测试
- **连接性测试**：验证服务可访问性
- **认证验证**：测试认证配置正确性
- **端点功能测试**：验证每个API端点的功能
- **性能基准测试**：响应时间和成功率分析
- **健康检查**：定期监控服务状态

### 🛠️ 企业级特性
- **高性能**：支持100+并发Agent实例
- **可观测性**：完整的监控、日志、指标系统
- **安全可靠**：权限控制、错误处理、自动重试

## 🏗️ 架构设计

### 分层架构图

```
应用层 (Application Layer)
├── 聊天机器人
├── 工作流引擎
└── 智能助手

Agent核心层 (Agent Core Layer)
├── 决策引擎 (AgentEngine)
├── 记忆管理 (MemoryManager)
├── 上下文管理 (ContextManager)
├── 任务规划 (PlanningEngine)
├── 执行控制 (ExecutionEngine)
└── 状态管理 (StateManager)

工具抽象层 (Tool Abstraction Layer)
├── 工具注册表 (ToolRegistry)
├── 工具执行器 (ToolExecutor)
└── 结果处理器 (ResultProcessor)

模型适配层 (Model Adapter Layer)
├── OpenAI适配器 (OpenAIAdapter)
├── Claude适配器 (ClaudeAdapter)
├── 国产模型适配器 (QwenAdapter)
└── 统一模型接口 (ModelInterface)

基础设施层 (Infrastructure Layer)
├── 配置管理 (ConfigManager)
├── 日志系统 (LoggingSystem)
└── 监控系统 (MonitoringSystem)
```

## 🚀 快速开始

### 安装

```bash
# 克隆仓库
git clone https://github.com/aier/ai-agent.git
cd ai-agent

# 使用 Poetry 安装依赖
poetry install

# 或使用 pip 安装
pip install -e .
```

### 环境配置

```bash
# 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export QWEN_API_KEY="your-qwen-api-key"
```

### 基础使用

```python
import asyncio
from ai_agent_framework import Agent
from ai_agent_framework.models import OpenAIAdapter
from ai_agent_framework.memory import MemoryManager
from ai_agent_framework.tools import CalculatorTool, WeatherTool, CrawlerTool, CrawlerServiceType
from ai_agent_framework.utils.tool_registry import tool_registry

async def main():
    # 注册工具
    calculator = CalculatorTool()
    weather = WeatherTool()
    crawler = CrawlerTool(
        service_url="http://localhost:8000",
        service_type=CrawlerServiceType.CRAWL4AI
    )

    tool_registry.register_tool(calculator)
    tool_registry.register_tool(weather)
    tool_registry.register_tool(crawler)

    # 创建模型适配器
    model = OpenAIAdapter(
        api_key="your-openai-api-key",
        model_name="gpt-4"
    )

    # 创建记忆管理器
    memory = MemoryManager()

    # 创建Agent
    agent = Agent(
        model=model,
        memory=memory,
        reasoning_mode="react"  # ReAct推理模式
    )

    # 与Agent对话
    response = await agent.chat("请帮我计算 25 * 4，然后查询北京的天气")
    print(response.content)

    # 清理资源
    await memory.close()

# 运行
asyncio.run(main())
```

### 服务注册快速示例

```python
import asyncio
from ai_agent_framework.tools.service_registry_tool import ServiceRegistryTool
from ai_agent_framework.tools.service_adapter import ServiceToolFactory
from ai_agent_framework.utils.tool_registry import ToolRegistry

async def register_service_example():
    # 创建核心组件
    service_registry = ServiceRegistryTool()
    tool_factory = ServiceToolFactory()
    tool_registry = ToolRegistry()

    # 定义服务配置
    service_config = {
        "name": "example_api",
        "description": "示例API服务",
        "base_url": "https://jsonplaceholder.typicode.com",
        "service_type": "rest_api",
        "auth": {"type": "none"},
        "endpoints": [
            {
                "name": "get_posts",
                "path": "/posts",
                "method": "GET",
                "description": "获取文章列表",
                "requires_auth": False,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "userId": {
                            "type": "integer",
                            "description": "用户ID（可选）"
                        }
                    },
                    "required": []
                }
            }
        ]
    }

    # 注册服务
    result = await service_registry.execute({
        "action": "register",
        "service_config": service_config
    })

    if result.success:
        print("✅ 服务注册成功!")

        # 创建动态工具
        from ai_agent_framework.tools.service_registry_tool import ServiceConfig
        config = ServiceConfig(**service_config)
        tools = tool_factory.create_tools_from_config(config)

        # 注册工具
        for tool in tools:
            tool_registry.register_tool(tool)
            print(f"已注册工具: {tool.name}")

        # 使用工具
        get_posts_tool = tool_registry.get_tool("example_api_get_posts")
        if get_posts_tool:
            result = await get_posts_tool.execute({})
            if result.success:
                posts = result.result.get("data", [])
                print(f"✅ 获取到 {len(posts)} 篇文章")
    else:
        print(f"❌ 服务注册失败: {result.error}")

# 运行示例
asyncio.run(register_service_example())
```

### 切换模型

```python
# 切换到Claude模型
from ai_agent_framework.models import ClaudeAdapter

claude_model = ClaudeAdapter(
    api_key="your-anthropic-api-key",
    model_name="claude-3-sonnet-20240229"
)

claude_agent = Agent(
    model=claude_model,
    memory=memory,
    reasoning_mode="react"
)

# 切换到通义千问模型
from ai_agent_framework.models import QwenAdapter

qwen_model = QwenAdapter(
    api_key="your-qwen-api-key",
    model_name="qwen-max"
)

qwen_agent = Agent(
    model=qwen_model,
    memory=memory,
    reasoning_mode="cot"  # 使用思维链推理
)
```

### 运行示例

```bash
# 运行简单聊天机器人
python examples/simple_chatbot.py

# 运行爬虫Agent示例
python examples/crawler_agent_example.py

# 运行爬虫Agent演示任务
python examples/crawler_agent_example.py --demo

# 使用配置文件
python examples/simple_chatbot.py --config examples/config_example.yaml
```

## 🕷️ 爬虫工具集成

框架内置了强大的爬虫工具，支持调用本地部署的爬虫服务：

### 支持的爬虫服务
- **Crawl4AI**: 专为AI应用设计的现代爬虫服务
- **Scrapy Splash**: 基于Scrapy的JavaScript渲染服务
- **Selenium Grid**: 分布式浏览器自动化服务
- **自定义服务**: 支持自定义API接口

### 快速开始

```python
from ai_agent_framework.tools import CrawlerTool, CrawlerServiceType

# 创建爬虫工具
crawler = CrawlerTool(
    service_url="http://localhost:8000",
    service_type=CrawlerServiceType.CRAWL4AI,
    enable_javascript=True
)

# 注册到Agent
tool_registry.register_tool(crawler)

# 使用示例
arguments = {
    "url": "https://example.com",
    "action": "extract_text",
    "selectors": {
        "title": "h1",
        "content": ".main-content"
    }
}

result = await crawler.execute(arguments)
```

### 部署爬虫服务

```bash
# 部署Crawl4AI服务
docker run -d --name crawl4ai -p 8000:8000 crawl4ai/crawl4ai:latest

# 部署Scrapy Splash服务
docker run -d --name splash -p 8050:8050 scrapinghub/splash:latest
```

详细文档请参考：[爬虫工具使用指南](docs/crawler-tool-guide.md)

## 📚 文档结构

### 核心文档
```
docs/
├── 01-需求分析文档.md      # 详细的功能和技术需求分析
├── 02-系统设计文档.md      # 完整的架构设计和接口定义
├── 03-功能开发计划.md      # 详细的开发任务和时间规划
├── api/                   # API参考文档
├── tutorials/             # 使用教程
├── examples/              # 示例代码
└── deployment/            # 部署指南
```

### 服务注册文档
- [服务注册快速指南](docs/服务注册快速指南.md) - 快速上手服务注册功能
- [API参考文档](docs/服务注册API参考.md) - 完整的API接口文档
- [最佳实践](docs/服务注册最佳实践.md) - 生产环境使用建议

### 示例代码
- [完整服务注册示例](examples/service_registration_example.py) - 展示完整的服务注册流程
- [快速注册示例](examples/quick_service_registration.py) - 最简单的注册示例
- [认证配置示例](examples/service_auth_example.py) - 各种认证方式的配置
- [服务验证示例](examples/service_validation_example.py) - 服务测试和验证

### 配置文件模板
- [服务配置模板](configs/services/service_template.yaml) - 完整的配置文件模板
- [天气服务示例](configs/services/weather_service.yaml) - 实际的服务配置示例

## 🛣️ 开发路线图

### 第一阶段：基础架构 ✅
- [x] 项目初始化和开发环境搭建
- [x] 核心接口定义和抽象类设计
- [x] 基础工具类实现（ConfigManager、LoggingSystem、ErrorHandler、ToolRegistry）

### 第二阶段：模型适配 ✅
- [x] OpenAI模型适配器实现（支持GPT-4、GPT-3.5、Function Calling、流式响应）
- [x] Claude模型适配器实现（支持Claude-3系列、Tool Use、流式响应）
- [x] 国产模型适配器实现（通义千问适配器，支持工具调用）

### 第三阶段：核心引擎 ✅
- [x] Agent决策引擎开发（支持ReAct、Chain-of-Thought、直接响应模式）
- [x] 工具执行系统实现（并发执行、权限控制、超时机制、示例工具）
- [x] 记忆管理系统开发（多层次记忆、相似度搜索、自动清理）

### 第四阶段：高级功能 ✅
- [x] 示例应用开发（简单聊天机器人）
- [x] 项目文档和配置示例
- [x] 服务注册与工具生成系统（支持REST API、GraphQL等多种服务类型）
- [x] 全面认证支持（API Key、OAuth2、JWT、Basic Auth等）
- [x] 服务验证测试工具（连接性、认证、性能测试）
- [x] 服务发现功能（OpenAPI自动发现、网络扫描）
- [x] 动态工具生成（根据服务配置自动创建工具）
- [ ] Prompt工程系统
- [ ] 监控和日志系统完善
- [ ] 性能优化和压力测试
- [ ] 单元测试覆盖率提升

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

### 贡献方式
- 🐛 报告Bug和问题
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- 🧪 编写测试用例

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目的启发和支持：
- [LangChain](https://github.com/langchain-ai/langchain) - AI应用开发框架
- [OpenAI Python SDK](https://github.com/openai/openai-python) - OpenAI API客户端
- [Anthropic SDK](https://github.com/anthropics/anthropic-sdk-python) - Claude API客户端

## 📞 联系我们

- **GitHub Issues**: [提交问题](https://github.com/your-org/ai-agent-framework/issues)
- **讨论区**: [GitHub Discussions](https://github.com/your-org/ai-agent-framework/discussions)
- **邮箱**: <EMAIL>

---

**让AI Agent开发变得简单而强大！** 🚀