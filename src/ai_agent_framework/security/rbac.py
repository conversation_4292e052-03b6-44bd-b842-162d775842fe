"""
基于角色的访问控制（RBAC）系统

提供完整的RBAC功能，包括角色管理、权限分配、访问控制等。
"""

import json
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
from pydantic import BaseModel, Field

from ai_agent_framework.utils.logging_system import logging_system


class PermissionType(str, Enum):
    """权限类型枚举"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"
    CREATE = "create"
    UPDATE = "update"
    MANAGE = "manage"


class ResourceType(str, Enum):
    """资源类型枚举"""
    USER = "user"
    ROLE = "role"
    PERMISSION = "permission"
    AGENT = "agent"
    TOOL = "tool"
    MODEL = "model"
    DATA = "data"
    CONFIG = "config"
    LOG = "log"
    SYSTEM = "system"


class Permission(BaseModel):
    """权限模型"""
    
    id: str
    name: str
    description: str
    resource_type: ResourceType
    permission_type: PermissionType
    resource_pattern: str = "*"  # 资源匹配模式，支持通配符
    conditions: Dict[str, Any] = Field(default_factory=dict)  # 额外条件
    created_at: datetime = Field(default_factory=datetime.now)
    
    def matches_resource(self, resource_id: str, resource_type: ResourceType) -> bool:
        """检查权限是否匹配指定资源"""
        if self.resource_type != resource_type:
            return False
        
        # 简单的通配符匹配
        if self.resource_pattern == "*":
            return True
        
        # 支持前缀匹配
        if self.resource_pattern.endswith("*"):
            prefix = self.resource_pattern[:-1]
            return resource_id.startswith(prefix)
        
        # 精确匹配
        return self.resource_pattern == resource_id


class Role(BaseModel):
    """角色模型"""
    
    id: str
    name: str
    description: str
    permissions: List[str] = Field(default_factory=list)  # 权限ID列表
    parent_roles: List[str] = Field(default_factory=list)  # 父角色ID列表（角色继承）
    is_system_role: bool = False  # 是否为系统角色
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    def add_permission(self, permission_id: str) -> None:
        """添加权限"""
        if permission_id not in self.permissions:
            self.permissions.append(permission_id)
            self.updated_at = datetime.now()
    
    def remove_permission(self, permission_id: str) -> None:
        """移除权限"""
        if permission_id in self.permissions:
            self.permissions.remove(permission_id)
            self.updated_at = datetime.now()
    
    def add_parent_role(self, role_id: str) -> None:
        """添加父角色"""
        if role_id not in self.parent_roles:
            self.parent_roles.append(role_id)
            self.updated_at = datetime.now()
    
    def remove_parent_role(self, role_id: str) -> None:
        """移除父角色"""
        if role_id in self.parent_roles:
            self.parent_roles.remove(role_id)
            self.updated_at = datetime.now()


class RBACUser(BaseModel):
    """RBAC用户模型（扩展基础用户模型）"""
    
    id: str
    username: str
    email: Optional[str] = None
    roles: List[str] = Field(default_factory=list)  # 角色ID列表
    direct_permissions: List[str] = Field(default_factory=list)  # 直接分配的权限ID列表
    is_active: bool = True
    is_superuser: bool = False  # 超级用户标志
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    
    def add_role(self, role_id: str) -> None:
        """添加角色"""
        if role_id not in self.roles:
            self.roles.append(role_id)
            self.updated_at = datetime.now()
    
    def remove_role(self, role_id: str) -> None:
        """移除角色"""
        if role_id in self.roles:
            self.roles.remove(role_id)
            self.updated_at = datetime.now()
    
    def add_direct_permission(self, permission_id: str) -> None:
        """添加直接权限"""
        if permission_id not in self.direct_permissions:
            self.direct_permissions.append(permission_id)
            self.updated_at = datetime.now()
    
    def remove_direct_permission(self, permission_id: str) -> None:
        """移除直接权限"""
        if permission_id in self.direct_permissions:
            self.direct_permissions.remove(permission_id)
            self.updated_at = datetime.now()


class AccessRequest(BaseModel):
    """访问请求模型"""
    
    user_id: str
    resource_id: str
    resource_type: ResourceType
    permission_type: PermissionType
    context: Dict[str, Any] = Field(default_factory=dict)  # 请求上下文
    timestamp: datetime = Field(default_factory=datetime.now)


class AccessResult(BaseModel):
    """访问控制结果"""
    
    granted: bool
    reason: str
    matched_permissions: List[str] = Field(default_factory=list)
    user_roles: List[str] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.now)


class RBACStore(ABC):
    """RBAC数据存储抽象接口"""
    
    @abstractmethod
    async def get_user(self, user_id: str) -> Optional[RBACUser]:
        """获取用户"""
        pass
    
    @abstractmethod
    async def save_user(self, user: RBACUser) -> bool:
        """保存用户"""
        pass
    
    @abstractmethod
    async def get_role(self, role_id: str) -> Optional[Role]:
        """获取角色"""
        pass
    
    @abstractmethod
    async def save_role(self, role: Role) -> bool:
        """保存角色"""
        pass
    
    @abstractmethod
    async def get_permission(self, permission_id: str) -> Optional[Permission]:
        """获取权限"""
        pass
    
    @abstractmethod
    async def save_permission(self, permission: Permission) -> bool:
        """保存权限"""
        pass
    
    @abstractmethod
    async def list_users(self) -> List[RBACUser]:
        """列出所有用户"""
        pass
    
    @abstractmethod
    async def list_roles(self) -> List[Role]:
        """列出所有角色"""
        pass
    
    @abstractmethod
    async def list_permissions(self) -> List[Permission]:
        """列出所有权限"""
        pass
    
    @abstractmethod
    async def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        pass
    
    @abstractmethod
    async def delete_role(self, role_id: str) -> bool:
        """删除角色"""
        pass
    
    @abstractmethod
    async def delete_permission(self, permission_id: str) -> bool:
        """删除权限"""
        pass


class MemoryRBACStore(RBACStore):
    """内存RBAC数据存储实现"""
    
    def __init__(self):
        """初始化内存存储"""
        self._users: Dict[str, RBACUser] = {}
        self._roles: Dict[str, Role] = {}
        self._permissions: Dict[str, Permission] = {}
        self._logger = logging_system.get_logger("memory_rbac_store")
        
        # 初始化默认权限和角色
        self._initialize_defaults()
    
    def _initialize_defaults(self):
        """初始化默认权限和角色"""
        # 创建基础权限
        default_permissions = [
            Permission(
                id="read_all",
                name="读取所有资源",
                description="允许读取所有类型的资源",
                resource_type=ResourceType.SYSTEM,
                permission_type=PermissionType.READ,
                resource_pattern="*"
            ),
            Permission(
                id="write_own_data",
                name="写入自己的数据",
                description="允许写入用户自己的数据",
                resource_type=ResourceType.DATA,
                permission_type=PermissionType.WRITE,
                resource_pattern="user:*"
            ),
            Permission(
                id="admin_all",
                name="管理所有资源",
                description="允许管理所有类型的资源",
                resource_type=ResourceType.SYSTEM,
                permission_type=PermissionType.ADMIN,
                resource_pattern="*"
            ),
            Permission(
                id="execute_tools",
                name="执行工具",
                description="允许执行AI工具",
                resource_type=ResourceType.TOOL,
                permission_type=PermissionType.EXECUTE,
                resource_pattern="*"
            ),
            Permission(
                id="manage_agents",
                name="管理AI代理",
                description="允许创建和管理AI代理",
                resource_type=ResourceType.AGENT,
                permission_type=PermissionType.MANAGE,
                resource_pattern="*"
            )
        ]
        
        for permission in default_permissions:
            self._permissions[permission.id] = permission
        
        # 创建基础角色
        default_roles = [
            Role(
                id="guest",
                name="访客",
                description="基础访客角色，只能读取公开资源",
                permissions=["read_all"],
                is_system_role=True
            ),
            Role(
                id="user",
                name="普通用户",
                description="普通用户角色，可以读取和写入自己的数据",
                permissions=["read_all", "write_own_data", "execute_tools"],
                is_system_role=True
            ),
            Role(
                id="agent_manager",
                name="代理管理员",
                description="AI代理管理员角色",
                permissions=["read_all", "write_own_data", "execute_tools", "manage_agents"],
                parent_roles=["user"],
                is_system_role=True
            ),
            Role(
                id="admin",
                name="系统管理员",
                description="系统管理员角色，拥有所有权限",
                permissions=["admin_all"],
                is_system_role=True
            )
        ]
        
        for role in default_roles:
            self._roles[role.id] = role
        
        self._logger.info("已初始化默认RBAC权限和角色")
    
    async def get_user(self, user_id: str) -> Optional[RBACUser]:
        """获取用户"""
        return self._users.get(user_id)
    
    async def save_user(self, user: RBACUser) -> bool:
        """保存用户"""
        try:
            self._users[user.id] = user
            return True
        except Exception as e:
            self._logger.error(f"保存用户失败: {e}")
            return False
    
    async def get_role(self, role_id: str) -> Optional[Role]:
        """获取角色"""
        return self._roles.get(role_id)
    
    async def save_role(self, role: Role) -> bool:
        """保存角色"""
        try:
            self._roles[role.id] = role
            return True
        except Exception as e:
            self._logger.error(f"保存角色失败: {e}")
            return False
    
    async def get_permission(self, permission_id: str) -> Optional[Permission]:
        """获取权限"""
        return self._permissions.get(permission_id)
    
    async def save_permission(self, permission: Permission) -> bool:
        """保存权限"""
        try:
            self._permissions[permission.id] = permission
            return True
        except Exception as e:
            self._logger.error(f"保存权限失败: {e}")
            return False
    
    async def list_users(self) -> List[RBACUser]:
        """列出所有用户"""
        return list(self._users.values())
    
    async def list_roles(self) -> List[Role]:
        """列出所有角色"""
        return list(self._roles.values())
    
    async def list_permissions(self) -> List[Permission]:
        """列出所有权限"""
        return list(self._permissions.values())
    
    async def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        try:
            if user_id in self._users:
                del self._users[user_id]
                return True
            return False
        except Exception as e:
            self._logger.error(f"删除用户失败: {e}")
            return False
    
    async def delete_role(self, role_id: str) -> bool:
        """删除角色"""
        try:
            role = self._roles.get(role_id)
            if role and role.is_system_role:
                self._logger.warning(f"不能删除系统角色: {role_id}")
                return False
            
            if role_id in self._roles:
                del self._roles[role_id]
                return True
            return False
        except Exception as e:
            self._logger.error(f"删除角色失败: {e}")
            return False
    
    async def delete_permission(self, permission_id: str) -> bool:
        """删除权限"""
        try:
            if permission_id in self._permissions:
                del self._permissions[permission_id]
                return True
            return False
        except Exception as e:
            self._logger.error(f"删除权限失败: {e}")
            return False


class RBACManager:
    """RBAC管理器 - 核心访问控制引擎"""

    def __init__(self, store: RBACStore):
        """
        初始化RBAC管理器

        Args:
            store: RBAC数据存储实现
        """
        self.store = store
        self._logger = logging_system.get_logger("rbac_manager")
        self._permission_cache: Dict[str, Set[str]] = {}  # 用户权限缓存
        self._cache_ttl = 300  # 缓存TTL（秒）
        self._cache_timestamps: Dict[str, datetime] = {}

    async def check_access(self, request: AccessRequest) -> AccessResult:
        """
        检查访问权限

        Args:
            request: 访问请求

        Returns:
            AccessResult: 访问控制结果
        """
        try:
            # 获取用户
            user = await self.store.get_user(request.user_id)
            if not user:
                return AccessResult(
                    granted=False,
                    reason=f"用户不存在: {request.user_id}"
                )

            if not user.is_active:
                return AccessResult(
                    granted=False,
                    reason=f"用户已被禁用: {request.user_id}"
                )

            # 超级用户拥有所有权限
            if user.is_superuser:
                return AccessResult(
                    granted=True,
                    reason="超级用户拥有所有权限",
                    user_roles=user.roles
                )

            # 获取用户的所有权限
            user_permissions = await self._get_user_permissions(user)

            # 检查权限匹配
            matched_permissions = []
            for permission_id in user_permissions:
                permission = await self.store.get_permission(permission_id)
                if permission and self._permission_matches(permission, request):
                    matched_permissions.append(permission_id)

            if matched_permissions:
                return AccessResult(
                    granted=True,
                    reason=f"权限匹配: {', '.join(matched_permissions)}",
                    matched_permissions=matched_permissions,
                    user_roles=user.roles
                )
            else:
                return AccessResult(
                    granted=False,
                    reason=f"没有匹配的权限访问 {request.resource_type}:{request.resource_id}",
                    user_roles=user.roles
                )

        except Exception as e:
            self._logger.error(f"访问控制检查失败: {e}")
            return AccessResult(
                granted=False,
                reason=f"访问控制检查失败: {str(e)}"
            )

    async def _get_user_permissions(self, user: RBACUser) -> Set[str]:
        """
        获取用户的所有权限（包括角色继承的权限）

        Args:
            user: 用户对象

        Returns:
            Set[str]: 权限ID集合
        """
        # 检查缓存
        cache_key = f"user_permissions:{user.id}"
        if self._is_cache_valid(cache_key):
            return self._permission_cache[cache_key]

        permissions = set()

        # 添加直接分配的权限
        permissions.update(user.direct_permissions)

        # 添加角色权限（包括继承）
        for role_id in user.roles:
            role_permissions = await self._get_role_permissions(role_id, set())
            permissions.update(role_permissions)

        # 更新缓存
        self._permission_cache[cache_key] = permissions
        self._cache_timestamps[cache_key] = datetime.now()

        return permissions

    async def _get_role_permissions(self, role_id: str, visited: Set[str]) -> Set[str]:
        """
        递归获取角色权限（处理角色继承）

        Args:
            role_id: 角色ID
            visited: 已访问的角色ID集合（防止循环继承）

        Returns:
            Set[str]: 权限ID集合
        """
        if role_id in visited:
            self._logger.warning(f"检测到角色循环继承: {role_id}")
            return set()

        visited.add(role_id)

        role = await self.store.get_role(role_id)
        if not role or not role.is_active:
            return set()

        permissions = set(role.permissions)

        # 递归获取父角色权限
        for parent_role_id in role.parent_roles:
            parent_permissions = await self._get_role_permissions(parent_role_id, visited.copy())
            permissions.update(parent_permissions)

        return permissions

    def _permission_matches(self, permission: Permission, request: AccessRequest) -> bool:
        """
        检查权限是否匹配访问请求

        Args:
            permission: 权限对象
            request: 访问请求

        Returns:
            bool: 是否匹配
        """
        # 检查权限类型
        if permission.permission_type != request.permission_type:
            # 管理员权限可以匹配所有操作
            if permission.permission_type != PermissionType.ADMIN:
                return False

        # 检查资源匹配
        if not permission.matches_resource(request.resource_id, request.resource_type):
            return False

        # 检查额外条件
        if permission.conditions:
            return self._check_conditions(permission.conditions, request)

        return True

    def _check_conditions(self, conditions: Dict[str, Any], request: AccessRequest) -> bool:
        """
        检查权限条件

        Args:
            conditions: 权限条件
            request: 访问请求

        Returns:
            bool: 条件是否满足
        """
        # 这里可以实现复杂的条件检查逻辑
        # 例如：时间限制、IP限制、资源属性检查等

        # 示例：检查时间限制
        if "time_range" in conditions:
            time_range = conditions["time_range"]
            current_hour = datetime.now().hour
            if not (time_range.get("start", 0) <= current_hour <= time_range.get("end", 23)):
                return False

        # 示例：检查用户属性
        if "user_attributes" in conditions:
            user_attrs = conditions["user_attributes"]
            request_attrs = request.context.get("user_attributes", {})
            for key, expected_value in user_attrs.items():
                if request_attrs.get(key) != expected_value:
                    return False

        return True

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache_timestamps:
            return False

        cache_time = self._cache_timestamps[cache_key]
        return (datetime.now() - cache_time).total_seconds() < self._cache_ttl

    def clear_cache(self, user_id: Optional[str] = None) -> None:
        """
        清除权限缓存

        Args:
            user_id: 用户ID，如果不指定则清除所有缓存
        """
        if user_id:
            cache_key = f"user_permissions:{user_id}"
            self._permission_cache.pop(cache_key, None)
            self._cache_timestamps.pop(cache_key, None)
        else:
            self._permission_cache.clear()
            self._cache_timestamps.clear()

        self._logger.info(f"已清除权限缓存: {user_id or '全部'}")

    # 用户管理方法
    async def create_user(
        self,
        user_id: str,
        username: str,
        email: Optional[str] = None,
        roles: Optional[List[str]] = None,
        is_superuser: bool = False
    ) -> bool:
        """
        创建用户

        Args:
            user_id: 用户ID
            username: 用户名
            email: 邮箱
            roles: 角色列表
            is_superuser: 是否为超级用户

        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查用户是否已存在
            existing_user = await self.store.get_user(user_id)
            if existing_user:
                self._logger.warning(f"用户已存在: {user_id}")
                return False

            user = RBACUser(
                id=user_id,
                username=username,
                email=email,
                roles=roles or ["user"],  # 默认分配普通用户角色
                is_superuser=is_superuser
            )

            success = await self.store.save_user(user)
            if success:
                self._logger.info(f"用户创建成功: {user_id}")
                self.clear_cache(user_id)

            return success

        except Exception as e:
            self._logger.error(f"创建用户失败: {e}")
            return False

    async def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """
        为用户分配角色

        Args:
            user_id: 用户ID
            role_id: 角色ID

        Returns:
            bool: 分配是否成功
        """
        try:
            user = await self.store.get_user(user_id)
            if not user:
                self._logger.warning(f"用户不存在: {user_id}")
                return False

            role = await self.store.get_role(role_id)
            if not role:
                self._logger.warning(f"角色不存在: {role_id}")
                return False

            user.add_role(role_id)
            success = await self.store.save_user(user)

            if success:
                self._logger.info(f"角色分配成功: 用户 {user_id} 获得角色 {role_id}")
                self.clear_cache(user_id)

            return success

        except Exception as e:
            self._logger.error(f"分配角色失败: {e}")
            return False

    async def revoke_role_from_user(self, user_id: str, role_id: str) -> bool:
        """
        撤销用户角色

        Args:
            user_id: 用户ID
            role_id: 角色ID

        Returns:
            bool: 撤销是否成功
        """
        try:
            user = await self.store.get_user(user_id)
            if not user:
                self._logger.warning(f"用户不存在: {user_id}")
                return False

            user.remove_role(role_id)
            success = await self.store.save_user(user)

            if success:
                self._logger.info(f"角色撤销成功: 用户 {user_id} 失去角色 {role_id}")
                self.clear_cache(user_id)

            return success

        except Exception as e:
            self._logger.error(f"撤销角色失败: {e}")
            return False

    async def assign_permission_to_user(self, user_id: str, permission_id: str) -> bool:
        """
        为用户直接分配权限

        Args:
            user_id: 用户ID
            permission_id: 权限ID

        Returns:
            bool: 分配是否成功
        """
        try:
            user = await self.store.get_user(user_id)
            if not user:
                self._logger.warning(f"用户不存在: {user_id}")
                return False

            permission = await self.store.get_permission(permission_id)
            if not permission:
                self._logger.warning(f"权限不存在: {permission_id}")
                return False

            user.add_direct_permission(permission_id)
            success = await self.store.save_user(user)

            if success:
                self._logger.info(f"权限分配成功: 用户 {user_id} 获得权限 {permission_id}")
                self.clear_cache(user_id)

            return success

        except Exception as e:
            self._logger.error(f"分配权限失败: {e}")
            return False

    # 角色管理方法
    async def create_role(
        self,
        role_id: str,
        name: str,
        description: str,
        permissions: Optional[List[str]] = None,
        parent_roles: Optional[List[str]] = None
    ) -> bool:
        """
        创建角色

        Args:
            role_id: 角色ID
            name: 角色名称
            description: 角色描述
            permissions: 权限列表
            parent_roles: 父角色列表

        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查角色是否已存在
            existing_role = await self.store.get_role(role_id)
            if existing_role:
                self._logger.warning(f"角色已存在: {role_id}")
                return False

            role = Role(
                id=role_id,
                name=name,
                description=description,
                permissions=permissions or [],
                parent_roles=parent_roles or []
            )

            success = await self.store.save_role(role)
            if success:
                self._logger.info(f"角色创建成功: {role_id}")
                self.clear_cache()  # 清除所有缓存

            return success

        except Exception as e:
            self._logger.error(f"创建角色失败: {e}")
            return False

    async def assign_permission_to_role(self, role_id: str, permission_id: str) -> bool:
        """
        为角色分配权限

        Args:
            role_id: 角色ID
            permission_id: 权限ID

        Returns:
            bool: 分配是否成功
        """
        try:
            role = await self.store.get_role(role_id)
            if not role:
                self._logger.warning(f"角色不存在: {role_id}")
                return False

            permission = await self.store.get_permission(permission_id)
            if not permission:
                self._logger.warning(f"权限不存在: {permission_id}")
                return False

            role.add_permission(permission_id)
            success = await self.store.save_role(role)

            if success:
                self._logger.info(f"权限分配成功: 角色 {role_id} 获得权限 {permission_id}")
                self.clear_cache()  # 清除所有缓存

            return success

        except Exception as e:
            self._logger.error(f"为角色分配权限失败: {e}")
            return False

    # 权限管理方法
    async def create_permission(
        self,
        permission_id: str,
        name: str,
        description: str,
        resource_type: ResourceType,
        permission_type: PermissionType,
        resource_pattern: str = "*",
        conditions: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        创建权限

        Args:
            permission_id: 权限ID
            name: 权限名称
            description: 权限描述
            resource_type: 资源类型
            permission_type: 权限类型
            resource_pattern: 资源匹配模式
            conditions: 额外条件

        Returns:
            bool: 创建是否成功
        """
        try:
            # 检查权限是否已存在
            existing_permission = await self.store.get_permission(permission_id)
            if existing_permission:
                self._logger.warning(f"权限已存在: {permission_id}")
                return False

            permission = Permission(
                id=permission_id,
                name=name,
                description=description,
                resource_type=resource_type,
                permission_type=permission_type,
                resource_pattern=resource_pattern,
                conditions=conditions or {}
            )

            success = await self.store.save_permission(permission)
            if success:
                self._logger.info(f"权限创建成功: {permission_id}")

            return success

        except Exception as e:
            self._logger.error(f"创建权限失败: {e}")
            return False

    # 查询方法
    async def get_user_roles(self, user_id: str) -> List[Role]:
        """获取用户的所有角色"""
        user = await self.store.get_user(user_id)
        if not user:
            return []

        roles = []
        for role_id in user.roles:
            role = await self.store.get_role(role_id)
            if role:
                roles.append(role)

        return roles

    async def get_user_permissions(self, user_id: str) -> List[Permission]:
        """获取用户的所有权限"""
        user = await self.store.get_user(user_id)
        if not user:
            return []

        permission_ids = await self._get_user_permissions(user)
        permissions = []

        for permission_id in permission_ids:
            permission = await self.store.get_permission(permission_id)
            if permission:
                permissions.append(permission)

        return permissions

    async def get_role_users(self, role_id: str) -> List[RBACUser]:
        """获取拥有指定角色的所有用户"""
        users = await self.store.list_users()
        return [user for user in users if role_id in user.roles]
