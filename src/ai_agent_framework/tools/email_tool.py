"""
邮件服务工具

提供邮件发送和管理功能，支持SMTP和多种邮件服务商。
"""

import asyncio
import smtplib
import ssl
from abc import ABC, abstractmethod
from datetime import datetime
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from email.utils import formataddr
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class EmailAttachment(BaseModel):
    """邮件附件模型"""
    
    filename: str
    content: Union[bytes, str]
    content_type: str = "application/octet-stream"
    encoding: str = "base64"


class EmailMessage(BaseModel):
    """邮件消息模型"""
    
    to: List[str]
    subject: str
    body: str
    cc: Optional[List[str]] = None
    bcc: Optional[List[str]] = None
    reply_to: Optional[str] = None
    attachments: Optional[List[EmailAttachment]] = None
    is_html: bool = False
    priority: str = "normal"  # low, normal, high
    
    def get_all_recipients(self) -> List[str]:
        """获取所有收件人"""
        recipients = self.to.copy()
        if self.cc:
            recipients.extend(self.cc)
        if self.bcc:
            recipients.extend(self.bcc)
        return recipients


class EmailProvider(ABC):
    """邮件服务提供商抽象基类"""
    
    @abstractmethod
    async def send_email(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """发送邮件"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接"""
        pass


class SMTPProvider(EmailProvider):
    """SMTP邮件服务提供商"""
    
    def __init__(
        self,
        host: str,
        port: int = 587,
        username: str = "",
        password: str = "",
        use_tls: bool = True,
        use_ssl: bool = False,
        timeout: int = 30
    ):
        """
        初始化SMTP提供商
        
        Args:
            host: SMTP服务器地址
            port: SMTP端口
            username: 用户名
            password: 密码
            use_tls: 是否使用TLS
            use_ssl: 是否使用SSL
            timeout: 连接超时时间
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.use_ssl = use_ssl
        self.timeout = timeout
        self._logger = logging_system.get_logger("smtp_provider")
    
    async def send_email(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """发送邮件"""
        try:
            # 在线程池中执行同步SMTP操作
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._send_email_sync, message, sender, sender_name)
        
        except Exception as e:
            self._logger.error(f"发送邮件失败: {e}")
            return False
    
    def _send_email_sync(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """同步发送邮件"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            
            # 设置发件人
            if sender_name:
                msg['From'] = formataddr((sender_name, sender))
            else:
                msg['From'] = sender
            
            # 设置收件人
            msg['To'] = ', '.join(message.to)
            if message.cc:
                msg['Cc'] = ', '.join(message.cc)
            
            # 设置主题和其他头部
            msg['Subject'] = message.subject
            msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')
            
            if message.reply_to:
                msg['Reply-To'] = message.reply_to
            
            # 设置优先级
            if message.priority == "high":
                msg['X-Priority'] = '1'
                msg['X-MSMail-Priority'] = 'High'
            elif message.priority == "low":
                msg['X-Priority'] = '5'
                msg['X-MSMail-Priority'] = 'Low'
            
            # 添加邮件正文
            if message.is_html:
                msg.attach(MIMEText(message.body, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(message.body, 'plain', 'utf-8'))
            
            # 添加附件
            if message.attachments:
                for attachment in message.attachments:
                    self._add_attachment(msg, attachment)
            
            # 连接SMTP服务器并发送
            if self.use_ssl:
                server = smtplib.SMTP_SSL(self.host, self.port, timeout=self.timeout)
            else:
                server = smtplib.SMTP(self.host, self.port, timeout=self.timeout)
            
            try:
                if self.use_tls and not self.use_ssl:
                    server.starttls(context=ssl.create_default_context())
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                # 发送邮件
                recipients = message.get_all_recipients()
                server.send_message(msg, from_addr=sender, to_addrs=recipients)
                
                self._logger.info(f"邮件发送成功: {message.subject} -> {recipients}")
                return True
            
            finally:
                server.quit()
        
        except Exception as e:
            self._logger.error(f"SMTP发送邮件失败: {e}")
            return False
    
    def _add_attachment(self, msg: MIMEMultipart, attachment: EmailAttachment) -> None:
        """添加附件"""
        try:
            if isinstance(attachment.content, str):
                content = attachment.content.encode('utf-8')
            else:
                content = attachment.content
            
            part = MIMEApplication(content, _subtype=attachment.content_type.split('/')[-1])
            part.add_header('Content-Disposition', 'attachment', filename=attachment.filename)
            msg.attach(part)
        
        except Exception as e:
            self._logger.error(f"添加附件失败: {e}")
    
    async def test_connection(self) -> bool:
        """测试SMTP连接"""
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._test_connection_sync)
        
        except Exception as e:
            self._logger.error(f"测试SMTP连接失败: {e}")
            return False
    
    def _test_connection_sync(self) -> bool:
        """同步测试连接"""
        try:
            if self.use_ssl:
                server = smtplib.SMTP_SSL(self.host, self.port, timeout=self.timeout)
            else:
                server = smtplib.SMTP(self.host, self.port, timeout=self.timeout)
            
            try:
                if self.use_tls and not self.use_ssl:
                    server.starttls(context=ssl.create_default_context())
                
                if self.username and self.password:
                    server.login(self.username, self.password)
                
                return True
            
            finally:
                server.quit()
        
        except Exception as e:
            self._logger.error(f"SMTP连接测试失败: {e}")
            return False


class EmailConfig(BaseModel):
    """邮件配置"""
    
    provider_type: str = "smtp"  # smtp, sendgrid, ses, etc.
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_use_tls: bool = True
    smtp_use_ssl: bool = False
    smtp_timeout: int = 30
    
    # 默认发件人信息
    default_sender: Optional[str] = None
    default_sender_name: Optional[str] = None
    
    # API配置（用于第三方服务）
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    region: Optional[str] = None


class EmailTool(ToolInterface):
    """邮件服务工具"""
    
    def __init__(self, config: Optional[EmailConfig] = None):
        """
        初始化邮件工具
        
        Args:
            config: 邮件配置
        """

        self.config = config or EmailConfig()
        self._provider: Optional[EmailProvider] = None
        self._logger = logging_system.get_logger("email_tool")
        
        # 初始化邮件提供商
        self._initialize_provider()
    
    def _initialize_provider(self) -> None:
        """初始化邮件提供商"""
        try:
            if self.config.provider_type == "smtp":
                if not self.config.smtp_host:
                    raise ValueError("SMTP配置缺少host参数")

                self._provider = SMTPProvider(
                    host=self.config.smtp_host,
                    port=self.config.smtp_port,
                    username=self.config.smtp_username or "",
                    password=self.config.smtp_password or "",
                    use_tls=self.config.smtp_use_tls,
                    use_ssl=self.config.smtp_use_ssl,
                    timeout=self.config.smtp_timeout
                )

            elif self.config.provider_type == "sendgrid":
                try:
                    from ai_agent_framework.tools.adapters.email_adapters import SendGridProvider
                    if not self.config.api_key:
                        raise ValueError("SendGrid配置缺少api_key参数")

                    self._provider = SendGridProvider(
                        api_key=self.config.api_key,
                        timeout=self.config.smtp_timeout
                    )
                except ImportError as e:
                    raise ImportError(f"SendGrid适配器依赖缺失: {e}")

            elif self.config.provider_type == "amazon_ses":
                try:
                    from ai_agent_framework.tools.adapters.email_adapters import AmazonSESProvider
                    if not all([self.config.api_key, self.config.api_secret]):
                        raise ValueError("Amazon SES配置缺少api_key和api_secret参数")

                    self._provider = AmazonSESProvider(
                        access_key_id=self.config.api_key,
                        secret_access_key=self.config.api_secret,
                        region=self.config.region or "us-east-1",
                        timeout=self.config.smtp_timeout
                    )
                except ImportError as e:
                    raise ImportError(f"Amazon SES适配器依赖缺失: {e}")

            elif self.config.provider_type == "mailgun":
                try:
                    from ai_agent_framework.tools.adapters.email_adapters import MailgunProvider
                    if not all([self.config.api_key, self.config.region]):  # region用作domain
                        raise ValueError("Mailgun配置缺少api_key和domain参数")

                    self._provider = MailgunProvider(
                        api_key=self.config.api_key,
                        domain=self.config.region,  # 使用region字段存储domain
                        timeout=self.config.smtp_timeout
                    )
                except ImportError as e:
                    raise ImportError(f"Mailgun适配器依赖缺失: {e}")

            else:
                raise ValueError(f"不支持的邮件提供商类型: {self.config.provider_type}")

            self._logger.info(f"邮件提供商初始化成功: {self.config.provider_type}")

        except Exception as e:
            self._logger.error(f"初始化邮件提供商失败: {e}")
            raise
    
    @property
    def name(self) -> str:
        return "email_tool"
    
    @property
    def description(self) -> str:
        return "邮件发送和管理工具，支持SMTP和多种邮件服务商"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": ["send", "test_connection", "validate_email"]
                },
                "to": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "收件人邮箱地址列表"
                },
                "subject": {
                    "type": "string",
                    "description": "邮件主题"
                },
                "body": {
                    "type": "string",
                    "description": "邮件正文"
                },
                "cc": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "抄送邮箱地址列表"
                },
                "bcc": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "密送邮箱地址列表"
                },
                "sender": {
                    "type": "string",
                    "description": "发件人邮箱地址"
                },
                "sender_name": {
                    "type": "string",
                    "description": "发件人姓名"
                },
                "is_html": {
                    "type": "boolean",
                    "description": "是否为HTML格式邮件"
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "normal", "high"],
                    "description": "邮件优先级"
                },
                "reply_to": {
                    "type": "string",
                    "description": "回复地址"
                },
                "attachments": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "filename": {"type": "string"},
                            "content": {"type": "string"},
                            "content_type": {"type": "string"}
                        }
                    },
                    "description": "附件列表"
                },
                "email": {
                    "type": "string",
                    "description": "要验证的邮箱地址"
                }
            },
            "required": ["action"]
        }

    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行邮件操作"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    success=False,
                    error="缺少action参数"
                )

            # 确保提供商已初始化
            if not self._provider:
                return ToolResult(
                    success=False,
                    error="邮件提供商未初始化"
                )

            # 执行对应的操作
            if action == "send":
                return await self._handle_send_email(arguments)
            elif action == "test_connection":
                return await self._handle_test_connection()
            elif action == "validate_email":
                return await self._handle_validate_email(arguments)
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的操作: {action}"
                )

        except Exception as e:
            self._logger.error(f"执行邮件操作失败: {e}")
            return ToolResult(
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _handle_send_email(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理发送邮件"""
        try:
            # 验证必需参数
            required_fields = ["to", "subject", "body"]
            for field in required_fields:
                if field not in arguments:
                    return ToolResult(
                        success=False,
                        error=f"缺少必需参数: {field}"
                    )

            # 获取发件人信息
            sender = arguments.get("sender") or self.config.default_sender
            if not sender:
                return ToolResult(
                    success=False,
                    error="缺少发件人邮箱地址"
                )

            sender_name = arguments.get("sender_name") or self.config.default_sender_name

            # 处理附件
            attachments = []
            if arguments.get("attachments"):
                for att_data in arguments["attachments"]:
                    attachment = EmailAttachment(
                        filename=att_data["filename"],
                        content=att_data["content"],
                        content_type=att_data.get("content_type", "application/octet-stream")
                    )
                    attachments.append(attachment)

            # 创建邮件消息
            message = EmailMessage(
                to=arguments["to"],
                subject=arguments["subject"],
                body=arguments["body"],
                cc=arguments.get("cc"),
                bcc=arguments.get("bcc"),
                reply_to=arguments.get("reply_to"),
                attachments=attachments if attachments else None,
                is_html=arguments.get("is_html", False),
                priority=arguments.get("priority", "normal")
            )

            # 发送邮件
            success = await self._provider.send_email(message, sender, sender_name)

            if success:
                return ToolResult(
                    success=True,
                    result={
                        "message": "邮件发送成功",
                        "recipients": message.get_all_recipients(),
                        "subject": message.subject,
                        "timestamp": datetime.now().isoformat()
                    }
                )
            else:
                return ToolResult(
                    success=False,
                    error="邮件发送失败"
                )

        except Exception as e:
            self._logger.error(f"发送邮件失败: {e}")
            return ToolResult(
                success=False,
                error=f"发送邮件失败: {str(e)}"
            )

    async def _handle_test_connection(self) -> ToolResult:
        """处理测试连接"""
        try:
            success = await self._provider.test_connection()

            if success:
                return ToolResult(
                    success=True,
                    result={
                        "message": "邮件服务连接测试成功",
                        "provider": self.config.provider_type,
                        "timestamp": datetime.now().isoformat()
                    }
                )
            else:
                return ToolResult(
                    success=False,
                    error="邮件服务连接测试失败"
                )

        except Exception as e:
            self._logger.error(f"测试连接失败: {e}")
            return ToolResult(
                success=False,
                error=f"测试连接失败: {str(e)}"
            )

    async def _handle_validate_email(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理邮箱地址验证"""
        try:
            email = arguments.get("email")
            if not email:
                return ToolResult(
                    success=False,
                    error="缺少email参数"
                )

            # 基础邮箱格式验证
            is_valid = self._validate_email_format(email)

            return ToolResult(
                success=True,
                result={
                    "email": email,
                    "is_valid": is_valid,
                    "validation_type": "format",
                    "timestamp": datetime.now().isoformat()
                }
            )

        except Exception as e:
            self._logger.error(f"验证邮箱失败: {e}")
            return ToolResult(
                success=False,
                error=f"验证邮箱失败: {str(e)}"
            )

    def _validate_email_format(self, email: str) -> bool:
        """验证邮箱格式"""
        import re

        # 基础邮箱格式正则表达式
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            if action == "send":
                # 验证发送邮件的必需参数
                required_fields = ["to", "subject", "body"]
                for field in required_fields:
                    if field not in arguments:
                        return False

                # 验证邮箱地址格式
                for email in arguments["to"]:
                    if not self._validate_email_format(email):
                        return False

                # 验证抄送和密送邮箱
                for field in ["cc", "bcc"]:
                    if field in arguments and arguments[field]:
                        for email in arguments[field]:
                            if not self._validate_email_format(email):
                                return False

            elif action == "validate_email":
                if "email" not in arguments:
                    return False

            return True

        except Exception as e:
            self._logger.error(f"参数验证失败: {e}")
            return False

    @property
    def requires_confirmation(self) -> bool:
        """发送邮件需要用户确认"""
        return True

    @property
    def timeout_seconds(self) -> Optional[float]:
        """执行超时时间"""
        return 60.0  # 邮件发送可能需要较长时间

    @property
    def name(self) -> str:
        """工具名称"""
        return "email_tool"

    @property
    def description(self) -> str:
        """工具描述"""
        return "邮件发送和管理工具，支持SMTP和多种邮件服务商"
