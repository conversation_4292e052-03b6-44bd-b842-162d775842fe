"""
OAuth2.0认证工具

提供OAuth2.0认证和授权功能，支持多种认证提供商和授权流程。
适用于AI Agent的用户认证、API访问授权等场景。
"""

import asyncio
import base64
import hashlib
import json
import secrets
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlencode, parse_qs, urlparse

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class OAuth2GrantType(Enum):
    """OAuth2.0授权类型枚举"""
    AUTHORIZATION_CODE = "authorization_code"
    CLIENT_CREDENTIALS = "client_credentials"
    RESOURCE_OWNER_PASSWORD = "password"
    REFRESH_TOKEN = "refresh_token"
    DEVICE_CODE = "urn:ietf:params:oauth:grant-type:device_code"


class OAuth2Provider(Enum):
    """OAuth2.0提供商枚举"""
    GOOGLE = "google"
    MICROSOFT = "microsoft"
    GITHUB = "github"
    FACEBOOK = "facebook"
    CUSTOM = "custom"


class OAuth2Token:
    """OAuth2.0令牌数据结构"""
    
    def __init__(
        self,
        access_token: str,
        token_type: str = "Bearer",
        expires_in: Optional[int] = None,
        refresh_token: Optional[str] = None,
        scope: Optional[str] = None,
        id_token: Optional[str] = None,
        **kwargs
    ):
        """
        初始化OAuth2.0令牌
        
        Args:
            access_token: 访问令牌
            token_type: 令牌类型
            expires_in: 过期时间（秒）
            refresh_token: 刷新令牌
            scope: 授权范围
            id_token: ID令牌（OpenID Connect）
            **kwargs: 其他令牌属性
        """
        self.access_token = access_token
        self.token_type = token_type
        self.expires_in = expires_in
        self.refresh_token = refresh_token
        self.scope = scope
        self.id_token = id_token
        self.extra_data = kwargs
        
        # 计算过期时间
        if expires_in:
            self.expires_at = datetime.now() + timedelta(seconds=expires_in)
        else:
            self.expires_at = None
        
        self.created_at = datetime.now()
    
    def is_expired(self) -> bool:
        """检查令牌是否已过期"""
        if not self.expires_at:
            return False
        return datetime.now() >= self.expires_at
    
    def is_valid(self) -> bool:
        """检查令牌是否有效"""
        return bool(self.access_token and not self.is_expired())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "access_token": self.access_token,
            "token_type": self.token_type,
            "expires_in": self.expires_in,
            "refresh_token": self.refresh_token,
            "scope": self.scope,
            "id_token": self.id_token,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat(),
            "is_expired": self.is_expired(),
            "is_valid": self.is_valid(),
            **self.extra_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "OAuth2Token":
        """从字典创建令牌对象"""
        # 提取标准字段
        standard_fields = {
            "access_token", "token_type", "expires_in", 
            "refresh_token", "scope", "id_token"
        }
        
        token_data = {k: v for k, v in data.items() if k in standard_fields}
        extra_data = {k: v for k, v in data.items() if k not in standard_fields and k not in {"expires_at", "created_at", "is_expired", "is_valid"}}
        
        return cls(**token_data, **extra_data)


class OAuth2Config:
    """OAuth2.0配置"""
    
    def __init__(
        self,
        client_id: str,
        client_secret: str,
        authorization_url: str,
        token_url: str,
        redirect_uri: Optional[str] = None,
        scope: Optional[List[str]] = None,
        provider: OAuth2Provider = OAuth2Provider.CUSTOM,
        **kwargs
    ):
        """
        初始化OAuth2.0配置
        
        Args:
            client_id: 客户端ID
            client_secret: 客户端密钥
            authorization_url: 授权URL
            token_url: 令牌URL
            redirect_uri: 重定向URI
            scope: 授权范围
            provider: 提供商类型
            **kwargs: 其他配置参数
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.authorization_url = authorization_url
        self.token_url = token_url
        self.redirect_uri = redirect_uri
        self.scope = scope or []
        self.provider = provider
        self.extra_config = kwargs
    
    @classmethod
    def from_provider(
        cls,
        provider: OAuth2Provider,
        client_id: str,
        client_secret: str,
        redirect_uri: Optional[str] = None,
        scope: Optional[List[str]] = None,
        **kwargs
    ) -> "OAuth2Config":
        """根据提供商创建配置"""
        provider_configs = {
            OAuth2Provider.GOOGLE: {
                "authorization_url": "https://accounts.google.com/o/oauth2/v2/auth",
                "token_url": "https://oauth2.googleapis.com/token",
                "scope": scope or ["openid", "email", "profile"],
            },
            OAuth2Provider.MICROSOFT: {
                "authorization_url": "https://login.microsoftonline.com/common/oauth2/v2.0/authorize",
                "token_url": "https://login.microsoftonline.com/common/oauth2/v2.0/token",
                "scope": scope or ["openid", "email", "profile"],
            },
            OAuth2Provider.GITHUB: {
                "authorization_url": "https://github.com/login/oauth/authorize",
                "token_url": "https://github.com/login/oauth/access_token",
                "scope": scope or ["user:email"],
            },
            OAuth2Provider.FACEBOOK: {
                "authorization_url": "https://www.facebook.com/v18.0/dialog/oauth",
                "token_url": "https://graph.facebook.com/v18.0/oauth/access_token",
                "scope": scope or ["email", "public_profile"],
            },
        }
        
        if provider not in provider_configs:
            raise ValueError(f"不支持的提供商: {provider}")
        
        config = provider_configs[provider]
        return cls(
            client_id=client_id,
            client_secret=client_secret,
            authorization_url=config["authorization_url"],
            token_url=config["token_url"],
            redirect_uri=redirect_uri,
            scope=config["scope"],
            provider=provider,
            **kwargs
        )


class OAuth2Tool(ToolInterface):
    """
    OAuth2.0认证工具
    
    提供OAuth2.0认证和授权功能，支持多种认证提供商和授权流程。
    适用于AI Agent的用户认证、API访问授权等场景。
    """
    
    def __init__(
        self,
        config: Optional[OAuth2Config] = None,
        http_timeout: int = 30,
    ):
        """
        初始化OAuth2.0认证工具
        
        Args:
            config: OAuth2.0配置
            http_timeout: HTTP请求超时时间（秒）
        """
        if not HTTPX_AVAILABLE:
            raise ImportError("OAuth2工具需要httpx库，请运行: pip install httpx")
        
        self.config = config
        self.http_timeout = http_timeout
        
        self._logger = logging_system.get_logger("oauth2_tool")
        self._http_client: Optional[httpx.AsyncClient] = None
        
        # 存储状态和令牌
        self._states: Dict[str, Dict[str, Any]] = {}
        self._tokens: Dict[str, OAuth2Token] = {}
    
    @property
    def name(self) -> str:
        return "oauth2"
    
    @property
    def description(self) -> str:
        return "OAuth2.0认证工具，支持多种认证提供商和授权流程"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "configure", "get_auth_url", "exchange_code",
                        "refresh_token", "validate_token", "revoke_token",
                        "get_user_info", "client_credentials", "device_flow"
                    ],
                    "description": "要执行的OAuth2.0操作"
                },
                "config": {
                    "type": "object",
                    "properties": {
                        "client_id": {"type": "string"},
                        "client_secret": {"type": "string"},
                        "authorization_url": {"type": "string"},
                        "token_url": {"type": "string"},
                        "redirect_uri": {"type": "string"},
                        "scope": {"type": "array", "items": {"type": "string"}},
                        "provider": {"type": "string", "enum": ["google", "microsoft", "github", "facebook", "custom"]}
                    },
                    "description": "OAuth2.0配置"
                },
                "provider": {
                    "type": "string",
                    "enum": ["google", "microsoft", "github", "facebook", "custom"],
                    "description": "认证提供商"
                },
                "client_id": {"type": "string", "description": "客户端ID"},
                "client_secret": {"type": "string", "description": "客户端密钥"},
                "redirect_uri": {"type": "string", "description": "重定向URI"},
                "scope": {"type": "array", "items": {"type": "string"}, "description": "授权范围"},
                "state": {"type": "string", "description": "状态参数"},
                "code": {"type": "string", "description": "授权码"},
                "refresh_token": {"type": "string", "description": "刷新令牌"},
                "access_token": {"type": "string", "description": "访问令牌"},
                "token_id": {"type": "string", "description": "令牌ID"},
                "username": {"type": "string", "description": "用户名"},
                "password": {"type": "string", "description": "密码"},
                "device_code": {"type": "string", "description": "设备码"}
            },
            "required": ["action"],
            "additionalProperties": False
        }

    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行OAuth2.0操作

        Args:
            arguments: 工具参数
            context: 执行上下文

        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )

            # 初始化HTTP客户端
            if not self._http_client:
                self._http_client = httpx.AsyncClient(timeout=self.http_timeout)

            # 执行对应的操作
            if action == "configure":
                result = await self._handle_configure(arguments)
            elif action == "get_auth_url":
                result = await self._handle_get_auth_url(arguments)
            elif action == "exchange_code":
                result = await self._handle_exchange_code(arguments)
            elif action == "refresh_token":
                result = await self._handle_refresh_token(arguments)
            elif action == "validate_token":
                result = await self._handle_validate_token(arguments)
            elif action == "revoke_token":
                result = await self._handle_revoke_token(arguments)
            elif action == "get_user_info":
                result = await self._handle_get_user_info(arguments)
            elif action == "client_credentials":
                result = await self._handle_client_credentials(arguments)
            elif action == "device_flow":
                result = await self._handle_device_flow(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

            return result

        except Exception as e:
            self._logger.error(f"OAuth2.0操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )
        finally:
            # 清理HTTP客户端
            if self._http_client:
                await self._http_client.aclose()
                self._http_client = None

    async def _handle_configure(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理配置操作"""
        config_data = arguments.get("config")
        provider = arguments.get("provider")

        if config_data:
            # 使用自定义配置
            self.config = OAuth2Config(
                client_id=config_data["client_id"],
                client_secret=config_data["client_secret"],
                authorization_url=config_data["authorization_url"],
                token_url=config_data["token_url"],
                redirect_uri=config_data.get("redirect_uri"),
                scope=config_data.get("scope", []),
                provider=OAuth2Provider(config_data.get("provider", "custom"))
            )
        elif provider:
            # 使用预定义提供商配置
            client_id = arguments.get("client_id")
            client_secret = arguments.get("client_secret")

            if not client_id or not client_secret:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="使用预定义提供商时需要提供client_id和client_secret"
                )

            self.config = OAuth2Config.from_provider(
                provider=OAuth2Provider(provider),
                client_id=client_id,
                client_secret=client_secret,
                redirect_uri=arguments.get("redirect_uri"),
                scope=arguments.get("scope")
            )
        else:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="需要提供config或provider参数"
            )

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "configured": True,
                "provider": self.config.provider.value,
                "client_id": self.config.client_id,
                "scope": self.config.scope,
            }
        )

    async def _handle_get_auth_url(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取授权URL操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        # 生成状态参数
        state = arguments.get("state") or secrets.token_urlsafe(32)

        # 生成PKCE参数（如果支持）
        code_verifier = secrets.token_urlsafe(32)
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip('=')

        # 构建授权URL参数
        params = {
            "response_type": "code",
            "client_id": self.config.client_id,
            "redirect_uri": self.config.redirect_uri,
            "scope": " ".join(self.config.scope),
            "state": state,
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
        }

        # 移除空值参数
        params = {k: v for k, v in params.items() if v}

        auth_url = f"{self.config.authorization_url}?{urlencode(params)}"

        # 存储状态信息
        self._states[state] = {
            "code_verifier": code_verifier,
            "redirect_uri": self.config.redirect_uri,
            "created_at": datetime.now(),
        }

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "auth_url": auth_url,
                "state": state,
                "code_verifier": code_verifier,
            }
        )

    async def _handle_exchange_code(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理授权码交换令牌操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        code = arguments.get("code")
        state = arguments.get("state")

        if not code:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: code"
            )

        # 验证状态参数
        state_info = None
        if state and state in self._states:
            state_info = self._states[state]

        # 构建令牌请求参数
        data = {
            "grant_type": OAuth2GrantType.AUTHORIZATION_CODE.value,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret,
            "code": code,
            "redirect_uri": self.config.redirect_uri,
        }

        # 添加PKCE参数
        if state_info and "code_verifier" in state_info:
            data["code_verifier"] = state_info["code_verifier"]

        # 发送令牌请求
        try:
            response = await self._http_client.post(
                self.config.token_url,
                data=data,
                headers={"Accept": "application/json"}
            )
            response.raise_for_status()

            token_data = response.json()
            token = OAuth2Token(**token_data)

            # 存储令牌
            token_id = secrets.token_urlsafe(16)
            self._tokens[token_id] = token

            # 清理状态信息
            if state and state in self._states:
                del self._states[state]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "token_exchanged": True,
                    "token_id": token_id,
                    "token": token.to_dict(),
                }
            )

        except httpx.HTTPStatusError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌交换失败: HTTP {e.response.status_code}"
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌交换失败: {str(e)}"
            )

    async def _handle_refresh_token(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理刷新令牌操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        refresh_token = arguments.get("refresh_token")
        token_id = arguments.get("token_id")

        # 从存储的令牌中获取刷新令牌
        if not refresh_token and token_id and token_id in self._tokens:
            stored_token = self._tokens[token_id]
            refresh_token = stored_token.refresh_token

        if not refresh_token:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少刷新令牌"
            )

        # 构建刷新令牌请求参数
        data = {
            "grant_type": OAuth2GrantType.REFRESH_TOKEN.value,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret,
            "refresh_token": refresh_token,
        }

        try:
            response = await self._http_client.post(
                self.config.token_url,
                data=data,
                headers={"Accept": "application/json"}
            )
            response.raise_for_status()

            token_data = response.json()
            new_token = OAuth2Token(**token_data)

            # 如果没有返回新的刷新令牌，保留原来的
            if not new_token.refresh_token:
                new_token.refresh_token = refresh_token

            # 更新存储的令牌
            if token_id:
                self._tokens[token_id] = new_token
            else:
                token_id = secrets.token_urlsafe(16)
                self._tokens[token_id] = new_token

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "token_refreshed": True,
                    "token_id": token_id,
                    "token": new_token.to_dict(),
                }
            )

        except httpx.HTTPStatusError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌刷新失败: HTTP {e.response.status_code}"
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌刷新失败: {str(e)}"
            )

    async def _handle_validate_token(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理验证令牌操作"""
        access_token = arguments.get("access_token")
        token_id = arguments.get("token_id")

        # 从存储的令牌中获取访问令牌
        token_obj = None
        if not access_token and token_id and token_id in self._tokens:
            token_obj = self._tokens[token_id]
            access_token = token_obj.access_token

        if not access_token:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少访问令牌"
            )

        # 本地验证
        is_valid = True
        validation_info = {"local_validation": True}

        if token_obj:
            is_valid = token_obj.is_valid()
            validation_info.update({
                "is_expired": token_obj.is_expired(),
                "expires_at": token_obj.expires_at.isoformat() if token_obj.expires_at else None,
                "scope": token_obj.scope,
            })

        # JWT令牌验证（如果可用）
        if JWT_AVAILABLE and access_token.count('.') == 2:
            try:
                # 不验证签名的JWT解码（仅获取payload）
                payload = jwt.decode(access_token, options={"verify_signature": False})
                validation_info.update({
                    "jwt_payload": payload,
                    "jwt_exp": payload.get("exp"),
                    "jwt_iat": payload.get("iat"),
                    "jwt_iss": payload.get("iss"),
                    "jwt_aud": payload.get("aud"),
                })

                # 检查JWT过期时间
                if "exp" in payload:
                    jwt_expired = datetime.fromtimestamp(payload["exp"]) <= datetime.now()
                    validation_info["jwt_expired"] = jwt_expired
                    if jwt_expired:
                        is_valid = False

            except Exception as e:
                validation_info["jwt_error"] = str(e)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "valid": is_valid,
                "validation_info": validation_info,
            }
        )

    async def _handle_revoke_token(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理撤销令牌操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        access_token = arguments.get("access_token")
        token_id = arguments.get("token_id")

        # 从存储的令牌中获取访问令牌
        if not access_token and token_id and token_id in self._tokens:
            stored_token = self._tokens[token_id]
            access_token = stored_token.access_token

        if not access_token:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少访问令牌"
            )

        # 构建撤销请求（如果提供商支持）
        revoke_url = self.config.extra_config.get("revoke_url")
        if not revoke_url:
            # 从存储中删除令牌
            if token_id and token_id in self._tokens:
                del self._tokens[token_id]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "revoked": True,
                    "method": "local_removal",
                    "message": "令牌已从本地存储中删除"
                }
            )

        try:
            # 发送撤销请求
            data = {
                "token": access_token,
                "client_id": self.config.client_id,
                "client_secret": self.config.client_secret,
            }

            response = await self._http_client.post(
                revoke_url,
                data=data,
                headers={"Accept": "application/json"}
            )
            response.raise_for_status()

            # 从存储中删除令牌
            if token_id and token_id in self._tokens:
                del self._tokens[token_id]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "revoked": True,
                    "method": "remote_revocation",
                    "message": "令牌已成功撤销"
                }
            )

        except httpx.HTTPStatusError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌撤销失败: HTTP {e.response.status_code}"
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"令牌撤销失败: {str(e)}"
            )

    async def _handle_get_user_info(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取用户信息操作"""
        access_token = arguments.get("access_token")
        token_id = arguments.get("token_id")

        # 从存储的令牌中获取访问令牌
        if not access_token and token_id and token_id in self._tokens:
            stored_token = self._tokens[token_id]
            access_token = stored_token.access_token

        if not access_token:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少访问令牌"
            )

        # 根据提供商确定用户信息端点
        user_info_urls = {
            OAuth2Provider.GOOGLE: "https://www.googleapis.com/oauth2/v2/userinfo",
            OAuth2Provider.MICROSOFT: "https://graph.microsoft.com/v1.0/me",
            OAuth2Provider.GITHUB: "https://api.github.com/user",
            OAuth2Provider.FACEBOOK: "https://graph.facebook.com/me?fields=id,name,email",
        }

        user_info_url = self.config.extra_config.get("user_info_url")
        if not user_info_url and self.config and self.config.provider in user_info_urls:
            user_info_url = user_info_urls[self.config.provider]

        if not user_info_url:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="未配置用户信息端点URL"
            )

        try:
            # 发送用户信息请求
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            }

            response = await self._http_client.get(user_info_url, headers=headers)
            response.raise_for_status()

            user_info = response.json()

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "user_info_retrieved": True,
                    "user_info": user_info,
                    "provider": self.config.provider.value if self.config else "unknown",
                }
            )

        except httpx.HTTPStatusError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取用户信息失败: HTTP {e.response.status_code}"
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取用户信息失败: {str(e)}"
            )

    async def _handle_client_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理客户端凭证流程操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        # 构建客户端凭证请求参数
        data = {
            "grant_type": OAuth2GrantType.CLIENT_CREDENTIALS.value,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret,
        }

        # 添加scope（如果有）
        if self.config.scope:
            data["scope"] = " ".join(self.config.scope)

        try:
            response = await self._http_client.post(
                self.config.token_url,
                data=data,
                headers={"Accept": "application/json"}
            )
            response.raise_for_status()

            token_data = response.json()
            token = OAuth2Token(**token_data)

            # 存储令牌
            token_id = secrets.token_urlsafe(16)
            self._tokens[token_id] = token

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "client_credentials_granted": True,
                    "token_id": token_id,
                    "token": token.to_dict(),
                }
            )

        except httpx.HTTPStatusError as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"客户端凭证授权失败: HTTP {e.response.status_code}"
            )
        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"客户端凭证授权失败: {str(e)}"
            )

    async def _handle_device_flow(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理设备流程操作"""
        if not self.config:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="请先配置OAuth2.0设置"
            )

        device_code = arguments.get("device_code")

        if not device_code:
            # 启动设备流程 - 获取设备码
            device_auth_url = self.config.extra_config.get("device_auth_url")
            if not device_auth_url:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="未配置设备授权端点URL"
                )

            data = {
                "client_id": self.config.client_id,
                "scope": " ".join(self.config.scope) if self.config.scope else "",
            }

            try:
                response = await self._http_client.post(
                    device_auth_url,
                    data=data,
                    headers={"Accept": "application/json"}
                )
                response.raise_for_status()

                device_info = response.json()

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "device_flow_started": True,
                        "device_code": device_info.get("device_code"),
                        "user_code": device_info.get("user_code"),
                        "verification_uri": device_info.get("verification_uri"),
                        "verification_uri_complete": device_info.get("verification_uri_complete"),
                        "expires_in": device_info.get("expires_in"),
                        "interval": device_info.get("interval", 5),
                    }
                )

            except httpx.HTTPStatusError as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"设备流程启动失败: HTTP {e.response.status_code}"
                )
            except Exception as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"设备流程启动失败: {str(e)}"
                )
        else:
            # 轮询令牌 - 使用设备码获取令牌
            data = {
                "grant_type": OAuth2GrantType.DEVICE_CODE.value,
                "client_id": self.config.client_id,
                "device_code": device_code,
            }

            try:
                response = await self._http_client.post(
                    self.config.token_url,
                    data=data,
                    headers={"Accept": "application/json"}
                )

                if response.status_code == 400:
                    # 处理设备流程特定错误
                    error_data = response.json()
                    error_code = error_data.get("error")

                    if error_code == "authorization_pending":
                        return ToolResult(
                            tool_call_id="",
                            success=True,
                            result={
                                "authorization_pending": True,
                                "message": "用户尚未完成授权，请继续轮询"
                            }
                        )
                    elif error_code == "slow_down":
                        return ToolResult(
                            tool_call_id="",
                            success=True,
                            result={
                                "slow_down": True,
                                "message": "轮询频率过高，请减慢轮询间隔"
                            }
                        )
                    elif error_code == "expired_token":
                        return ToolResult(
                            tool_call_id="",
                            success=False,
                            error="设备码已过期，请重新启动设备流程"
                        )
                    elif error_code == "access_denied":
                        return ToolResult(
                            tool_call_id="",
                            success=False,
                            error="用户拒绝了授权请求"
                        )

                response.raise_for_status()

                token_data = response.json()
                token = OAuth2Token(**token_data)

                # 存储令牌
                token_id = secrets.token_urlsafe(16)
                self._tokens[token_id] = token

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "device_flow_completed": True,
                        "token_id": token_id,
                        "token": token.to_dict(),
                    }
                )

            except httpx.HTTPStatusError as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"设备流程令牌获取失败: HTTP {e.response.status_code}"
                )
            except Exception as e:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"设备流程令牌获取失败: {str(e)}"
                )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """
        验证工具参数

        Args:
            arguments: 工具参数

        Returns:
            bool: 参数是否有效
        """
        action = arguments.get("action")
        if not action:
            return False

        valid_actions = [
            "configure", "get_auth_url", "exchange_code",
            "refresh_token", "validate_token", "revoke_token",
            "get_user_info", "client_credentials", "device_flow"
        ]

        if action not in valid_actions:
            return False

        # 验证特定操作的必需参数
        if action == "configure":
            return bool(arguments.get("config") or arguments.get("provider"))
        elif action == "exchange_code":
            return bool(arguments.get("code"))
        elif action in ["refresh_token", "validate_token", "revoke_token", "get_user_info"]:
            return bool(arguments.get("access_token") or arguments.get("token_id"))

        return True
