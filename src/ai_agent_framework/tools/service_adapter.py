"""
AI Agent Framework 服务适配器

提供统一的服务调用接口，支持多种服务类型：
- REST API
- GraphQL
- gRPC
- WebSocket
- SOAP

将外部服务适配为AI Agent可调用的工具。
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin

import aiohttp
import yaml
from pydantic import BaseModel, Field

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.exceptions import ToolError
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager
from ai_agent_framework.tools.service_registry_tool import ServiceConfig, ServiceEndpoint
from ai_agent_framework.utils.logging_system import logging_system


class ServiceAdapter(ABC):
    """服务适配器抽象基类"""
    
    def __init__(self, service_config: ServiceConfig, auth_manager: Optional[ServiceAuthManager] = None):
        """
        初始化服务适配器
        
        Args:
            service_config: 服务配置
            auth_manager: 认证管理器
        """
        self.service_config = service_config
        self.auth_manager = auth_manager
        self._logger = logging_system.get_logger(f"service_adapter_{service_config.name}")
    
    @abstractmethod
    async def call_endpoint(
        self, 
        endpoint: ServiceEndpoint, 
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """调用服务端点"""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass


class RestApiAdapter(ServiceAdapter):
    """REST API适配器"""
    
    def __init__(self, service_config: ServiceConfig, auth_manager: Optional[ServiceAuthManager] = None):
        super().__init__(service_config, auth_manager)
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def call_endpoint(
        self, 
        endpoint: ServiceEndpoint, 
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """调用REST API端点"""
        try:
            # 确保HTTP会话已初始化
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 构建请求URL
            url = urljoin(str(self.service_config.base_url), endpoint.path)
            
            # 处理路径参数
            path_params = {}
            query_params = {}
            body_data = {}
            
            for param_name, param_value in parameters.items():
                param_schema = endpoint.parameters.get("properties", {}).get(param_name, {})
                param_in = param_schema.get("in", "query")
                
                if param_in == "path":
                    path_params[param_name] = param_value
                elif param_in == "query":
                    query_params[param_name] = param_value
                elif param_in == "body":
                    body_data[param_name] = param_value
            
            # 替换路径参数
            for param_name, param_value in path_params.items():
                url = url.replace(f"{{{param_name}}}", str(param_value))
            
            # 准备请求头
            headers = dict(self.service_config.headers)
            
            # 添加认证头
            if self.auth_manager and endpoint.requires_auth:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(
                    self.service_config.name
                )
                headers.update(auth_headers)
            
            # 准备请求数据
            request_kwargs = {
                "headers": headers,
                "timeout": aiohttp.ClientTimeout(total=endpoint.timeout or 30)
            }
            
            if query_params:
                request_kwargs["params"] = query_params
            
            if body_data and endpoint.method in ["POST", "PUT", "PATCH"]:
                if headers.get("Content-Type", "").startswith("application/json"):
                    request_kwargs["json"] = body_data
                else:
                    request_kwargs["data"] = body_data
            
            # 发送请求
            self._logger.info(f"调用REST API: {endpoint.method} {url}")
            
            async with self._session.request(
                endpoint.method,
                url,
                **request_kwargs
            ) as response:
                response_data = {
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "success": response.status < 400
                }
                
                # 解析响应内容
                content_type = response.headers.get("content-type", "")
                if "application/json" in content_type:
                    try:
                        response_data["data"] = await response.json()
                    except Exception:
                        response_data["data"] = await response.text()
                else:
                    response_data["data"] = await response.text()
                
                if not response_data["success"]:
                    response_data["error"] = f"HTTP {response.status}: {response_data['data']}"
                
                return response_data
                
        except Exception as e:
            self._logger.error(f"REST API调用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "status_code": None,
                "data": None
            }
    
    async def health_check(self) -> bool:
        """REST API健康检查"""
        try:
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 使用健康检查路径或根路径
            health_path = self.service_config.health_check_path or "/"
            url = urljoin(str(self.service_config.base_url), health_path)
            
            # 准备认证头
            headers = dict(self.service_config.headers)
            if self.auth_manager:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(
                    self.service_config.name
                )
                headers.update(auth_headers)
            
            async with self._session.get(
                url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                return response.status < 400
                
        except Exception as e:
            self._logger.warning(f"健康检查失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None


class GraphQLAdapter(ServiceAdapter):
    """GraphQL适配器"""
    
    def __init__(self, service_config: ServiceConfig, auth_manager: Optional[ServiceAuthManager] = None):
        super().__init__(service_config, auth_manager)
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def call_endpoint(
        self, 
        endpoint: ServiceEndpoint, 
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """调用GraphQL端点"""
        try:
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # GraphQL通常使用POST方法到单一端点
            url = str(self.service_config.base_url)
            if not url.endswith('/graphql'):
                url = urljoin(url, '/graphql')
            
            # 准备GraphQL查询
            query = parameters.get("query")
            variables = parameters.get("variables", {})
            operation_name = parameters.get("operationName")
            
            if not query:
                return {
                    "success": False,
                    "error": "缺少GraphQL查询",
                    "data": None
                }
            
            # 构建GraphQL请求体
            graphql_request = {
                "query": query,
                "variables": variables
            }
            
            if operation_name:
                graphql_request["operationName"] = operation_name
            
            # 准备请求头
            headers = {
                "Content-Type": "application/json",
                **self.service_config.headers
            }
            
            # 添加认证头
            if self.auth_manager and endpoint.requires_auth:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(
                    self.service_config.name
                )
                headers.update(auth_headers)
            
            # 发送GraphQL请求
            self._logger.info(f"调用GraphQL: {url}")
            
            async with self._session.post(
                url,
                json=graphql_request,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=endpoint.timeout or 30)
            ) as response:
                response_data = {
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "success": response.status < 400
                }
                
                if response.status == 200:
                    try:
                        graphql_response = await response.json()
                        response_data["data"] = graphql_response.get("data")
                        
                        # 检查GraphQL错误
                        if "errors" in graphql_response:
                            response_data["success"] = False
                            response_data["error"] = graphql_response["errors"]
                        
                    except Exception as e:
                        response_data["success"] = False
                        response_data["error"] = f"解析GraphQL响应失败: {str(e)}"
                        response_data["data"] = await response.text()
                else:
                    response_data["success"] = False
                    response_data["error"] = f"HTTP {response.status}"
                    response_data["data"] = await response.text()
                
                return response_data
                
        except Exception as e:
            self._logger.error(f"GraphQL调用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "status_code": None,
                "data": None
            }
    
    async def health_check(self) -> bool:
        """GraphQL健康检查"""
        try:
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            url = str(self.service_config.base_url)
            if not url.endswith('/graphql'):
                url = urljoin(url, '/graphql')
            
            # 使用内省查询进行健康检查
            introspection_query = {
                "query": "{ __schema { types { name } } }"
            }
            
            headers = {
                "Content-Type": "application/json",
                **self.service_config.headers
            }
            
            if self.auth_manager:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(
                    self.service_config.name
                )
                headers.update(auth_headers)
            
            async with self._session.post(
                url,
                json=introspection_query,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                return response.status == 200
                
        except Exception as e:
            self._logger.warning(f"GraphQL健康检查失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None


class DynamicServiceTool(ToolInterface):
    """
    动态服务工具

    根据服务配置动态创建的工具，可以调用外部服务的端点。
    """

    def __init__(
        self,
        service_config: ServiceConfig,
        endpoint: ServiceEndpoint,
        auth_manager: Optional[ServiceAuthManager] = None
    ):
        """
        初始化动态服务工具

        Args:
            service_config: 服务配置
            endpoint: 端点配置
            auth_manager: 认证管理器
        """
        self.service_config = service_config
        self.endpoint = endpoint
        self.auth_manager = auth_manager
        self._logger = logging_system.get_logger(f"dynamic_service_tool_{service_config.name}_{endpoint.name}")

        # 创建适配器
        self.adapter = self._create_adapter()

    def _create_adapter(self) -> ServiceAdapter:
        """创建服务适配器"""
        if self.service_config.service_type.value == "rest_api":
            return RestApiAdapter(self.service_config, self.auth_manager)
        elif self.service_config.service_type.value == "graphql":
            return GraphQLAdapter(self.service_config, self.auth_manager)
        else:
            # 默认使用REST API适配器
            return RestApiAdapter(self.service_config, self.auth_manager)

    @property
    def name(self) -> str:
        """工具名称"""
        return f"{self.service_config.name}_{self.endpoint.name}"

    @property
    def description(self) -> str:
        """工具描述"""
        return f"{self.service_config.description} - {self.endpoint.description}"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return self.endpoint.parameters

    @property
    def requires_confirmation(self) -> bool:
        """是否需要用户确认"""
        # 对于修改操作（POST、PUT、DELETE）需要确认
        return self.endpoint.method in ["POST", "PUT", "DELETE", "PATCH"]

    @property
    def timeout_seconds(self) -> Optional[float]:
        """执行超时时间"""
        return self.endpoint.timeout

    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """执行服务调用"""
        try:
            # 验证参数
            if not await self.validate_arguments(arguments):
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="参数验证失败"
                )

            # 调用服务端点
            result = await self.adapter.call_endpoint(self.endpoint, arguments, context)

            if result["success"]:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "service": self.service_config.name,
                        "endpoint": self.endpoint.name,
                        "status_code": result.get("status_code"),
                        "data": result.get("data"),
                        "headers": result.get("headers", {})
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=result.get("error", "服务调用失败"),
                    result={
                        "service": self.service_config.name,
                        "endpoint": self.endpoint.name,
                        "status_code": result.get("status_code"),
                        "data": result.get("data")
                    }
                )

        except Exception as e:
            self._logger.error(f"动态服务工具执行失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"执行失败: {str(e)}"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        try:
            required_params = self.endpoint.parameters.get("required", [])

            # 检查必需参数
            for param in required_params:
                if param not in arguments:
                    self._logger.error(f"缺少必需参数: {param}")
                    return False

            # 检查参数类型（简单验证）
            properties = self.endpoint.parameters.get("properties", {})
            for param_name, param_value in arguments.items():
                if param_name in properties:
                    expected_type = properties[param_name].get("type")
                    if expected_type and not self._validate_param_type(param_value, expected_type):
                        self._logger.error(f"参数类型错误: {param_name}")
                        return False

            return True

        except Exception as e:
            self._logger.error(f"参数验证失败: {str(e)}")
            return False

    def _validate_param_type(self, value: Any, expected_type: str) -> bool:
        """验证参数类型"""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        else:
            return True  # 未知类型，跳过验证

    async def close(self):
        """关闭适配器"""
        if hasattr(self.adapter, 'close'):
            await self.adapter.close()


class ServiceToolFactory:
    """
    服务工具工厂

    根据服务配置创建动态服务工具。
    """

    def __init__(self, auth_manager: Optional[ServiceAuthManager] = None):
        """
        初始化工具工厂

        Args:
            auth_manager: 认证管理器
        """
        self.auth_manager = auth_manager
        self._logger = logging_system.get_logger("service_tool_factory")

    def create_tools_from_config(self, service_config: ServiceConfig) -> List[DynamicServiceTool]:
        """
        从服务配置创建工具列表

        Args:
            service_config: 服务配置

        Returns:
            List[DynamicServiceTool]: 动态服务工具列表
        """
        tools = []

        try:
            for endpoint in service_config.endpoints:
                tool = DynamicServiceTool(
                    service_config=service_config,
                    endpoint=endpoint,
                    auth_manager=self.auth_manager
                )
                tools.append(tool)

            self._logger.info(f"为服务 {service_config.name} 创建了 {len(tools)} 个工具")

        except Exception as e:
            self._logger.error(f"创建服务工具失败: {str(e)}")

        return tools

    def create_tools_from_config_file(self, config_file: Union[str, Path]) -> List[DynamicServiceTool]:
        """
        从配置文件创建工具列表

        Args:
            config_file: 配置文件路径

        Returns:
            List[DynamicServiceTool]: 动态服务工具列表
        """
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_path}")

            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    config_data = yaml.safe_load(f)
                else:
                    config_data = json.load(f)

            service_config = ServiceConfig(**config_data)
            return self.create_tools_from_config(service_config)

        except Exception as e:
            self._logger.error(f"从配置文件创建工具失败: {str(e)}")
            return []

    async def create_and_register_tools(
        self,
        service_configs: List[ServiceConfig],
        tool_registry
    ) -> int:
        """
        创建并注册服务工具到工具注册表

        Args:
            service_configs: 服务配置列表
            tool_registry: 工具注册表

        Returns:
            int: 成功注册的工具数量
        """
        registered_count = 0

        try:
            for service_config in service_configs:
                tools = self.create_tools_from_config(service_config)

                for tool in tools:
                    try:
                        # 注册工具到工具注册表
                        tool_registry.register_tool(
                            tool=tool,
                            permissions=set(),  # 可以根据需要设置权限
                            max_concurrent=1
                        )
                        registered_count += 1

                    except Exception as e:
                        self._logger.error(f"注册工具失败 {tool.name}: {str(e)}")

            self._logger.info(f"成功注册 {registered_count} 个服务工具")

        except Exception as e:
            self._logger.error(f"批量注册工具失败: {str(e)}")

        return registered_count
