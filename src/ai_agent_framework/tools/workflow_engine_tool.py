"""
工作流引擎工具

提供工作流编排和执行引擎，支持复杂业务流程自动化。
"""

import asyncio
import json
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
from pydantic import BaseModel, Field
import uuid

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    SKIPPED = "skipped"      # 已跳过
    CANCELLED = "cancelled"  # 已取消


class TaskType(str, Enum):
    """任务类型枚举"""
    HTTP_REQUEST = "http_request"    # HTTP请求任务
    SCRIPT = "script"                # 脚本执行任务
    FUNCTION = "function"            # 函数调用任务
    CONDITION = "condition"          # 条件判断任务
    PARALLEL = "parallel"            # 并行任务组
    SEQUENTIAL = "sequential"        # 顺序任务组
    DELAY = "delay"                  # 延迟任务
    APPROVAL = "approval"            # 人工审批任务
    NOTIFICATION = "notification"    # 通知任务


class WorkflowTask(BaseModel):
    """工作流任务模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str = ""
    task_type: TaskType
    config: Dict[str, Any] = Field(default_factory=dict)
    dependencies: List[str] = Field(default_factory=list)  # 依赖的任务ID
    conditions: Dict[str, Any] = Field(default_factory=dict)  # 执行条件
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: Optional[int] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def start(self) -> None:
        """开始执行任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete(self, result: Optional[Dict[str, Any]] = None) -> None:
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
    
    def fail(self, error: str) -> None:
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error = error
    
    def skip(self) -> None:
        """跳过任务"""
        self.status = TaskStatus.SKIPPED
        self.completed_at = datetime.now()


class Workflow(BaseModel):
    """工作流模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str = ""
    version: str = "1.0"
    tasks: List[WorkflowTask] = Field(default_factory=list)
    variables: Dict[str, Any] = Field(default_factory=dict)  # 工作流变量
    status: WorkflowStatus = WorkflowStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_by: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def add_task(self, task: WorkflowTask) -> None:
        """添加任务"""
        self.tasks.append(task)
    
    def get_task(self, task_id: str) -> Optional[WorkflowTask]:
        """获取任务"""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def get_ready_tasks(self) -> List[WorkflowTask]:
        """获取准备执行的任务"""
        ready_tasks = []
        
        for task in self.tasks:
            if task.status != TaskStatus.PENDING:
                continue
            
            # 检查依赖是否完成
            dependencies_met = True
            for dep_id in task.dependencies:
                dep_task = self.get_task(dep_id)
                if not dep_task or dep_task.status != TaskStatus.COMPLETED:
                    dependencies_met = False
                    break
            
            if dependencies_met:
                ready_tasks.append(task)
        
        return ready_tasks
    
    def is_completed(self) -> bool:
        """检查工作流是否完成"""
        for task in self.tasks:
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                return False
        return True
    
    def has_failed_tasks(self) -> bool:
        """检查是否有失败的任务"""
        for task in self.tasks:
            if task.status == TaskStatus.FAILED:
                return True
        return False


class TaskExecutor(ABC):
    """任务执行器抽象基类"""
    
    @abstractmethod
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务"""
        pass


class HttpRequestExecutor(TaskExecutor):
    """HTTP请求任务执行器"""
    
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行HTTP请求任务"""
        import aiohttp
        
        config = task.config
        url = config.get("url")
        method = config.get("method", "GET").upper()
        headers = config.get("headers", {})
        data = config.get("data")
        timeout = task.timeout_seconds or 30
        
        # 变量替换
        if "variables" in context:
            url = self._replace_variables(url, context["variables"])
            if isinstance(data, str):
                data = self._replace_variables(data, context["variables"])
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.request(method, url, headers=headers, json=data) as response:
                result = {
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content": await response.text()
                }
                
                if response.status >= 400:
                    raise Exception(f"HTTP请求失败: {response.status}")
                
                return result
    
    def _replace_variables(self, text: str, variables: Dict[str, Any]) -> str:
        """替换文本中的变量"""
        if not isinstance(text, str):
            return text
        
        for key, value in variables.items():
            text = text.replace(f"${{{key}}}", str(value))
        
        return text


class ScriptExecutor(TaskExecutor):
    """脚本执行任务执行器"""
    
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行脚本任务"""
        config = task.config
        script = config.get("script")
        script_type = config.get("type", "shell")
        timeout = task.timeout_seconds or 60
        
        if not script:
            raise ValueError("脚本任务缺少script配置")
        
        # 变量替换
        if "variables" in context:
            for key, value in context["variables"].items():
                script = script.replace(f"${{{key}}}", str(value))
        
        if script_type == "shell":
            process = await asyncio.create_subprocess_shell(
                script,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        elif script_type == "python":
            process = await asyncio.create_subprocess_exec(
                "python3", "-c", script,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        else:
            raise ValueError(f"不支持的脚本类型: {script_type}")
        
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
            
            result = {
                "return_code": process.returncode,
                "stdout": stdout.decode(),
                "stderr": stderr.decode()
            }
            
            if process.returncode != 0:
                raise Exception(f"脚本执行失败: {stderr.decode()}")
            
            return result
        
        except asyncio.TimeoutError:
            process.kill()
            raise Exception("脚本执行超时")


class FunctionExecutor(TaskExecutor):
    """函数调用任务执行器"""
    
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行函数调用任务"""
        config = task.config
        function_name = config.get("function")
        module_name = config.get("module")
        args = config.get("args", [])
        kwargs = config.get("kwargs", {})
        
        if not function_name:
            raise ValueError("函数任务缺少function配置")
        
        # 变量替换
        if "variables" in context:
            args = self._replace_variables_in_data(args, context["variables"])
            kwargs = self._replace_variables_in_data(kwargs, context["variables"])
        
        # 动态导入模块和函数
        if module_name:
            import importlib
            module = importlib.import_module(module_name)
            func = getattr(module, function_name)
        else:
            # 从全局命名空间获取函数
            func = globals().get(function_name)
            if not func:
                raise ValueError(f"找不到函数: {function_name}")
        
        # 执行函数
        if asyncio.iscoroutinefunction(func):
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
        
        return {"result": result}
    
    def _replace_variables_in_data(self, data: Any, variables: Dict[str, Any]) -> Any:
        """在数据结构中替换变量"""
        if isinstance(data, str):
            for key, value in variables.items():
                data = data.replace(f"${{{key}}}", str(value))
            return data
        elif isinstance(data, list):
            return [self._replace_variables_in_data(item, variables) for item in data]
        elif isinstance(data, dict):
            return {k: self._replace_variables_in_data(v, variables) for k, v in data.items()}
        else:
            return data


class ConditionExecutor(TaskExecutor):
    """条件判断任务执行器"""
    
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行条件判断任务"""
        config = task.config
        condition = config.get("condition")
        
        if not condition:
            raise ValueError("条件任务缺少condition配置")
        
        # 简单的条件评估
        variables = context.get("variables", {})
        
        # 替换变量
        for key, value in variables.items():
            condition = condition.replace(f"${{{key}}}", str(value))
        
        try:
            # 安全的条件评估（仅支持基本比较）
            result = self._evaluate_condition(condition, variables)
            return {"condition_result": result}
        except Exception as e:
            raise Exception(f"条件评估失败: {e}")
    
    def _evaluate_condition(self, condition: str, variables: Dict[str, Any]) -> bool:
        """评估条件表达式"""
        # 简单的条件评估实现
        # 支持基本的比较操作
        
        # 移除空格
        condition = condition.strip()
        
        # 支持的操作符
        operators = ["==", "!=", ">=", "<=", ">", "<"]
        
        for op in operators:
            if op in condition:
                left, right = condition.split(op, 1)
                left = left.strip()
                right = right.strip()
                
                # 尝试转换为数字
                try:
                    left_val = float(left)
                    right_val = float(right)
                except ValueError:
                    left_val = left.strip('"\'')
                    right_val = right.strip('"\'')
                
                # 执行比较
                if op == "==":
                    return left_val == right_val
                elif op == "!=":
                    return left_val != right_val
                elif op == ">=":
                    return left_val >= right_val
                elif op == "<=":
                    return left_val <= right_val
                elif op == ">":
                    return left_val > right_val
                elif op == "<":
                    return left_val < right_val
        
        # 如果没有操作符，检查布尔值
        if condition.lower() in ["true", "1", "yes"]:
            return True
        elif condition.lower() in ["false", "0", "no"]:
            return False
        
        raise ValueError(f"无法评估条件: {condition}")


class DelayExecutor(TaskExecutor):
    """延迟任务执行器"""
    
    async def execute(self, task: WorkflowTask, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行延迟任务"""
        config = task.config
        delay_seconds = config.get("seconds", 1)
        
        await asyncio.sleep(delay_seconds)
        
        return {"delayed_seconds": delay_seconds}


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self):
        """初始化工作流引擎"""
        self._workflows: Dict[str, Workflow] = {}
        self._running_workflows: Dict[str, asyncio.Task] = {}
        self._executors: Dict[TaskType, TaskExecutor] = {
            TaskType.HTTP_REQUEST: HttpRequestExecutor(),
            TaskType.SCRIPT: ScriptExecutor(),
            TaskType.FUNCTION: FunctionExecutor(),
            TaskType.CONDITION: ConditionExecutor(),
            TaskType.DELAY: DelayExecutor()
        }
        self._logger = logging_system.get_logger("workflow_engine")
    
    def register_executor(self, task_type: TaskType, executor: TaskExecutor) -> None:
        """注册任务执行器"""
        self._executors[task_type] = executor
        self._logger.info(f"注册任务执行器: {task_type.value}")
    
    def create_workflow(self, workflow: Workflow) -> str:
        """创建工作流"""
        self._workflows[workflow.id] = workflow
        self._logger.info(f"创建工作流: {workflow.name} ({workflow.id})")
        return workflow.id
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self._workflows.get(workflow_id)
    
    def list_workflows(self, status: Optional[WorkflowStatus] = None) -> List[Workflow]:
        """列出工作流"""
        if status:
            return [wf for wf in self._workflows.values() if wf.status == status]
        return list(self._workflows.values())
    
    async def start_workflow(self, workflow_id: str, variables: Optional[Dict[str, Any]] = None) -> bool:
        """启动工作流"""
        workflow = self._workflows.get(workflow_id)
        if not workflow:
            return False
        
        if workflow.status == WorkflowStatus.RUNNING:
            return False
        
        # 设置变量
        if variables:
            workflow.variables.update(variables)
        
        # 启动工作流
        workflow.status = WorkflowStatus.RUNNING
        workflow.started_at = datetime.now()
        
        # 重置所有任务状态
        for task in workflow.tasks:
            task.status = TaskStatus.PENDING
            task.result = None
            task.error = None
            task.started_at = None
            task.completed_at = None
            task.retry_count = 0
        
        # 创建执行任务
        execution_task = asyncio.create_task(self._execute_workflow(workflow))
        self._running_workflows[workflow_id] = execution_task
        
        self._logger.info(f"启动工作流: {workflow.name} ({workflow_id})")
        return True
    
    async def stop_workflow(self, workflow_id: str) -> bool:
        """停止工作流"""
        if workflow_id in self._running_workflows:
            execution_task = self._running_workflows[workflow_id]
            execution_task.cancel()
            
            try:
                await execution_task
            except asyncio.CancelledError:
                pass
            
            del self._running_workflows[workflow_id]
            
            workflow = self._workflows.get(workflow_id)
            if workflow:
                workflow.status = WorkflowStatus.CANCELLED
                workflow.completed_at = datetime.now()
            
            self._logger.info(f"停止工作流: {workflow_id}")
            return True
        
        return False
    
    async def _execute_workflow(self, workflow: Workflow) -> None:
        """执行工作流"""
        try:
            context = {
                "workflow_id": workflow.id,
                "variables": workflow.variables.copy()
            }
            
            while not workflow.is_completed():
                ready_tasks = workflow.get_ready_tasks()
                
                if not ready_tasks:
                    # 检查是否有失败的任务
                    if workflow.has_failed_tasks():
                        workflow.status = WorkflowStatus.FAILED
                        break
                    
                    # 等待一段时间再检查
                    await asyncio.sleep(1)
                    continue
                
                # 执行准备好的任务
                tasks_to_execute = []
                for task in ready_tasks:
                    task_coroutine = self._execute_task(task, context)
                    tasks_to_execute.append(task_coroutine)
                
                # 并行执行任务
                if tasks_to_execute:
                    await asyncio.gather(*tasks_to_execute, return_exceptions=True)
            
            # 完成工作流
            if workflow.has_failed_tasks():
                workflow.status = WorkflowStatus.FAILED
            else:
                workflow.status = WorkflowStatus.COMPLETED
            
            workflow.completed_at = datetime.now()
            
            self._logger.info(f"工作流执行完成: {workflow.name} - 状态: {workflow.status.value}")
        
        except asyncio.CancelledError:
            workflow.status = WorkflowStatus.CANCELLED
            workflow.completed_at = datetime.now()
            self._logger.info(f"工作流被取消: {workflow.name}")
        
        except Exception as e:
            workflow.status = WorkflowStatus.FAILED
            workflow.completed_at = datetime.now()
            self._logger.error(f"工作流执行异常: {workflow.name} - {e}")
        
        finally:
            # 清理运行中的工作流记录
            self._running_workflows.pop(workflow.id, None)
    
    async def _execute_task(self, task: WorkflowTask, context: Dict[str, Any]) -> None:
        """执行单个任务"""
        try:
            task.start()
            self._logger.info(f"开始执行任务: {task.name} ({task.id})")
            
            # 检查执行条件
            if task.conditions and not self._check_conditions(task.conditions, context):
                task.skip()
                self._logger.info(f"任务条件不满足，跳过: {task.name}")
                return
            
            # 获取任务执行器
            executor = self._executors.get(task.task_type)
            if not executor:
                raise Exception(f"不支持的任务类型: {task.task_type.value}")
            
            # 执行任务
            result = await executor.execute(task, context)
            task.complete(result)
            
            # 更新上下文变量
            if result and "variables" in result:
                context["variables"].update(result["variables"])
            
            self._logger.info(f"任务执行完成: {task.name}")
        
        except Exception as e:
            error_msg = str(e)
            task.fail(error_msg)
            
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                self._logger.warning(f"任务执行失败，准备重试: {task.name} - {error_msg}")
            else:
                self._logger.error(f"任务执行失败，已达到最大重试次数: {task.name} - {error_msg}")
    
    def _check_conditions(self, conditions: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查执行条件"""
        # 简单的条件检查实现
        variables = context.get("variables", {})
        
        for key, expected_value in conditions.items():
            if key not in variables:
                return False
            
            actual_value = variables[key]
            if actual_value != expected_value:
                return False
        
        return True


class WorkflowEngineConfig(BaseModel):
    """工作流引擎配置"""

    max_concurrent_workflows: int = 10
    default_task_timeout: int = 300
    enable_workflow_persistence: bool = True
    workflow_history_limit: int = 1000


class WorkflowEngineTool(ToolInterface):
    """工作流引擎工具"""

    def __init__(self, config: Optional[WorkflowEngineConfig] = None):
        """
        初始化工作流引擎工具

        Args:
            config: 工作流引擎配置
        """
        self.config = config or WorkflowEngineConfig()
        self._engine = WorkflowEngine()
        self._logger = logging_system.get_logger("workflow_engine_tool")

    @property
    def name(self) -> str:
        """工具名称"""
        return "workflow_engine_tool"

    @property
    def description(self) -> str:
        """工具描述"""
        return "工作流编排和执行引擎，支持复杂业务流程自动化"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数模式"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": [
                        "create_workflow", "get_workflow", "list_workflows",
                        "start_workflow", "stop_workflow", "get_workflow_status",
                        "add_task", "get_task", "list_tasks"
                    ]
                },
                "workflow_config": {
                    "type": "object",
                    "description": "工作流配置",
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "version": {"type": "string"},
                        "tasks": {"type": "array"},
                        "variables": {"type": "object"}
                    }
                },
                "task_config": {
                    "type": "object",
                    "description": "任务配置",
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "task_type": {"type": "string"},
                        "config": {"type": "object"},
                        "dependencies": {"type": "array"},
                        "conditions": {"type": "object"}
                    }
                },
                "workflow_id": {"type": "string"},
                "task_id": {"type": "string"},
                "variables": {"type": "object"},
                "status_filter": {"type": "string"}
            },
            "required": ["action"]
        }

    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行工作流引擎操作"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少action参数"
                )

            # 工作流管理操作
            if action == "create_workflow":
                return await self._create_workflow(arguments)
            elif action == "get_workflow":
                return await self._get_workflow(arguments)
            elif action == "list_workflows":
                return await self._list_workflows(arguments)
            elif action == "start_workflow":
                return await self._start_workflow(arguments)
            elif action == "stop_workflow":
                return await self._stop_workflow(arguments)
            elif action == "get_workflow_status":
                return await self._get_workflow_status(arguments)

            # 任务管理操作
            elif action == "add_task":
                return await self._add_task(arguments)
            elif action == "get_task":
                return await self._get_task(arguments)
            elif action == "list_tasks":
                return await self._list_tasks(arguments)

            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

        except Exception as e:
            self._logger.error(f"执行工作流引擎操作失败: {e}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _create_workflow(self, arguments: Dict[str, Any]) -> ToolResult:
        """创建工作流"""
        try:
            workflow_config = arguments.get("workflow_config", {})

            # 验证必需参数
            if "name" not in workflow_config:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: name"
                )

            # 创建工作流对象
            workflow = Workflow(
                name=workflow_config["name"],
                description=workflow_config.get("description", ""),
                version=workflow_config.get("version", "1.0"),
                variables=workflow_config.get("variables", {}),
                created_by=arguments.get("user_id")
            )

            # 添加任务
            tasks_config = workflow_config.get("tasks", [])
            for task_config in tasks_config:
                task = WorkflowTask(
                    name=task_config["name"],
                    description=task_config.get("description", ""),
                    task_type=TaskType(task_config["task_type"]),
                    config=task_config.get("config", {}),
                    dependencies=task_config.get("dependencies", []),
                    conditions=task_config.get("conditions", {}),
                    max_retries=task_config.get("max_retries", 3),
                    timeout_seconds=task_config.get("timeout_seconds")
                )
                workflow.add_task(task)

            # 创建工作流
            workflow_id = self._engine.create_workflow(workflow)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "workflow_id": workflow_id,
                    "message": f"工作流创建成功: {workflow.name}",
                    "task_count": len(workflow.tasks)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"创建工作流失败: {str(e)}"
            )

    async def _get_workflow(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取工作流"""
        try:
            workflow_id = arguments.get("workflow_id")
            if not workflow_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少workflow_id参数"
                )

            workflow = self._engine.get_workflow(workflow_id)

            if workflow:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "workflow": {
                            "id": workflow.id,
                            "name": workflow.name,
                            "description": workflow.description,
                            "version": workflow.version,
                            "status": workflow.status.value,
                            "created_at": workflow.created_at.isoformat(),
                            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
                            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
                            "task_count": len(workflow.tasks),
                            "variables": workflow.variables,
                            "metadata": workflow.metadata
                        }
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"工作流不存在: {workflow_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取工作流失败: {str(e)}"
            )

    async def _list_workflows(self, arguments: Dict[str, Any]) -> ToolResult:
        """列出工作流"""
        try:
            status_filter = arguments.get("status_filter")
            status = WorkflowStatus(status_filter) if status_filter else None

            workflows = self._engine.list_workflows(status)

            workflows_data = []
            for workflow in workflows:
                workflows_data.append({
                    "id": workflow.id,
                    "name": workflow.name,
                    "description": workflow.description,
                    "version": workflow.version,
                    "status": workflow.status.value,
                    "created_at": workflow.created_at.isoformat(),
                    "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
                    "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
                    "task_count": len(workflow.tasks),
                    "created_by": workflow.created_by
                })

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "workflows": workflows_data,
                    "count": len(workflows_data)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出工作流失败: {str(e)}"
            )

    async def _start_workflow(self, arguments: Dict[str, Any]) -> ToolResult:
        """启动工作流"""
        try:
            workflow_id = arguments.get("workflow_id")
            if not workflow_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少workflow_id参数"
                )

            variables = arguments.get("variables", {})

            success = await self._engine.start_workflow(workflow_id, variables)

            if success:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "message": f"工作流启动成功: {workflow_id}",
                        "workflow_id": workflow_id
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"工作流启动失败: {workflow_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"启动工作流失败: {str(e)}"
            )

    async def _stop_workflow(self, arguments: Dict[str, Any]) -> ToolResult:
        """停止工作流"""
        try:
            workflow_id = arguments.get("workflow_id")
            if not workflow_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少workflow_id参数"
                )

            success = await self._engine.stop_workflow(workflow_id)

            if success:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "message": f"工作流停止成功: {workflow_id}",
                        "workflow_id": workflow_id
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"工作流停止失败: {workflow_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"停止工作流失败: {str(e)}"
            )

    async def _get_workflow_status(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取工作流状态"""
        try:
            workflow_id = arguments.get("workflow_id")
            if not workflow_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少workflow_id参数"
                )

            workflow = self._engine.get_workflow(workflow_id)

            if workflow:
                # 统计任务状态
                task_status_counts = {}
                for status in TaskStatus:
                    task_status_counts[status.value] = 0

                for task in workflow.tasks:
                    task_status_counts[task.status.value] += 1

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "workflow_id": workflow.id,
                        "workflow_status": workflow.status.value,
                        "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
                        "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None,
                        "task_status_counts": task_status_counts,
                        "total_tasks": len(workflow.tasks),
                        "is_running": workflow_id in self._engine._running_workflows
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"工作流不存在: {workflow_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取工作流状态失败: {str(e)}"
            )

    async def _list_tasks(self, arguments: Dict[str, Any]) -> ToolResult:
        """列出任务"""
        try:
            workflow_id = arguments.get("workflow_id")
            if not workflow_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少workflow_id参数"
                )

            workflow = self._engine.get_workflow(workflow_id)

            if workflow:
                tasks_data = []
                for task in workflow.tasks:
                    tasks_data.append({
                        "id": task.id,
                        "name": task.name,
                        "description": task.description,
                        "task_type": task.task_type.value,
                        "status": task.status.value,
                        "dependencies": task.dependencies,
                        "retry_count": task.retry_count,
                        "max_retries": task.max_retries,
                        "started_at": task.started_at.isoformat() if task.started_at else None,
                        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                        "error": task.error,
                        "result": task.result
                    })

                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "tasks": tasks_data,
                        "count": len(tasks_data)
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"工作流不存在: {workflow_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出任务失败: {str(e)}"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            if action == "create_workflow":
                workflow_config = arguments.get("workflow_config", {})
                if "name" not in workflow_config:
                    return False

            elif action in ["get_workflow", "start_workflow", "stop_workflow", "get_workflow_status", "list_tasks"]:
                if "workflow_id" not in arguments:
                    return False

            return True

        except Exception:
            return False
