"""
消息队列服务工具

提供统一的消息队列操作接口，支持Redis、RabbitMQ、Kafka等多种消息队列系统。
适用于AI Agent的异步任务处理、事件驱动工作流等场景。
"""

import asyncio
import json
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class MessageQueueType(Enum):
    """消息队列类型枚举"""
    REDIS = "redis"
    RABBITMQ = "rabbitmq"
    KAFKA = "kafka"
    MEMORY = "memory"  # 内存队列，用于测试


class MessagePriority(Enum):
    """消息优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class QueueMessage:
    """队列消息数据结构"""
    
    def __init__(
        self,
        content: Union[str, Dict[str, Any]],
        message_id: Optional[str] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
        delay_seconds: int = 0,
        ttl_seconds: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化队列消息
        
        Args:
            content: 消息内容
            message_id: 消息ID，为空时自动生成
            priority: 消息优先级
            delay_seconds: 延迟发送秒数
            ttl_seconds: 消息生存时间（秒）
            metadata: 消息元数据
        """
        self.content = content
        self.message_id = message_id or self._generate_id()
        self.priority = priority
        self.delay_seconds = delay_seconds
        self.ttl_seconds = ttl_seconds
        self.metadata = metadata or {}
        self.created_at = datetime.now()
        self.attempts = 0
    
    def _generate_id(self) -> str:
        """生成消息ID"""
        import uuid
        return str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message_id": self.message_id,
            "content": self.content,
            "priority": self.priority.value,
            "delay_seconds": self.delay_seconds,
            "ttl_seconds": self.ttl_seconds,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat(),
            "attempts": self.attempts,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "QueueMessage":
        """从字典创建消息对象"""
        msg = cls(
            content=data["content"],
            message_id=data["message_id"],
            priority=MessagePriority(data["priority"]),
            delay_seconds=data.get("delay_seconds", 0),
            ttl_seconds=data.get("ttl_seconds"),
            metadata=data.get("metadata", {}),
        )
        msg.created_at = datetime.fromisoformat(data["created_at"])
        msg.attempts = data.get("attempts", 0)
        return msg


class MessageQueueAdapter(ABC):
    """消息队列适配器抽象基类"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接到消息队列"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """断开连接"""
        pass
    
    @abstractmethod
    async def send_message(
        self,
        queue_name: str,
        message: QueueMessage,
    ) -> bool:
        """发送消息到队列"""
        pass
    
    @abstractmethod
    async def receive_message(
        self,
        queue_name: str,
        timeout_seconds: Optional[int] = None,
    ) -> Optional[QueueMessage]:
        """从队列接收消息"""
        pass
    
    @abstractmethod
    async def create_queue(
        self,
        queue_name: str,
        durable: bool = True,
        max_size: Optional[int] = None,
    ) -> bool:
        """创建队列"""
        pass
    
    @abstractmethod
    async def delete_queue(self, queue_name: str) -> bool:
        """删除队列"""
        pass
    
    @abstractmethod
    async def get_queue_info(self, queue_name: str) -> Dict[str, Any]:
        """获取队列信息"""
        pass
    
    @abstractmethod
    async def purge_queue(self, queue_name: str) -> int:
        """清空队列，返回清除的消息数量"""
        pass


class MemoryQueueAdapter(MessageQueueAdapter):
    """内存队列适配器（用于测试和开发）"""
    
    def __init__(self):
        self._queues: Dict[str, List[QueueMessage]] = {}
        self._connected = False
        self._logger = logging_system.get_logger("memory_queue")
    
    async def connect(self) -> bool:
        """连接到内存队列"""
        self._connected = True
        self._logger.info("已连接到内存队列")
        return True
    
    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._logger.info("已断开内存队列连接")
        return True
    
    async def send_message(self, queue_name: str, message: QueueMessage) -> bool:
        """发送消息到队列"""
        if not self._connected:
            return False
        
        if queue_name not in self._queues:
            await self.create_queue(queue_name)
        
        # 处理延迟发送
        if message.delay_seconds > 0:
            await asyncio.sleep(message.delay_seconds)
        
        # 按优先级插入消息
        queue = self._queues[queue_name]
        inserted = False
        for i, existing_msg in enumerate(queue):
            if message.priority.value > existing_msg.priority.value:
                queue.insert(i, message)
                inserted = True
                break
        
        if not inserted:
            queue.append(message)
        
        self._logger.info(f"消息已发送到队列 {queue_name}: {message.message_id}")
        return True
    
    async def receive_message(
        self,
        queue_name: str,
        timeout_seconds: Optional[int] = None,
    ) -> Optional[QueueMessage]:
        """从队列接收消息"""
        if not self._connected or queue_name not in self._queues:
            return None
        
        queue = self._queues[queue_name]
        if not queue:
            return None
        
        message = queue.pop(0)
        message.attempts += 1
        
        self._logger.info(f"从队列 {queue_name} 接收消息: {message.message_id}")
        return message
    
    async def create_queue(
        self,
        queue_name: str,
        durable: bool = True,
        max_size: Optional[int] = None,
    ) -> bool:
        """创建队列"""
        if queue_name not in self._queues:
            self._queues[queue_name] = []
            self._logger.info(f"已创建队列: {queue_name}")
        return True
    
    async def delete_queue(self, queue_name: str) -> bool:
        """删除队列"""
        if queue_name in self._queues:
            del self._queues[queue_name]
            self._logger.info(f"已删除队列: {queue_name}")
            return True
        return False
    
    async def get_queue_info(self, queue_name: str) -> Dict[str, Any]:
        """获取队列信息"""
        if queue_name not in self._queues:
            return {}
        
        queue = self._queues[queue_name]
        return {
            "name": queue_name,
            "size": len(queue),
            "type": "memory",
            "durable": False,
        }
    
    async def purge_queue(self, queue_name: str) -> int:
        """清空队列"""
        if queue_name not in self._queues:
            return 0
        
        count = len(self._queues[queue_name])
        self._queues[queue_name].clear()
        self._logger.info(f"已清空队列 {queue_name}，清除 {count} 条消息")
        return count


class MessageQueueTool(ToolInterface):
    """
    消息队列服务工具
    
    提供统一的消息队列操作接口，支持多种消息队列系统。
    适用于AI Agent的异步任务处理、事件驱动工作流等场景。
    """
    
    def __init__(
        self,
        queue_type: MessageQueueType = MessageQueueType.MEMORY,
        connection_config: Optional[Dict[str, Any]] = None,
        default_timeout: int = 30,
    ):
        """
        初始化消息队列工具
        
        Args:
            queue_type: 消息队列类型
            connection_config: 连接配置
            default_timeout: 默认超时时间（秒）
        """
        self.queue_type = queue_type
        self.connection_config = connection_config or {}
        self.default_timeout = default_timeout
        
        self._logger = logging_system.get_logger("message_queue_tool")
        self._adapter: Optional[MessageQueueAdapter] = None
        self._initialize_adapter()
    
    def _initialize_adapter(self) -> None:
        """初始化队列适配器"""
        if self.queue_type == MessageQueueType.MEMORY:
            self._adapter = MemoryQueueAdapter()
        elif self.queue_type == MessageQueueType.REDIS:
            try:
                from ai_agent_framework.tools.adapters import RedisQueueAdapter
                self._adapter = RedisQueueAdapter(**self.connection_config)
            except ImportError:
                raise ImportError("Redis适配器需要redis库，请运行: pip install redis")
        elif self.queue_type == MessageQueueType.RABBITMQ:
            try:
                from ai_agent_framework.tools.adapters.enterprise_queue_adapters import RabbitMQAdapter
                self._adapter = RabbitMQAdapter(**self.connection_config)
            except ImportError as e:
                raise ImportError(f"RabbitMQ适配器依赖缺失: {e}")
        elif self.queue_type == MessageQueueType.KAFKA:
            try:
                from ai_agent_framework.tools.adapters.enterprise_queue_adapters import KafkaAdapter
                self._adapter = KafkaAdapter(**self.connection_config)
            except ImportError as e:
                raise ImportError(f"Kafka适配器依赖缺失: {e}")
        else:
            raise ValueError(f"不支持的队列类型: {self.queue_type}")
    
    @property
    def name(self) -> str:
        return "message_queue"
    
    @property
    def description(self) -> str:
        return "消息队列服务工具，支持消息发送、接收、队列管理等操作"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": [
                        "connect", "disconnect", "send", "receive",
                        "create_queue", "delete_queue", "queue_info",
                        "purge_queue", "list_queues"
                    ],
                    "description": "要执行的操作"
                },
                "queue_name": {
                    "type": "string",
                    "description": "队列名称"
                },
                "message": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": ["string", "object"],
                            "description": "消息内容"
                        },
                        "priority": {
                            "type": "string",
                            "enum": ["low", "normal", "high", "urgent"],
                            "default": "normal",
                            "description": "消息优先级"
                        },
                        "delay_seconds": {
                            "type": "integer",
                            "minimum": 0,
                            "default": 0,
                            "description": "延迟发送秒数"
                        },
                        "ttl_seconds": {
                            "type": "integer",
                            "minimum": 1,
                            "description": "消息生存时间（秒）"
                        },
                        "metadata": {
                            "type": "object",
                            "description": "消息元数据"
                        }
                    },
                    "required": ["content"],
                    "description": "要发送的消息"
                },
                "timeout_seconds": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "操作超时时间（秒）"
                },
                "durable": {
                    "type": "boolean",
                    "default": True,
                    "description": "队列是否持久化"
                },
                "max_size": {
                    "type": "integer",
                    "minimum": 1,
                    "description": "队列最大大小"
                }
            },
            "required": ["action"],
            "additionalProperties": False
        }

    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行消息队列操作

        Args:
            arguments: 工具参数
            context: 执行上下文

        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )

            # 确保适配器已连接
            if not self._adapter:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="消息队列适配器未初始化"
                )

            # 执行对应的操作
            if action == "connect":
                result = await self._handle_connect()
            elif action == "disconnect":
                result = await self._handle_disconnect()
            elif action == "send":
                result = await self._handle_send_message(arguments)
            elif action == "receive":
                result = await self._handle_receive_message(arguments)
            elif action == "create_queue":
                result = await self._handle_create_queue(arguments)
            elif action == "delete_queue":
                result = await self._handle_delete_queue(arguments)
            elif action == "queue_info":
                result = await self._handle_queue_info(arguments)
            elif action == "purge_queue":
                result = await self._handle_purge_queue(arguments)
            elif action == "list_queues":
                result = await self._handle_list_queues()
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

            return result

        except Exception as e:
            self._logger.error(f"消息队列操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _handle_connect(self) -> ToolResult:
        """处理连接操作"""
        success = await self._adapter.connect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"connected": success, "queue_type": self.queue_type.value}
        )

    async def _handle_disconnect(self) -> ToolResult:
        """处理断开连接操作"""
        success = await self._adapter.disconnect()
        return ToolResult(
            tool_call_id="",
            success=success,
            result={"disconnected": success}
        )

    async def _handle_send_message(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理发送消息操作"""
        queue_name = arguments.get("queue_name")
        message_data = arguments.get("message", {})

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        if not message_data.get("content"):
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: message.content"
            )

        # 创建消息对象
        priority_map = {
            "low": MessagePriority.LOW,
            "normal": MessagePriority.NORMAL,
            "high": MessagePriority.HIGH,
            "urgent": MessagePriority.URGENT,
        }

        message = QueueMessage(
            content=message_data["content"],
            priority=priority_map.get(message_data.get("priority", "normal"), MessagePriority.NORMAL),
            delay_seconds=message_data.get("delay_seconds", 0),
            ttl_seconds=message_data.get("ttl_seconds"),
            metadata=message_data.get("metadata", {}),
        )

        success = await self._adapter.send_message(queue_name, message)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "sent": success,
                "message_id": message.message_id,
                "queue_name": queue_name,
            }
        )

    async def _handle_receive_message(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理接收消息操作"""
        queue_name = arguments.get("queue_name")
        timeout_seconds = arguments.get("timeout_seconds", self.default_timeout)

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        message = await self._adapter.receive_message(queue_name, timeout_seconds)

        if message:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "received": True,
                    "message": message.to_dict(),
                    "queue_name": queue_name,
                }
            )
        else:
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "received": False,
                    "message": None,
                    "queue_name": queue_name,
                }
            )

    async def _handle_create_queue(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理创建队列操作"""
        queue_name = arguments.get("queue_name")
        durable = arguments.get("durable", True)
        max_size = arguments.get("max_size")

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        success = await self._adapter.create_queue(queue_name, durable, max_size)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "created": success,
                "queue_name": queue_name,
                "durable": durable,
                "max_size": max_size,
            }
        )

    async def _handle_delete_queue(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理删除队列操作"""
        queue_name = arguments.get("queue_name")

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        success = await self._adapter.delete_queue(queue_name)

        return ToolResult(
            tool_call_id="",
            success=success,
            result={
                "deleted": success,
                "queue_name": queue_name,
            }
        )

    async def _handle_queue_info(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理获取队列信息操作"""
        queue_name = arguments.get("queue_name")

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        info = await self._adapter.get_queue_info(queue_name)

        return ToolResult(
            tool_call_id="",
            success=bool(info),
            result=info
        )

    async def _handle_purge_queue(self, arguments: Dict[str, Any]) -> ToolResult:
        """处理清空队列操作"""
        queue_name = arguments.get("queue_name")

        if not queue_name:
            return ToolResult(
                tool_call_id="",
                success=False,
                error="缺少必需参数: queue_name"
            )

        count = await self._adapter.purge_queue(queue_name)

        return ToolResult(
            tool_call_id="",
            success=True,
            result={
                "purged": True,
                "queue_name": queue_name,
                "messages_removed": count,
            }
        )

    async def _handle_list_queues(self) -> ToolResult:
        """处理列出队列操作"""
        # 对于内存队列，直接返回队列列表
        if isinstance(self._adapter, MemoryQueueAdapter):
            queues = list(self._adapter._queues.keys())
            return ToolResult(
                tool_call_id="",
                success=True,
                result={"queues": queues, "count": len(queues)}
            )

        # 其他适配器需要实现相应的列表方法
        return ToolResult(
            tool_call_id="",
            success=False,
            error="当前适配器不支持列出队列操作"
        )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """
        验证工具参数

        Args:
            arguments: 工具参数

        Returns:
            bool: 参数是否有效
        """
        action = arguments.get("action")
        if not action:
            return False

        valid_actions = [
            "connect", "disconnect", "send", "receive",
            "create_queue", "delete_queue", "queue_info",
            "purge_queue", "list_queues"
        ]

        if action not in valid_actions:
            return False

        # 验证需要队列名称的操作
        queue_required_actions = [
            "send", "receive", "create_queue", "delete_queue",
            "queue_info", "purge_queue"
        ]

        if action in queue_required_actions and not arguments.get("queue_name"):
            return False

        # 验证发送消息的参数
        if action == "send":
            message = arguments.get("message", {})
            if not message.get("content"):
                return False

        return True

    @property
    def timeout_seconds(self) -> Optional[float]:
        """执行超时时间"""
        return float(self.default_timeout)
