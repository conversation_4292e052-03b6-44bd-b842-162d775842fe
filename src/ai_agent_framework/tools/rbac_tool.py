"""
RBAC权限控制工具

提供基于角色的访问控制功能，包括用户管理、角色管理、权限检查等。
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.security.rbac import (
    RBACManager, MemoryRBACStore, AccessRequest, AccessResult,
    RBACUser, Role, Permission, ResourceType, PermissionType
)
from ai_agent_framework.utils.logging_system import logging_system


class RBACToolConfig(BaseModel):
    """RBAC工具配置"""
    
    store_type: str = "memory"  # 存储类型：memory, database, file
    store_config: Dict[str, Any] = {}  # 存储配置
    cache_ttl: int = 300  # 缓存TTL（秒）
    enable_audit_log: bool = True  # 是否启用审计日志


class RBACTool(ToolInterface):
    """RBAC权限控制工具"""
    
    def __init__(self, config: Optional[RBACToolConfig] = None):
        """
        初始化RBAC工具
        
        Args:
            config: RBAC工具配置
        """

        self.config = config or RBACToolConfig()
        self._rbac_manager = None
        self._logger = logging_system.get_logger("rbac_tool")
        
        # 初始化RBAC管理器
        self._initialize_rbac_manager()
    
    def _initialize_rbac_manager(self) -> None:
        """初始化RBAC管理器"""
        try:
            # 目前只支持内存存储，后续可以扩展数据库存储
            if self.config.store_type == "memory":
                store = MemoryRBACStore()
            else:
                raise ValueError(f"不支持的存储类型: {self.config.store_type}")
            
            self._rbac_manager = RBACManager(store)
            self._rbac_manager._cache_ttl = self.config.cache_ttl
            
            self._logger.info("RBAC管理器初始化成功")
            
        except Exception as e:
            self._logger.error(f"初始化RBAC管理器失败: {e}")
            raise
    
    async def execute(self, params: Dict[str, Any]) -> ToolResult:
        """
        执行RBAC操作
        
        Args:
            params: 操作参数
            
        Returns:
            ToolResult: 执行结果
        """
        try:
            action = params.get("action")
            if not action:
                return ToolResult(
                    success=False,
                    error="缺少action参数"
                )
            
            # 权限检查操作
            if action == "check_access":
                return await self._check_access(params)
            
            # 用户管理操作
            elif action == "create_user":
                return await self._create_user(params)
            elif action == "assign_role":
                return await self._assign_role(params)
            elif action == "revoke_role":
                return await self._revoke_role(params)
            elif action == "assign_permission":
                return await self._assign_permission(params)
            elif action == "get_user_info":
                return await self._get_user_info(params)
            
            # 角色管理操作
            elif action == "create_role":
                return await self._create_role(params)
            elif action == "assign_role_permission":
                return await self._assign_role_permission(params)
            elif action == "get_role_info":
                return await self._get_role_info(params)
            elif action == "list_roles":
                return await self._list_roles(params)
            
            # 权限管理操作
            elif action == "create_permission":
                return await self._create_permission(params)
            elif action == "list_permissions":
                return await self._list_permissions(params)
            
            # 查询操作
            elif action == "list_users":
                return await self._list_users(params)
            elif action == "get_user_roles":
                return await self._get_user_roles(params)
            elif action == "get_user_permissions":
                return await self._get_user_permissions(params)
            
            # 缓存管理
            elif action == "clear_cache":
                return await self._clear_cache(params)
            
            else:
                return ToolResult(
                    success=False,
                    error=f"不支持的操作: {action}"
                )
        
        except Exception as e:
            self._logger.error(f"执行RBAC操作失败: {e}")
            return ToolResult(
                success=False,
                error=f"操作失败: {str(e)}"
            )
    
    async def _check_access(self, params: Dict[str, Any]) -> ToolResult:
        """检查访问权限"""
        try:
            user_id = params.get("user_id")
            resource_id = params.get("resource_id")
            resource_type = params.get("resource_type")
            permission_type = params.get("permission_type")
            context = params.get("context", {})
            
            if not all([user_id, resource_id, resource_type, permission_type]):
                return ToolResult(
                    success=False,
                    error="缺少必要参数: user_id, resource_id, resource_type, permission_type"
                )
            
            # 转换枚举类型
            try:
                resource_type_enum = ResourceType(resource_type)
                permission_type_enum = PermissionType(permission_type)
            except ValueError as e:
                return ToolResult(
                    success=False,
                    error=f"无效的类型参数: {e}"
                )
            
            # 创建访问请求
            request = AccessRequest(
                user_id=user_id,
                resource_id=resource_id,
                resource_type=resource_type_enum,
                permission_type=permission_type_enum,
                context=context
            )
            
            # 执行权限检查
            result = await self._rbac_manager.check_access(request)
            
            # 记录审计日志
            if self.config.enable_audit_log:
                self._logger.info(
                    f"权限检查: 用户={user_id}, 资源={resource_type}:{resource_id}, "
                    f"权限={permission_type}, 结果={'允许' if result.granted else '拒绝'}, "
                    f"原因={result.reason}"
                )
            
            return ToolResult(
                success=True,
                result={
                    "granted": result.granted,
                    "reason": result.reason,
                    "matched_permissions": result.matched_permissions,
                    "user_roles": result.user_roles,
                    "timestamp": result.timestamp.isoformat()
                }
            )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"权限检查失败: {str(e)}"
            )
    
    async def _create_user(self, params: Dict[str, Any]) -> ToolResult:
        """创建用户"""
        try:
            user_id = params.get("user_id")
            username = params.get("username")
            email = params.get("email")
            roles = params.get("roles", ["user"])
            is_superuser = params.get("is_superuser", False)
            
            if not all([user_id, username]):
                return ToolResult(
                    success=False,
                    error="缺少必要参数: user_id, username"
                )
            
            success = await self._rbac_manager.create_user(
                user_id=user_id,
                username=username,
                email=email,
                roles=roles,
                is_superuser=is_superuser
            )
            
            if success:
                return ToolResult(
                    success=True,
                    result={"message": f"用户创建成功: {user_id}"}
                )
            else:
                return ToolResult(
                    success=False,
                    error="用户创建失败"
                )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"创建用户失败: {str(e)}"
            )
    
    async def _assign_role(self, params: Dict[str, Any]) -> ToolResult:
        """分配角色"""
        try:
            user_id = params.get("user_id")
            role_id = params.get("role_id")
            
            if not all([user_id, role_id]):
                return ToolResult(
                    success=False,
                    error="缺少必要参数: user_id, role_id"
                )
            
            success = await self._rbac_manager.assign_role_to_user(user_id, role_id)
            
            if success:
                return ToolResult(
                    success=True,
                    result={"message": f"角色分配成功: 用户 {user_id} 获得角色 {role_id}"}
                )
            else:
                return ToolResult(
                    success=False,
                    error="角色分配失败"
                )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"分配角色失败: {str(e)}"
            )
    
    async def _create_role(self, params: Dict[str, Any]) -> ToolResult:
        """创建角色"""
        try:
            role_id = params.get("role_id")
            name = params.get("name")
            description = params.get("description")
            permissions = params.get("permissions", [])
            parent_roles = params.get("parent_roles", [])
            
            if not all([role_id, name, description]):
                return ToolResult(
                    success=False,
                    error="缺少必要参数: role_id, name, description"
                )
            
            success = await self._rbac_manager.create_role(
                role_id=role_id,
                name=name,
                description=description,
                permissions=permissions,
                parent_roles=parent_roles
            )
            
            if success:
                return ToolResult(
                    success=True,
                    result={"message": f"角色创建成功: {role_id}"}
                )
            else:
                return ToolResult(
                    success=False,
                    error="角色创建失败"
                )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"创建角色失败: {str(e)}"
            )
    
    async def _create_permission(self, params: Dict[str, Any]) -> ToolResult:
        """创建权限"""
        try:
            permission_id = params.get("permission_id")
            name = params.get("name")
            description = params.get("description")
            resource_type = params.get("resource_type")
            permission_type = params.get("permission_type")
            resource_pattern = params.get("resource_pattern", "*")
            conditions = params.get("conditions", {})
            
            if not all([permission_id, name, description, resource_type, permission_type]):
                return ToolResult(
                    success=False,
                    error="缺少必要参数: permission_id, name, description, resource_type, permission_type"
                )
            
            # 转换枚举类型
            try:
                resource_type_enum = ResourceType(resource_type)
                permission_type_enum = PermissionType(permission_type)
            except ValueError as e:
                return ToolResult(
                    success=False,
                    error=f"无效的类型参数: {e}"
                )
            
            success = await self._rbac_manager.create_permission(
                permission_id=permission_id,
                name=name,
                description=description,
                resource_type=resource_type_enum,
                permission_type=permission_type_enum,
                resource_pattern=resource_pattern,
                conditions=conditions
            )
            
            if success:
                return ToolResult(
                    success=True,
                    result={"message": f"权限创建成功: {permission_id}"}
                )
            else:
                return ToolResult(
                    success=False,
                    error="权限创建失败"
                )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"创建权限失败: {str(e)}"
            )
    
    async def _get_user_info(self, params: Dict[str, Any]) -> ToolResult:
        """获取用户信息"""
        try:
            user_id = params.get("user_id")
            if not user_id:
                return ToolResult(
                    success=False,
                    error="缺少参数: user_id"
                )
            
            user = await self._rbac_manager.store.get_user(user_id)
            if not user:
                return ToolResult(
                    success=False,
                    error=f"用户不存在: {user_id}"
                )
            
            # 获取用户角色和权限
            roles = await self._rbac_manager.get_user_roles(user_id)
            permissions = await self._rbac_manager.get_user_permissions(user_id)
            
            return ToolResult(
                success=True,
                result={
                    "user": user.model_dump(),
                    "roles": [role.model_dump() for role in roles],
                    "permissions": [perm.model_dump() for perm in permissions]
                }
            )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"获取用户信息失败: {str(e)}"
            )
    
    async def _list_users(self, params: Dict[str, Any]) -> ToolResult:
        """列出所有用户"""
        try:
            users = await self._rbac_manager.store.list_users()
            return ToolResult(
                success=True,
                result={
                    "users": [user.model_dump() for user in users],
                    "count": len(users)
                }
            )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"列出用户失败: {str(e)}"
            )
    
    async def _clear_cache(self, params: Dict[str, Any]) -> ToolResult:
        """清除缓存"""
        try:
            user_id = params.get("user_id")
            self._rbac_manager.clear_cache(user_id)
            
            return ToolResult(
                success=True,
                result={"message": f"缓存已清除: {user_id or '全部'}"}
            )
        
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"清除缓存失败: {str(e)}"
            )
    
    def get_schema(self) -> Dict[str, Any]:
        """获取工具模式"""
        return {
            "name": "rbac_tool",
            "description": "基于角色的访问控制工具，提供用户、角色、权限管理功能",
            "parameters": {
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "description": "操作类型",
                        "enum": [
                            "check_access", "create_user", "assign_role", "revoke_role",
                            "assign_permission", "get_user_info", "create_role",
                            "assign_role_permission", "get_role_info", "list_roles",
                            "create_permission", "list_permissions", "list_users",
                            "get_user_roles", "get_user_permissions", "clear_cache"
                        ]
                    }
                },
                "required": ["action"]
            }
        }

    @property
    def name(self) -> str:
        """工具名称"""
        return "rbac_tool"

    @property
    def description(self) -> str:
        """工具描述"""
        return "基于角色的访问控制工具，提供用户、角色、权限管理功能"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数模式"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": [
                        "check_access", "create_user", "assign_role", "revoke_role",
                        "assign_permission", "get_user_info", "create_role",
                        "assign_role_permission", "get_role_info", "list_roles",
                        "create_permission", "list_permissions", "list_users",
                        "get_user_roles", "get_user_permissions", "clear_cache"
                    ]
                }
            },
            "required": ["action"]
        }

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            if action == "check_access":
                # 验证访问控制检查的必需参数
                required_fields = ["user_id", "resource_id", "resource_type", "permission_type"]
                for field in required_fields:
                    if field not in arguments:
                        return False

            elif action == "create_user":
                # 验证创建用户的必需参数
                required_fields = ["user_id", "username"]
                for field in required_fields:
                    if field not in arguments:
                        return False

            elif action == "create_role":
                # 验证创建角色的必需参数
                required_fields = ["role_id", "name", "description"]
                for field in required_fields:
                    if field not in arguments:
                        return False

            elif action == "create_permission":
                # 验证创建权限的必需参数
                required_fields = ["permission_id", "name", "description", "resource_type", "permission_type"]
                for field in required_fields:
                    if field not in arguments:
                        return False

            return True

        except Exception as e:
            return False
