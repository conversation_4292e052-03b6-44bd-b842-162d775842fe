"""
AI Agent Framework 服务验证工具

提供全面的服务验证功能，确保注册的服务能够正常工作：
- 连接性测试
- 认证验证
- 端点功能测试
- 性能基准测试
- 健康检查
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import aiohttp
import yaml
from pydantic import BaseModel, Field

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager
from ai_agent_framework.tools.service_registry_tool import ServiceConfig, ServiceEndpoint
from ai_agent_framework.utils.logging_system import logging_system


class ValidationResult(BaseModel):
    """验证结果"""
    
    test_name: str = Field(..., description="测试名称")
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")
    duration: float = Field(..., description="执行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="测试时间")


class ServiceValidationReport(BaseModel):
    """服务验证报告"""
    
    service_name: str = Field(..., description="服务名称")
    service_url: str = Field(..., description="服务URL")
    overall_success: bool = Field(..., description="整体是否成功")
    test_results: List[ValidationResult] = Field(default_factory=list, description="测试结果列表")
    summary: Dict[str, Any] = Field(default_factory=dict, description="测试摘要")
    generated_at: datetime = Field(default_factory=datetime.now, description="报告生成时间")


class ServiceValidator(ToolInterface):
    """
    服务验证工具
    
    对注册的服务进行全面的验证测试。
    """
    
    def __init__(
        self, 
        auth_manager: Optional[ServiceAuthManager] = None,
        reports_dir: Optional[Union[str, Path]] = None
    ):
        """
        初始化服务验证工具
        
        Args:
            auth_manager: 认证管理器
            reports_dir: 报告存储目录
        """
        self._logger = logging_system.get_logger("service_validator")
        self.auth_manager = auth_manager
        self._reports_dir = Path(reports_dir or "./reports/service_validation")
        self._reports_dir.mkdir(parents=True, exist_ok=True)
        
        # HTTP会话
        self._session: Optional[aiohttp.ClientSession] = None
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "service_validator"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "验证注册的服务，包括连接性、认证、端点功能和性能测试"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["validate_service", "validate_endpoint", "health_check", "performance_test", "full_validation"],
                    "description": "验证操作类型"
                },
                "service_config": {
                    "type": "object",
                    "description": "服务配置（直接验证时需要）"
                },
                "config_file": {
                    "type": "string",
                    "description": "服务配置文件路径"
                },
                "endpoint_name": {
                    "type": "string",
                    "description": "要验证的端点名称（单独验证端点时需要）"
                },
                "test_parameters": {
                    "type": "object",
                    "description": "测试参数"
                },
                "options": {
                    "type": "object",
                    "description": "验证选项",
                    "properties": {
                        "timeout": {
                            "type": "number",
                            "description": "超时时间（秒）",
                            "default": 30
                        },
                        "max_retries": {
                            "type": "integer",
                            "description": "最大重试次数",
                            "default": 3
                        },
                        "performance_iterations": {
                            "type": "integer",
                            "description": "性能测试迭代次数",
                            "default": 5
                        },
                        "generate_report": {
                            "type": "boolean",
                            "description": "是否生成详细报告",
                            "default": True
                        }
                    }
                }
            },
            "required": ["action"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """执行服务验证"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )
            
            # 确保HTTP会话已初始化
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 根据操作类型执行相应功能
            if action == "validate_service":
                return await self._validate_service(arguments)
            elif action == "validate_endpoint":
                return await self._validate_endpoint(arguments)
            elif action == "health_check":
                return await self._health_check(arguments)
            elif action == "performance_test":
                return await self._performance_test(arguments)
            elif action == "full_validation":
                return await self._full_validation(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )
                
        except Exception as e:
            self._logger.error(f"服务验证执行失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"执行失败: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        return "action" in arguments and arguments["action"] in [
            "validate_service", "validate_endpoint", "health_check", "performance_test", "full_validation"
        ]
    
    async def _load_service_config(self, arguments: Dict[str, Any]) -> Optional[ServiceConfig]:
        """加载服务配置"""
        try:
            # 从参数或配置文件加载服务配置
            if "config_file" in arguments:
                config_file = Path(arguments["config_file"])
                if not config_file.exists():
                    raise FileNotFoundError(f"配置文件不存在: {config_file}")
                
                with open(config_file, 'r', encoding='utf-8') as f:
                    if config_file.suffix.lower() in ['.yaml', '.yml']:
                        config_data = yaml.safe_load(f)
                    else:
                        config_data = json.load(f)
            else:
                config_data = arguments.get("service_config", {})
            
            if not config_data:
                return None
            
            return ServiceConfig(**config_data)
            
        except Exception as e:
            self._logger.error(f"加载服务配置失败: {str(e)}")
            return None
    
    async def _validate_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """验证整个服务"""
        try:
            service_config = await self._load_service_config(arguments)
            if not service_config:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="无法加载服务配置"
                )
            
            options = arguments.get("options", {})
            test_results = []
            
            # 1. 连接性测试
            connectivity_result = await self._test_connectivity(service_config, options)
            test_results.append(connectivity_result)
            
            # 2. 认证测试
            if service_config.auth and service_config.auth.type != "none":
                auth_result = await self._test_authentication(service_config, options)
                test_results.append(auth_result)
            
            # 3. 端点测试
            for endpoint in service_config.endpoints:
                endpoint_result = await self._test_endpoint(service_config, endpoint, options)
                test_results.append(endpoint_result)
            
            # 生成验证报告
            report = ServiceValidationReport(
                service_name=service_config.name,
                service_url=str(service_config.base_url),
                overall_success=all(result.success for result in test_results),
                test_results=test_results,
                summary=self._generate_summary(test_results)
            )
            
            # 保存报告
            if options.get("generate_report", True):
                report_file = await self._save_report(report)
            else:
                report_file = None
            
            return ToolResult(
                tool_call_id="",
                success=report.overall_success,
                result={
                    "service_name": service_config.name,
                    "overall_success": report.overall_success,
                    "total_tests": len(test_results),
                    "passed_tests": sum(1 for r in test_results if r.success),
                    "failed_tests": sum(1 for r in test_results if not r.success),
                    "summary": report.summary,
                    "report_file": str(report_file) if report_file else None,
                    "test_results": [r.dict() for r in test_results]
                }
            )
            
        except Exception as e:
            self._logger.error(f"服务验证失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"服务验证失败: {str(e)}"
            )
    
    async def _test_connectivity(self, service_config: ServiceConfig, options: Dict[str, Any]) -> ValidationResult:
        """测试连接性"""
        start_time = time.time()
        
        try:
            timeout = options.get("timeout", 30)
            test_url = str(service_config.base_url)
            
            # 如果有健康检查路径，使用它
            if service_config.health_check_path:
                from urllib.parse import urljoin
                test_url = urljoin(test_url, service_config.health_check_path)
            
            async with self._session.get(
                test_url,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                duration = time.time() - start_time
                
                if response.status < 400:
                    return ValidationResult(
                        test_name="connectivity",
                        success=True,
                        message=f"连接成功，状态码: {response.status}",
                        details={
                            "status_code": response.status,
                            "response_headers": dict(response.headers),
                            "test_url": test_url
                        },
                        duration=duration
                    )
                else:
                    return ValidationResult(
                        test_name="connectivity",
                        success=False,
                        message=f"连接失败，状态码: {response.status}",
                        details={
                            "status_code": response.status,
                            "test_url": test_url
                        },
                        duration=duration
                    )
                    
        except Exception as e:
            duration = time.time() - start_time
            return ValidationResult(
                test_name="connectivity",
                success=False,
                message=f"连接异常: {str(e)}",
                details={"error": str(e), "test_url": test_url},
                duration=duration
            )

    async def _test_authentication(self, service_config: ServiceConfig, options: Dict[str, Any]) -> ValidationResult:
        """测试认证"""
        start_time = time.time()

        try:
            if not self.auth_manager:
                return ValidationResult(
                    test_name="authentication",
                    success=False,
                    message="未配置认证管理器",
                    details={},
                    duration=time.time() - start_time
                )

            # 获取认证头部
            auth_headers = await self.auth_manager.get_auth_headers_for_service(service_config.name)

            if not auth_headers:
                return ValidationResult(
                    test_name="authentication",
                    success=False,
                    message="未找到认证凭据",
                    details={},
                    duration=time.time() - start_time
                )

            # 使用认证头部测试请求
            test_url = str(service_config.base_url)
            if service_config.health_check_path:
                from urllib.parse import urljoin
                test_url = urljoin(test_url, service_config.health_check_path)

            headers = dict(service_config.headers)
            headers.update(auth_headers)

            async with self._session.get(
                test_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=options.get("timeout", 30))
            ) as response:
                duration = time.time() - start_time

                # 认证成功的标准：不是401或403
                if response.status not in [401, 403]:
                    return ValidationResult(
                        test_name="authentication",
                        success=True,
                        message=f"认证成功，状态码: {response.status}",
                        details={
                            "status_code": response.status,
                            "auth_headers_used": list(auth_headers.keys())
                        },
                        duration=duration
                    )
                else:
                    return ValidationResult(
                        test_name="authentication",
                        success=False,
                        message=f"认证失败，状态码: {response.status}",
                        details={
                            "status_code": response.status,
                            "auth_headers_used": list(auth_headers.keys())
                        },
                        duration=duration
                    )

        except Exception as e:
            duration = time.time() - start_time
            return ValidationResult(
                test_name="authentication",
                success=False,
                message=f"认证测试异常: {str(e)}",
                details={"error": str(e)},
                duration=duration
            )

    async def _test_endpoint(self, service_config: ServiceConfig, endpoint: ServiceEndpoint, options: Dict[str, Any]) -> ValidationResult:
        """测试单个端点"""
        start_time = time.time()

        try:
            from urllib.parse import urljoin

            # 构建请求URL
            url = urljoin(str(service_config.base_url), endpoint.path)

            # 准备请求头
            headers = dict(service_config.headers)

            # 添加认证头
            if self.auth_manager and endpoint.requires_auth:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(service_config.name)
                headers.update(auth_headers)

            # 准备测试参数
            test_params = options.get("test_parameters", {})

            # 处理路径参数
            for param_name, param_value in test_params.items():
                if f"{{{param_name}}}" in url:
                    url = url.replace(f"{{{param_name}}}", str(param_value))

            # 发送请求
            request_kwargs = {
                "headers": headers,
                "timeout": aiohttp.ClientTimeout(total=endpoint.timeout or 30)
            }

            if endpoint.method in ["POST", "PUT", "PATCH"] and test_params:
                if headers.get("Content-Type", "").startswith("application/json"):
                    request_kwargs["json"] = test_params
                else:
                    request_kwargs["data"] = test_params
            elif endpoint.method == "GET" and test_params:
                request_kwargs["params"] = test_params

            async with self._session.request(
                endpoint.method,
                url,
                **request_kwargs
            ) as response:
                duration = time.time() - start_time

                response_text = await response.text()

                if response.status < 400:
                    return ValidationResult(
                        test_name=f"endpoint_{endpoint.name}",
                        success=True,
                        message=f"端点 {endpoint.name} 测试成功",
                        details={
                            "endpoint_name": endpoint.name,
                            "method": endpoint.method,
                            "url": url,
                            "status_code": response.status,
                            "response_size": len(response_text),
                            "content_type": response.headers.get("content-type", "")
                        },
                        duration=duration
                    )
                else:
                    return ValidationResult(
                        test_name=f"endpoint_{endpoint.name}",
                        success=False,
                        message=f"端点 {endpoint.name} 测试失败，状态码: {response.status}",
                        details={
                            "endpoint_name": endpoint.name,
                            "method": endpoint.method,
                            "url": url,
                            "status_code": response.status,
                            "error_response": response_text[:500]  # 限制错误响应长度
                        },
                        duration=duration
                    )

        except Exception as e:
            duration = time.time() - start_time
            return ValidationResult(
                test_name=f"endpoint_{endpoint.name}",
                success=False,
                message=f"端点 {endpoint.name} 测试异常: {str(e)}",
                details={
                    "endpoint_name": endpoint.name,
                    "error": str(e)
                },
                duration=duration
            )

    async def _validate_endpoint(self, arguments: Dict[str, Any]) -> ToolResult:
        """验证单个端点"""
        try:
            service_config = await self._load_service_config(arguments)
            if not service_config:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="无法加载服务配置"
                )

            endpoint_name = arguments.get("endpoint_name")
            if not endpoint_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少端点名称"
                )

            # 查找指定端点
            endpoint = None
            for ep in service_config.endpoints:
                if ep.name == endpoint_name:
                    endpoint = ep
                    break

            if not endpoint:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"未找到端点: {endpoint_name}"
                )

            options = arguments.get("options", {})

            # 测试端点
            result = await self._test_endpoint(service_config, endpoint, options)

            return ToolResult(
                tool_call_id="",
                success=result.success,
                result={
                    "endpoint_name": endpoint_name,
                    "test_result": result.dict()
                }
            )

        except Exception as e:
            self._logger.error(f"端点验证失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"端点验证失败: {str(e)}"
            )

    async def _health_check(self, arguments: Dict[str, Any]) -> ToolResult:
        """健康检查"""
        try:
            service_config = await self._load_service_config(arguments)
            if not service_config:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="无法加载服务配置"
                )

            options = arguments.get("options", {})

            # 执行连接性测试作为健康检查
            result = await self._test_connectivity(service_config, options)

            return ToolResult(
                tool_call_id="",
                success=result.success,
                result={
                    "service_name": service_config.name,
                    "health_status": "healthy" if result.success else "unhealthy",
                    "check_result": result.dict()
                }
            )

        except Exception as e:
            self._logger.error(f"健康检查失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"健康检查失败: {str(e)}"
            )

    async def _performance_test(self, arguments: Dict[str, Any]) -> ToolResult:
        """性能测试"""
        try:
            service_config = await self._load_service_config(arguments)
            if not service_config:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="无法加载服务配置"
                )

            options = arguments.get("options", {})
            iterations = options.get("performance_iterations", 5)

            # 执行多次请求测试性能
            response_times = []
            success_count = 0

            test_url = str(service_config.base_url)
            if service_config.health_check_path:
                from urllib.parse import urljoin
                test_url = urljoin(test_url, service_config.health_check_path)

            headers = dict(service_config.headers)
            if self.auth_manager:
                auth_headers = await self.auth_manager.get_auth_headers_for_service(service_config.name)
                headers.update(auth_headers)

            for i in range(iterations):
                start_time = time.time()
                try:
                    async with self._session.get(
                        test_url,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=options.get("timeout", 30))
                    ) as response:
                        end_time = time.time()
                        response_time = (end_time - start_time) * 1000  # 转换为毫秒
                        response_times.append(response_time)

                        if response.status < 400:
                            success_count += 1

                except Exception:
                    pass  # 忽略单次请求失败

                # 在请求之间稍作延迟
                if i < iterations - 1:
                    await asyncio.sleep(0.1)

            # 计算性能指标
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                min_response_time = min(response_times)
                max_response_time = max(response_times)
                success_rate = (success_count / iterations) * 100

                performance_metrics = {
                    "iterations": iterations,
                    "avg_response_time": round(avg_response_time, 2),
                    "min_response_time": round(min_response_time, 2),
                    "max_response_time": round(max_response_time, 2),
                    "success_rate": round(success_rate, 2),
                    "successful_requests": success_count,
                    "failed_requests": iterations - success_count
                }

                # 性能评级
                if avg_response_time < 100:
                    performance_grade = "优秀"
                elif avg_response_time < 500:
                    performance_grade = "良好"
                elif avg_response_time < 1000:
                    performance_grade = "一般"
                else:
                    performance_grade = "较差"

                return ToolResult(
                    tool_call_id="",
                    success=success_rate > 80,  # 成功率超过80%认为测试成功
                    result={
                        "service_name": service_config.name,
                        "performance_metrics": performance_metrics,
                        "performance_grade": performance_grade,
                        "test_url": test_url
                    }
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="性能测试失败，无有效响应时间数据"
                )

        except Exception as e:
            self._logger.error(f"性能测试失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"性能测试失败: {str(e)}"
            )

    async def _full_validation(self, arguments: Dict[str, Any]) -> ToolResult:
        """完整验证"""
        try:
            # 执行完整的服务验证，包括所有测试类型
            service_result = await self._validate_service(arguments)

            if service_result.success:
                # 如果基础验证通过，再执行性能测试
                performance_result = await self._performance_test(arguments)

                # 合并结果
                combined_result = service_result.result.copy()
                combined_result["performance_test"] = performance_result.result if performance_result.success else {"error": performance_result.error}

                return ToolResult(
                    tool_call_id="",
                    success=service_result.success and performance_result.success,
                    result=combined_result
                )
            else:
                return service_result

        except Exception as e:
            self._logger.error(f"完整验证失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"完整验证失败: {str(e)}"
            )

    def _generate_summary(self, test_results: List[ValidationResult]) -> Dict[str, Any]:
        """生成测试摘要"""
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r.success)
        failed_tests = total_tests - passed_tests

        total_duration = sum(r.duration for r in test_results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0

        # 按测试类型分组
        test_types = {}
        for result in test_results:
            test_type = result.test_name.split('_')[0]  # 获取测试类型前缀
            if test_type not in test_types:
                test_types[test_type] = {"passed": 0, "failed": 0}

            if result.success:
                test_types[test_type]["passed"] += 1
            else:
                test_types[test_type]["failed"] += 1

        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0,
            "total_duration": round(total_duration, 2),
            "average_duration": round(avg_duration, 2),
            "test_types": test_types
        }

    async def _save_report(self, report: ServiceValidationReport) -> Path:
        """保存验证报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"{report.service_name}_validation_{timestamp}.json"
            report_file = self._reports_dir / report_filename

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report.dict(), f, indent=2, ensure_ascii=False, default=str)

            self._logger.info(f"验证报告已保存: {report_file}")
            return report_file

        except Exception as e:
            self._logger.error(f"保存验证报告失败: {str(e)}")
            raise

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self._session is None:
            self._session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.close()
            self._session = None
