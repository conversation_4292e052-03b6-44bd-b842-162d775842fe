"""
AI Agent Framework 服务认证管理工具

提供统一的服务认证管理功能，支持多种认证方式：
- API Key认证
- Bearer Token认证
- Basic认证
- OAuth2认证
- JWT认证
- 自定义认证
"""

import asyncio
import base64
import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlencode, urlparse

import aiohttp
import jwt
from pydantic import BaseModel, Field, HttpUrl, validator

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.exceptions import ToolError
from ai_agent_framework.utils.logging_system import logging_system


class AuthCredentials(BaseModel):
    """认证凭据"""
    
    auth_type: str = Field(..., description="认证类型")
    credentials: Dict[str, Any] = Field(..., description="认证凭据")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class OAuth2Token(BaseModel):
    """OAuth2令牌"""
    
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="Bearer", description="令牌类型")
    expires_in: Optional[int] = Field(None, description="过期时间（秒）")
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    scope: Optional[str] = Field(None, description="权限范围")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    @property
    def is_expired(self) -> bool:
        """检查令牌是否过期"""
        if not self.expires_in:
            return False
        
        expiry_time = self.created_at + timedelta(seconds=self.expires_in)
        return datetime.now() >= expiry_time
    
    @property
    def expires_at(self) -> Optional[datetime]:
        """获取过期时间"""
        if not self.expires_in:
            return None
        return self.created_at + timedelta(seconds=self.expires_in)


class ServiceAuthManager(ToolInterface):
    """
    服务认证管理工具
    
    管理各种服务的认证凭据，提供统一的认证接口。
    """
    
    def __init__(self, credentials_dir: Optional[Union[str, Path]] = None):
        """
        初始化认证管理器
        
        Args:
            credentials_dir: 凭据存储目录
        """
        self._logger = logging_system.get_logger("service_auth_manager")
        self._credentials_dir = Path(credentials_dir or "./configs/credentials")
        self._credentials_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存中的凭据缓存
        self._credentials_cache: Dict[str, AuthCredentials] = {}
        
        # OAuth2令牌缓存
        self._oauth2_tokens: Dict[str, OAuth2Token] = {}
        
        # HTTP会话
        self._session: Optional[aiohttp.ClientSession] = None
        
        # 加载已有凭据
        asyncio.create_task(self._load_credentials())
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "service_auth_manager"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "管理服务认证凭据，支持API Key、OAuth2、JWT等多种认证方式"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["store", "get", "refresh", "validate", "list", "delete"],
                    "description": "认证操作类型"
                },
                "service_name": {
                    "type": "string",
                    "description": "服务名称"
                },
                "auth_type": {
                    "type": "string",
                    "enum": ["api_key", "bearer_token", "basic_auth", "oauth2", "jwt", "custom"],
                    "description": "认证类型"
                },
                "credentials": {
                    "type": "object",
                    "description": "认证凭据",
                    "properties": {
                        "api_key": {"type": "string", "description": "API密钥"},
                        "token": {"type": "string", "description": "Bearer令牌"},
                        "username": {"type": "string", "description": "用户名"},
                        "password": {"type": "string", "description": "密码"},
                        "client_id": {"type": "string", "description": "OAuth2客户端ID"},
                        "client_secret": {"type": "string", "description": "OAuth2客户端密钥"},
                        "token_url": {"type": "string", "description": "OAuth2令牌URL"},
                        "scope": {"type": "string", "description": "OAuth2权限范围"},
                        "jwt_secret": {"type": "string", "description": "JWT密钥"},
                        "jwt_algorithm": {"type": "string", "description": "JWT算法"},
                        "custom_headers": {"type": "object", "description": "自定义头部"}
                    }
                },
                "test_url": {
                    "type": "string",
                    "description": "测试认证的URL"
                }
            },
            "required": ["action"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """执行认证管理操作"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )
            
            # 确保HTTP会话已初始化
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 根据操作类型执行相应功能
            if action == "store":
                return await self._store_credentials(arguments)
            elif action == "get":
                return await self._get_credentials(arguments)
            elif action == "refresh":
                return await self._refresh_credentials(arguments)
            elif action == "validate":
                return await self._validate_credentials(arguments)
            elif action == "list":
                return await self._list_credentials()
            elif action == "delete":
                return await self._delete_credentials(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )
                
        except Exception as e:
            self._logger.error(f"认证管理操作失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"执行失败: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        return "action" in arguments and arguments["action"] in [
            "store", "get", "refresh", "validate", "list", "delete"
        ]
    
    async def _load_credentials(self) -> None:
        """加载已有凭据"""
        try:
            credentials_file = self._credentials_dir / "credentials.json"
            if credentials_file.exists():
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    credentials_data = json.load(f)
                
                for service_name, cred_data in credentials_data.items():
                    try:
                        # 处理过期时间
                        if cred_data.get("expires_at"):
                            cred_data["expires_at"] = datetime.fromisoformat(
                                cred_data["expires_at"]
                            )
                        
                        credentials = AuthCredentials(**cred_data)
                        self._credentials_cache[service_name] = credentials
                        
                    except Exception as e:
                        self._logger.warning(f"加载凭据失败 {service_name}: {str(e)}")
                
                self._logger.info(f"已加载 {len(self._credentials_cache)} 个服务的凭据")
                
        except Exception as e:
            self._logger.error(f"加载凭据文件失败: {str(e)}")
    
    async def _store_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """存储认证凭据"""
        try:
            service_name = arguments.get("service_name")
            auth_type = arguments.get("auth_type")
            credentials = arguments.get("credentials", {})
            
            if not service_name or not auth_type:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称或认证类型"
                )
            
            # 验证凭据
            validation_result = await self._validate_credentials_data(auth_type, credentials)
            if not validation_result["valid"]:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"凭据验证失败: {validation_result['error']}"
                )
            
            # 创建认证凭据对象
            auth_credentials = AuthCredentials(
                auth_type=auth_type,
                credentials=credentials,
                metadata={
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }
            )
            
            # 存储到缓存
            self._credentials_cache[service_name] = auth_credentials
            
            # 持久化到文件
            await self._save_credentials()
            
            # 可选：测试认证
            test_url = arguments.get("test_url")
            test_result = None
            if test_url:
                test_result = await self._test_auth(service_name, test_url)
            
            self._logger.info(f"成功存储服务认证凭据: {service_name}")
            
            result = {
                "message": f"成功存储服务认证凭据: {service_name}",
                "service_name": service_name,
                "auth_type": auth_type
            }
            
            if test_result:
                result["test_result"] = test_result
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result=result
            )
            
        except Exception as e:
            self._logger.error(f"存储认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"存储认证凭据失败: {str(e)}"
            )

    async def _get_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取认证凭据"""
        try:
            service_name = arguments.get("service_name")
            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._credentials_cache:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"未找到服务的认证凭据: {service_name}"
                )

            credentials = self._credentials_cache[service_name]

            # 检查凭据是否过期
            if credentials.expires_at and credentials.expires_at < datetime.now():
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"认证凭据已过期: {service_name}"
                )

            # 生成认证头部
            auth_headers = await self._generate_auth_headers(credentials)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "service_name": service_name,
                    "auth_type": credentials.auth_type,
                    "auth_headers": auth_headers,
                    "expires_at": credentials.expires_at.isoformat() if credentials.expires_at else None
                }
            )

        except Exception as e:
            self._logger.error(f"获取认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取认证凭据失败: {str(e)}"
            )

    async def _refresh_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """刷新认证凭据"""
        try:
            service_name = arguments.get("service_name")
            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._credentials_cache:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"未找到服务的认证凭据: {service_name}"
                )

            credentials = self._credentials_cache[service_name]

            # 根据认证类型执行刷新
            if credentials.auth_type == "oauth2":
                refresh_result = await self._refresh_oauth2_token(service_name, credentials)
                if refresh_result["success"]:
                    return ToolResult(
                        tool_call_id="",
                        success=True,
                        result={
                            "message": f"成功刷新OAuth2令牌: {service_name}",
                            "service_name": service_name,
                            "new_token": refresh_result["token"]
                        }
                    )
                else:
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"刷新OAuth2令牌失败: {refresh_result['error']}"
                    )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"认证类型 {credentials.auth_type} 不支持刷新"
                )

        except Exception as e:
            self._logger.error(f"刷新认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"刷新认证凭据失败: {str(e)}"
            )

    async def _validate_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """验证认证凭据"""
        try:
            service_name = arguments.get("service_name")
            test_url = arguments.get("test_url")

            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._credentials_cache:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"未找到服务的认证凭据: {service_name}"
                )

            # 执行认证测试
            if test_url:
                test_result = await self._test_auth(service_name, test_url)
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={
                        "service_name": service_name,
                        "test_url": test_url,
                        "validation_result": test_result
                    }
                )
            else:
                # 只验证凭据格式
                credentials = self._credentials_cache[service_name]
                validation_result = await self._validate_credentials_data(
                    credentials.auth_type,
                    credentials.credentials
                )

                return ToolResult(
                    tool_call_id="",
                    success=validation_result["valid"],
                    result={
                        "service_name": service_name,
                        "validation_result": validation_result
                    }
                )

        except Exception as e:
            self._logger.error(f"验证认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"验证认证凭据失败: {str(e)}"
            )

    async def _list_credentials(self) -> ToolResult:
        """列出所有认证凭据"""
        try:
            credentials_list = []

            for service_name, credentials in self._credentials_cache.items():
                credentials_info = {
                    "service_name": service_name,
                    "auth_type": credentials.auth_type,
                    "expires_at": credentials.expires_at.isoformat() if credentials.expires_at else None,
                    "is_expired": credentials.expires_at < datetime.now() if credentials.expires_at else False,
                    "metadata": credentials.metadata
                }
                credentials_list.append(credentials_info)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"找到 {len(credentials_list)} 个服务的认证凭据",
                    "credentials": credentials_list,
                    "total_count": len(credentials_list)
                }
            )

        except Exception as e:
            self._logger.error(f"列出认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出认证凭据失败: {str(e)}"
            )

    async def _delete_credentials(self, arguments: Dict[str, Any]) -> ToolResult:
        """删除认证凭据"""
        try:
            service_name = arguments.get("service_name")
            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._credentials_cache:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"未找到服务的认证凭据: {service_name}"
                )

            # 从缓存中删除
            del self._credentials_cache[service_name]

            # 从OAuth2令牌缓存中删除
            if service_name in self._oauth2_tokens:
                del self._oauth2_tokens[service_name]

            # 持久化更改
            await self._save_credentials()

            self._logger.info(f"成功删除服务认证凭据: {service_name}")

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功删除服务认证凭据: {service_name}",
                    "service_name": service_name
                }
            )

        except Exception as e:
            self._logger.error(f"删除认证凭据失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"删除认证凭据失败: {str(e)}"
            )

    async def _validate_credentials_data(self, auth_type: str, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """验证凭据数据"""
        try:
            if auth_type == "api_key":
                if not credentials.get("api_key"):
                    return {"valid": False, "error": "缺少API密钥"}

            elif auth_type == "bearer_token":
                if not credentials.get("token"):
                    return {"valid": False, "error": "缺少Bearer令牌"}

            elif auth_type == "basic_auth":
                if not credentials.get("username") or not credentials.get("password"):
                    return {"valid": False, "error": "缺少用户名或密码"}

            elif auth_type == "oauth2":
                required_fields = ["client_id", "client_secret", "token_url"]
                for field in required_fields:
                    if not credentials.get(field):
                        return {"valid": False, "error": f"缺少OAuth2字段: {field}"}

            elif auth_type == "jwt":
                if not credentials.get("jwt_secret"):
                    return {"valid": False, "error": "缺少JWT密钥"}

            elif auth_type == "custom":
                if not credentials.get("custom_headers"):
                    return {"valid": False, "error": "缺少自定义头部"}

            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"验证失败: {str(e)}"}

    async def _generate_auth_headers(self, credentials: AuthCredentials) -> Dict[str, str]:
        """生成认证头部"""
        headers = {}

        try:
            if credentials.auth_type == "api_key":
                api_key = credentials.credentials.get("api_key")
                header_name = credentials.credentials.get("api_key_header", "X-API-Key")
                if api_key:
                    headers[header_name] = api_key

            elif credentials.auth_type == "bearer_token":
                token = credentials.credentials.get("token")
                if token:
                    headers["Authorization"] = f"Bearer {token}"

            elif credentials.auth_type == "basic_auth":
                username = credentials.credentials.get("username")
                password = credentials.credentials.get("password")
                if username and password:
                    credentials_str = base64.b64encode(
                        f"{username}:{password}".encode()
                    ).decode()
                    headers["Authorization"] = f"Basic {credentials_str}"

            elif credentials.auth_type == "oauth2":
                # 检查是否有缓存的令牌
                service_name = next(
                    (name for name, cred in self._credentials_cache.items() if cred == credentials),
                    None
                )
                if service_name and service_name in self._oauth2_tokens:
                    token = self._oauth2_tokens[service_name]
                    if not token.is_expired:
                        headers["Authorization"] = f"{token.token_type} {token.access_token}"

            elif credentials.auth_type == "jwt":
                jwt_token = await self._generate_jwt_token(credentials.credentials)
                if jwt_token:
                    headers["Authorization"] = f"Bearer {jwt_token}"

            elif credentials.auth_type == "custom":
                custom_headers = credentials.credentials.get("custom_headers", {})
                headers.update(custom_headers)

        except Exception as e:
            self._logger.error(f"生成认证头部失败: {str(e)}")

        return headers

    async def _generate_jwt_token(self, credentials: Dict[str, Any]) -> Optional[str]:
        """生成JWT令牌"""
        try:
            secret = credentials.get("jwt_secret")
            algorithm = credentials.get("jwt_algorithm", "HS256")

            if not secret:
                return None

            # 构建JWT载荷
            payload = {
                "iat": int(time.time()),
                "exp": int(time.time()) + 3600,  # 1小时过期
                **credentials.get("jwt_payload", {})
            }

            token = jwt.encode(payload, secret, algorithm=algorithm)
            return token

        except Exception as e:
            self._logger.error(f"生成JWT令牌失败: {str(e)}")
            return None

    async def _refresh_oauth2_token(self, service_name: str, credentials: AuthCredentials) -> Dict[str, Any]:
        """刷新OAuth2令牌"""
        try:
            creds = credentials.credentials
            token_url = creds.get("token_url")
            client_id = creds.get("client_id")
            client_secret = creds.get("client_secret")

            if not all([token_url, client_id, client_secret]):
                return {"success": False, "error": "缺少OAuth2配置"}

            # 检查是否有刷新令牌
            refresh_token = None
            if service_name in self._oauth2_tokens:
                refresh_token = self._oauth2_tokens[service_name].refresh_token

            if not refresh_token:
                # 使用客户端凭据流程
                data = {
                    "grant_type": "client_credentials",
                    "client_id": client_id,
                    "client_secret": client_secret
                }

                scope = creds.get("scope")
                if scope:
                    data["scope"] = scope
            else:
                # 使用刷新令牌流程
                data = {
                    "grant_type": "refresh_token",
                    "refresh_token": refresh_token,
                    "client_id": client_id,
                    "client_secret": client_secret
                }

            # 发送令牌请求
            async with self._session.post(
                token_url,
                data=data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            ) as response:
                if response.status == 200:
                    token_data = await response.json()

                    # 创建新的令牌对象
                    oauth2_token = OAuth2Token(
                        access_token=token_data["access_token"],
                        token_type=token_data.get("token_type", "Bearer"),
                        expires_in=token_data.get("expires_in"),
                        refresh_token=token_data.get("refresh_token", refresh_token),
                        scope=token_data.get("scope")
                    )

                    # 缓存令牌
                    self._oauth2_tokens[service_name] = oauth2_token

                    return {
                        "success": True,
                        "token": oauth2_token.dict()
                    }
                else:
                    error_text = await response.text()
                    return {
                        "success": False,
                        "error": f"HTTP {response.status}: {error_text}"
                    }

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _test_auth(self, service_name: str, test_url: str) -> Dict[str, Any]:
        """测试认证"""
        try:
            if service_name not in self._credentials_cache:
                return {
                    "success": False,
                    "error": f"未找到服务的认证凭据: {service_name}"
                }

            credentials = self._credentials_cache[service_name]
            auth_headers = await self._generate_auth_headers(credentials)

            # 发送测试请求
            start_time = time.time()
            async with self._session.get(
                test_url,
                headers=auth_headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                end_time = time.time()

                response_time = round((end_time - start_time) * 1000, 2)

                return {
                    "success": response.status < 400,
                    "status_code": response.status,
                    "response_time": response_time,
                    "auth_headers_sent": list(auth_headers.keys()),
                    "test_url": test_url
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "test_url": test_url
            }

    async def _save_credentials(self) -> None:
        """保存凭据到文件"""
        try:
            credentials_data = {}

            for service_name, credentials in self._credentials_cache.items():
                cred_dict = credentials.dict()

                # 处理日期时间序列化
                if cred_dict.get("expires_at"):
                    cred_dict["expires_at"] = cred_dict["expires_at"].isoformat()

                credentials_data[service_name] = cred_dict

            credentials_file = self._credentials_dir / "credentials.json"
            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(credentials_data, f, indent=2, ensure_ascii=False)

            self._logger.info(f"凭据已保存到: {credentials_file}")

        except Exception as e:
            self._logger.error(f"保存凭据失败: {str(e)}")
            raise

    async def get_auth_headers_for_service(self, service_name: str) -> Dict[str, str]:
        """
        为指定服务获取认证头部（供其他工具使用）

        Args:
            service_name: 服务名称

        Returns:
            Dict[str, str]: 认证头部
        """
        try:
            if service_name not in self._credentials_cache:
                return {}

            credentials = self._credentials_cache[service_name]

            # 检查凭据是否过期
            if credentials.expires_at and credentials.expires_at < datetime.now():
                # 尝试刷新凭据
                if credentials.auth_type == "oauth2":
                    refresh_result = await self._refresh_oauth2_token(service_name, credentials)
                    if not refresh_result["success"]:
                        self._logger.warning(f"刷新凭据失败: {service_name}")
                        return {}

            return await self._generate_auth_headers(credentials)

        except Exception as e:
            self._logger.error(f"获取认证头部失败 {service_name}: {str(e)}")
            return {}

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self._session is None:
            self._session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.close()
            self._session = None
