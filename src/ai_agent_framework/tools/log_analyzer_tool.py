"""
日志分析和告警工具

提供智能日志分析、模式识别、异常检测和告警功能。
"""

import asyncio
import json
import re
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Pattern, Union
from pydantic import BaseModel, Field
import uuid

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class AlertSeverity(str, Enum):
    """告警严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """告警状态枚举"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class LogEntry(BaseModel):
    """日志条目模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime
    level: LogLevel
    message: str
    source: str = ""
    component: str = ""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "message": self.message,
            "source": self.source,
            "component": self.component,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "request_id": self.request_id,
            "metadata": self.metadata
        }


class AlertRule(BaseModel):
    """告警规则模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    enabled: bool = True
    severity: AlertSeverity
    conditions: Dict[str, Any]
    threshold: Dict[str, Any] = Field(default_factory=dict)
    time_window: int = 300  # 时间窗口（秒）
    cooldown: int = 600  # 冷却时间（秒）
    actions: List[Dict[str, Any]] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class Alert(BaseModel):
    """告警模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str
    rule_name: str
    severity: AlertSeverity
    status: AlertStatus = AlertStatus.ACTIVE
    message: str
    details: Dict[str, Any] = Field(default_factory=dict)
    triggered_at: datetime = Field(default_factory=datetime.now)
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    resolved_by: Optional[str] = None
    count: int = 1
    
    def acknowledge(self, user_id: str) -> None:
        """确认告警"""
        self.status = AlertStatus.ACKNOWLEDGED
        self.acknowledged_at = datetime.now()
        self.acknowledged_by = user_id
    
    def resolve(self, user_id: str) -> None:
        """解决告警"""
        self.status = AlertStatus.RESOLVED
        self.resolved_at = datetime.now()
        self.resolved_by = user_id


class LogPattern(BaseModel):
    """日志模式模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    pattern: str
    regex: Optional[Pattern] = None
    description: str = ""
    category: str = "general"
    severity: LogLevel = LogLevel.INFO
    count: int = 0
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.pattern and not self.regex:
            try:
                self.regex = re.compile(self.pattern, re.IGNORECASE)
            except re.error:
                pass
    
    def matches(self, message: str) -> bool:
        """检查消息是否匹配模式"""
        if self.regex:
            return bool(self.regex.search(message))
        return self.pattern.lower() in message.lower()


class LogAnalyzer:
    """日志分析器"""
    
    def __init__(self):
        """初始化日志分析器"""
        self._patterns: Dict[str, LogPattern] = {}
        self._log_buffer: deque = deque(maxlen=10000)  # 日志缓冲区
        self._error_counts: Dict[str, int] = defaultdict(int)
        self._warning_counts: Dict[str, int] = defaultdict(int)
        self._performance_metrics: Dict[str, List[float]] = defaultdict(list)
        self._logger = logging_system.get_logger("log_analyzer")
    
    def add_pattern(self, pattern: LogPattern) -> None:
        """添加日志模式"""
        self._patterns[pattern.id] = pattern
        self._logger.info(f"添加日志模式: {pattern.name}")
    
    def remove_pattern(self, pattern_id: str) -> bool:
        """移除日志模式"""
        if pattern_id in self._patterns:
            pattern = self._patterns.pop(pattern_id)
            self._logger.info(f"移除日志模式: {pattern.name}")
            return True
        return False
    
    def analyze_log(self, log_entry: LogEntry) -> Dict[str, Any]:
        """分析单条日志"""
        analysis_result = {
            "log_id": log_entry.id,
            "timestamp": log_entry.timestamp,
            "level": log_entry.level,
            "matched_patterns": [],
            "anomalies": [],
            "metrics": {}
        }
        
        # 添加到缓冲区
        self._log_buffer.append(log_entry)
        
        # 模式匹配
        for pattern in self._patterns.values():
            if pattern.matches(log_entry.message):
                pattern.count += 1
                pattern.last_seen = log_entry.timestamp
                if not pattern.first_seen:
                    pattern.first_seen = log_entry.timestamp
                
                analysis_result["matched_patterns"].append({
                    "pattern_id": pattern.id,
                    "pattern_name": pattern.name,
                    "category": pattern.category
                })
        
        # 错误和警告统计
        if log_entry.level == LogLevel.ERROR:
            self._error_counts[log_entry.component] += 1
        elif log_entry.level == LogLevel.WARNING:
            self._warning_counts[log_entry.component] += 1
        
        # 性能指标提取
        self._extract_performance_metrics(log_entry, analysis_result)
        
        # 异常检测
        anomalies = self._detect_anomalies(log_entry)
        analysis_result["anomalies"] = anomalies
        
        return analysis_result
    
    def _extract_performance_metrics(self, log_entry: LogEntry, analysis_result: Dict[str, Any]) -> None:
        """提取性能指标"""
        message = log_entry.message
        
        # 响应时间提取
        response_time_patterns = [
            r'response_time[:\s]+(\d+(?:\.\d+)?)',
            r'took\s+(\d+(?:\.\d+)?)\s*ms',
            r'duration[:\s]+(\d+(?:\.\d+)?)',
            r'elapsed[:\s]+(\d+(?:\.\d+)?)'
        ]
        
        for pattern in response_time_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                response_time = float(match.group(1))
                self._performance_metrics["response_time"].append(response_time)
                analysis_result["metrics"]["response_time"] = response_time
                break
        
        # 内存使用提取
        memory_patterns = [
            r'memory[:\s]+(\d+(?:\.\d+)?)\s*(?:MB|GB|KB)',
            r'heap[:\s]+(\d+(?:\.\d+)?)\s*(?:MB|GB|KB)',
            r'used[:\s]+(\d+(?:\.\d+)?)\s*(?:MB|GB|KB)'
        ]
        
        for pattern in memory_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                memory_usage = float(match.group(1))
                self._performance_metrics["memory_usage"].append(memory_usage)
                analysis_result["metrics"]["memory_usage"] = memory_usage
                break
        
        # CPU使用提取
        cpu_patterns = [
            r'cpu[:\s]+(\d+(?:\.\d+)?)\s*%',
            r'load[:\s]+(\d+(?:\.\d+)?)'
        ]
        
        for pattern in cpu_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                cpu_usage = float(match.group(1))
                self._performance_metrics["cpu_usage"].append(cpu_usage)
                analysis_result["metrics"]["cpu_usage"] = cpu_usage
                break
    
    def _detect_anomalies(self, log_entry: LogEntry) -> List[Dict[str, Any]]:
        """检测异常"""
        anomalies = []
        
        # 检测错误率异常
        if log_entry.level in [LogLevel.ERROR, LogLevel.CRITICAL]:
            recent_logs = [log for log in list(self._log_buffer)[-100:] 
                          if log.component == log_entry.component]
            
            if len(recent_logs) >= 10:
                error_rate = sum(1 for log in recent_logs 
                               if log.level in [LogLevel.ERROR, LogLevel.CRITICAL]) / len(recent_logs)
                
                if error_rate > 0.1:  # 错误率超过10%
                    anomalies.append({
                        "type": "high_error_rate",
                        "severity": "high",
                        "description": f"组件 {log_entry.component} 错误率异常: {error_rate:.2%}",
                        "value": error_rate
                    })
        
        # 检测重复错误
        similar_errors = [log for log in list(self._log_buffer)[-50:] 
                         if log.level == LogLevel.ERROR and 
                         self._calculate_similarity(log.message, log_entry.message) > 0.8]
        
        if len(similar_errors) >= 5:
            anomalies.append({
                "type": "repeated_error",
                "severity": "medium",
                "description": f"检测到重复错误，最近50条日志中出现{len(similar_errors)}次",
                "count": len(similar_errors)
            })
        
        # 检测性能异常
        if "response_time" in log_entry.metadata:
            response_time = log_entry.metadata["response_time"]
            recent_times = self._performance_metrics["response_time"][-100:]
            
            if len(recent_times) >= 10:
                avg_time = sum(recent_times) / len(recent_times)
                if response_time > avg_time * 3:  # 响应时间超过平均值3倍
                    anomalies.append({
                        "type": "slow_response",
                        "severity": "medium",
                        "description": f"响应时间异常: {response_time}ms (平均: {avg_time:.2f}ms)",
                        "value": response_time,
                        "baseline": avg_time
                    })
        
        return anomalies
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简单的相似度计算，基于共同词汇
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union)
    
    def get_statistics(self, time_window: Optional[int] = None) -> Dict[str, Any]:
        """获取日志统计信息"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=time_window) if time_window else None
        
        # 过滤时间窗口内的日志
        if cutoff_time:
            recent_logs = [log for log in self._log_buffer if log.timestamp >= cutoff_time]
        else:
            recent_logs = list(self._log_buffer)
        
        # 统计各级别日志数量
        level_counts = defaultdict(int)
        component_counts = defaultdict(int)
        
        for log in recent_logs:
            level_counts[log.level.value] += 1
            component_counts[log.component] += 1
        
        # 计算性能指标统计
        performance_stats = {}
        for metric, values in self._performance_metrics.items():
            if values:
                performance_stats[metric] = {
                    "count": len(values),
                    "avg": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values)
                }
        
        return {
            "total_logs": len(recent_logs),
            "time_window": time_window,
            "level_distribution": dict(level_counts),
            "component_distribution": dict(component_counts),
            "performance_metrics": performance_stats,
            "pattern_matches": {p.name: p.count for p in self._patterns.values()},
            "error_counts": dict(self._error_counts),
            "warning_counts": dict(self._warning_counts)
        }


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        """初始化告警管理器"""
        self._rules: Dict[str, AlertRule] = {}
        self._alerts: Dict[str, Alert] = {}
        self._rule_states: Dict[str, Dict[str, Any]] = {}  # 规则状态跟踪
        self._logger = logging_system.get_logger("alert_manager")
    
    def add_rule(self, rule: AlertRule) -> None:
        """添加告警规则"""
        self._rules[rule.id] = rule
        self._rule_states[rule.id] = {
            "last_triggered": None,
            "trigger_count": 0,
            "suppressed_until": None
        }
        self._logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_id: str) -> bool:
        """移除告警规则"""
        if rule_id in self._rules:
            rule = self._rules.pop(rule_id)
            self._rule_states.pop(rule_id, None)
            self._logger.info(f"移除告警规则: {rule.name}")
            return True
        return False
    
    def evaluate_rules(self, analysis_result: Dict[str, Any], log_entry: LogEntry) -> List[Alert]:
        """评估告警规则"""
        triggered_alerts = []
        
        for rule in self._rules.values():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            rule_state = self._rule_states[rule.id]
            if rule_state["suppressed_until"] and datetime.now() < rule_state["suppressed_until"]:
                continue
            
            # 评估规则条件
            if self._evaluate_rule_conditions(rule, analysis_result, log_entry):
                alert = self._create_alert(rule, analysis_result, log_entry)
                triggered_alerts.append(alert)
                
                # 更新规则状态
                rule_state["last_triggered"] = datetime.now()
                rule_state["trigger_count"] += 1
                rule_state["suppressed_until"] = datetime.now() + timedelta(seconds=rule.cooldown)
        
        return triggered_alerts
    
    def _evaluate_rule_conditions(self, rule: AlertRule, analysis_result: Dict[str, Any], log_entry: LogEntry) -> bool:
        """评估规则条件"""
        conditions = rule.conditions
        
        # 日志级别条件
        if "log_level" in conditions:
            required_levels = conditions["log_level"]
            if isinstance(required_levels, str):
                required_levels = [required_levels]
            if log_entry.level.value not in required_levels:
                return False
        
        # 组件条件
        if "component" in conditions:
            required_components = conditions["component"]
            if isinstance(required_components, str):
                required_components = [required_components]
            if log_entry.component not in required_components:
                return False
        
        # 消息模式条件
        if "message_pattern" in conditions:
            pattern = conditions["message_pattern"]
            if not re.search(pattern, log_entry.message, re.IGNORECASE):
                return False
        
        # 异常条件
        if "anomaly_types" in conditions:
            required_anomalies = conditions["anomaly_types"]
            if isinstance(required_anomalies, str):
                required_anomalies = [required_anomalies]
            
            detected_anomalies = [anomaly["type"] for anomaly in analysis_result.get("anomalies", [])]
            if not any(anomaly_type in detected_anomalies for anomaly_type in required_anomalies):
                return False
        
        # 阈值条件
        if "threshold" in rule.threshold:
            threshold_config = rule.threshold["threshold"]
            metric_name = threshold_config.get("metric")
            operator = threshold_config.get("operator", ">")
            value = threshold_config.get("value")
            
            if metric_name and metric_name in analysis_result.get("metrics", {}):
                metric_value = analysis_result["metrics"][metric_name]
                
                if operator == ">" and metric_value <= value:
                    return False
                elif operator == "<" and metric_value >= value:
                    return False
                elif operator == "==" and metric_value != value:
                    return False
        
        return True
    
    def _create_alert(self, rule: AlertRule, analysis_result: Dict[str, Any], log_entry: LogEntry) -> Alert:
        """创建告警"""
        # 检查是否已存在相同的活跃告警
        existing_alert = None
        for alert in self._alerts.values():
            if (alert.rule_id == rule.id and 
                alert.status == AlertStatus.ACTIVE and
                alert.triggered_at > datetime.now() - timedelta(seconds=rule.time_window)):
                existing_alert = alert
                break
        
        if existing_alert:
            # 更新现有告警
            existing_alert.count += 1
            existing_alert.triggered_at = datetime.now()
            return existing_alert
        else:
            # 创建新告警
            alert = Alert(
                rule_id=rule.id,
                rule_name=rule.name,
                severity=rule.severity,
                message=self._generate_alert_message(rule, analysis_result, log_entry),
                details={
                    "log_entry": log_entry.to_dict(),
                    "analysis_result": analysis_result,
                    "rule_conditions": rule.conditions
                }
            )
            
            self._alerts[alert.id] = alert
            self._logger.warning(f"触发告警: {alert.message}")
            
            # 执行告警动作
            self._execute_alert_actions(rule, alert)
            
            return alert
    
    def _generate_alert_message(self, rule: AlertRule, analysis_result: Dict[str, Any], log_entry: LogEntry) -> str:
        """生成告警消息"""
        base_message = f"告警规则 '{rule.name}' 被触发"
        
        details = []
        if log_entry.component:
            details.append(f"组件: {log_entry.component}")
        
        if analysis_result.get("anomalies"):
            anomaly_types = [anomaly["type"] for anomaly in analysis_result["anomalies"]]
            details.append(f"异常类型: {', '.join(anomaly_types)}")
        
        if analysis_result.get("metrics"):
            metrics_info = []
            for metric, value in analysis_result["metrics"].items():
                metrics_info.append(f"{metric}: {value}")
            if metrics_info:
                details.append(f"指标: {', '.join(metrics_info)}")
        
        if details:
            return f"{base_message} - {'; '.join(details)}"
        else:
            return base_message
    
    def _execute_alert_actions(self, rule: AlertRule, alert: Alert) -> None:
        """执行告警动作"""
        for action in rule.actions:
            try:
                action_type = action.get("type")
                
                if action_type == "log":
                    self._logger.warning(f"告警动作 - 日志: {alert.message}")
                
                elif action_type == "email":
                    # 这里可以集成邮件发送功能
                    self._logger.info(f"告警动作 - 邮件: 发送告警邮件到 {action.get('recipients', [])}")
                
                elif action_type == "webhook":
                    # 这里可以集成Webhook调用
                    self._logger.info(f"告警动作 - Webhook: 调用 {action.get('url')}")
                
                elif action_type == "script":
                    # 这里可以执行自定义脚本
                    self._logger.info(f"告警动作 - 脚本: 执行 {action.get('script')}")
            
            except Exception as e:
                self._logger.error(f"执行告警动作失败: {e}")
    
    def acknowledge_alert(self, alert_id: str, user_id: str) -> bool:
        """确认告警"""
        if alert_id in self._alerts:
            alert = self._alerts[alert_id]
            alert.acknowledge(user_id)
            self._logger.info(f"告警已确认: {alert_id} by {user_id}")
            return True
        return False
    
    def resolve_alert(self, alert_id: str, user_id: str) -> bool:
        """解决告警"""
        if alert_id in self._alerts:
            alert = self._alerts[alert_id]
            alert.resolve(user_id)
            self._logger.info(f"告警已解决: {alert_id} by {user_id}")
            return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self._alerts.values() if alert.status == AlertStatus.ACTIVE]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        alerts = list(self._alerts.values())
        
        # 按严重程度统计
        severity_counts = defaultdict(int)
        status_counts = defaultdict(int)
        
        for alert in alerts:
            severity_counts[alert.severity.value] += 1
            status_counts[alert.status.value] += 1
        
        # 最近24小时的告警
        recent_alerts = [alert for alert in alerts 
                        if alert.triggered_at > datetime.now() - timedelta(hours=24)]
        
        return {
            "total_alerts": len(alerts),
            "active_alerts": len([a for a in alerts if a.status == AlertStatus.ACTIVE]),
            "recent_alerts_24h": len(recent_alerts),
            "severity_distribution": dict(severity_counts),
            "status_distribution": dict(status_counts),
            "rules_count": len(self._rules)
        }


class LogAnalyzerConfig(BaseModel):
    """日志分析器配置"""

    buffer_size: int = 10000
    enable_anomaly_detection: bool = True
    enable_pattern_matching: bool = True
    enable_performance_analysis: bool = True
    alert_cooldown_seconds: int = 600
    max_alerts_per_rule: int = 100


class LogAnalyzerTool(ToolInterface):
    """日志分析和告警工具"""

    def __init__(self, config: Optional[LogAnalyzerConfig] = None):
        """
        初始化日志分析工具

        Args:
            config: 日志分析器配置
        """
        self.config = config or LogAnalyzerConfig()
        self._analyzer = LogAnalyzer()
        self._alert_manager = AlertManager()
        self._logger = logging_system.get_logger("log_analyzer_tool")

        # 初始化默认模式和规则
        self._initialize_default_patterns()
        self._initialize_default_rules()

    def _initialize_default_patterns(self) -> None:
        """初始化默认日志模式"""
        default_patterns = [
            LogPattern(
                name="数据库连接错误",
                pattern=r"database.*connection.*(?:failed|error|timeout)",
                category="database",
                severity=LogLevel.ERROR
            ),
            LogPattern(
                name="内存不足",
                pattern=r"out of memory|memory.*exhausted|heap.*overflow",
                category="system",
                severity=LogLevel.CRITICAL
            ),
            LogPattern(
                name="HTTP 5xx错误",
                pattern=r"HTTP.*5\d{2}|status.*5\d{2}",
                category="http",
                severity=LogLevel.ERROR
            ),
            LogPattern(
                name="认证失败",
                pattern=r"authentication.*failed|login.*failed|unauthorized",
                category="security",
                severity=LogLevel.WARNING
            ),
            LogPattern(
                name="文件系统错误",
                pattern=r"no space left|disk.*full|file.*not found|permission denied",
                category="filesystem",
                severity=LogLevel.ERROR
            ),
            LogPattern(
                name="网络超时",
                pattern=r"connection.*timeout|network.*timeout|request.*timeout",
                category="network",
                severity=LogLevel.WARNING
            )
        ]

        for pattern in default_patterns:
            self._analyzer.add_pattern(pattern)

    def _initialize_default_rules(self) -> None:
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                name="高错误率告警",
                description="当错误率超过阈值时触发告警",
                severity=AlertSeverity.HIGH,
                conditions={
                    "anomaly_types": ["high_error_rate"]
                },
                time_window=300,
                cooldown=600,
                actions=[
                    {"type": "log", "message": "检测到高错误率"},
                    {"type": "email", "recipients": ["<EMAIL>"]}
                ]
            ),
            AlertRule(
                name="重复错误告警",
                description="当检测到重复错误时触发告警",
                severity=AlertSeverity.MEDIUM,
                conditions={
                    "anomaly_types": ["repeated_error"]
                },
                time_window=300,
                cooldown=300,
                actions=[
                    {"type": "log", "message": "检测到重复错误"}
                ]
            ),
            AlertRule(
                name="关键错误告警",
                description="当出现关键级别错误时立即告警",
                severity=AlertSeverity.CRITICAL,
                conditions={
                    "log_level": ["CRITICAL"]
                },
                time_window=60,
                cooldown=60,
                actions=[
                    {"type": "log", "message": "检测到关键错误"},
                    {"type": "email", "recipients": ["<EMAIL>"]},
                    {"type": "webhook", "url": "https://hooks.slack.com/..."}
                ]
            ),
            AlertRule(
                name="性能异常告警",
                description="当响应时间异常时触发告警",
                severity=AlertSeverity.MEDIUM,
                conditions={
                    "anomaly_types": ["slow_response"]
                },
                threshold={
                    "threshold": {
                        "metric": "response_time",
                        "operator": ">",
                        "value": 1000
                    }
                },
                time_window=300,
                cooldown=600,
                actions=[
                    {"type": "log", "message": "检测到性能异常"}
                ]
            )
        ]

        for rule in default_rules:
            self._alert_manager.add_rule(rule)

    @property
    def name(self) -> str:
        """工具名称"""
        return "log_analyzer_tool"

    @property
    def description(self) -> str:
        """工具描述"""
        return "智能日志分析和告警工具，提供日志模式识别、异常检测和告警功能"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数模式"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": [
                        "analyze_log", "add_pattern", "remove_pattern", "list_patterns",
                        "add_rule", "remove_rule", "list_rules", "get_alerts",
                        "acknowledge_alert", "resolve_alert", "get_statistics",
                        "get_alert_statistics"
                    ]
                },
                "log_data": {
                    "type": "object",
                    "description": "日志数据",
                    "properties": {
                        "timestamp": {"type": "string"},
                        "level": {"type": "string"},
                        "message": {"type": "string"},
                        "source": {"type": "string"},
                        "component": {"type": "string"},
                        "metadata": {"type": "object"}
                    }
                },
                "pattern_config": {
                    "type": "object",
                    "description": "日志模式配置"
                },
                "rule_config": {
                    "type": "object",
                    "description": "告警规则配置"
                },
                "pattern_id": {"type": "string"},
                "rule_id": {"type": "string"},
                "alert_id": {"type": "string"},
                "user_id": {"type": "string"},
                "time_window": {"type": "integer"}
            },
            "required": ["action"]
        }

    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行日志分析操作"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少action参数"
                )

            # 日志分析操作
            if action == "analyze_log":
                return await self._analyze_log(arguments)
            elif action == "get_statistics":
                return await self._get_statistics(arguments)

            # 模式管理操作
            elif action == "add_pattern":
                return await self._add_pattern(arguments)
            elif action == "remove_pattern":
                return await self._remove_pattern(arguments)
            elif action == "list_patterns":
                return await self._list_patterns()

            # 规则管理操作
            elif action == "add_rule":
                return await self._add_rule(arguments)
            elif action == "remove_rule":
                return await self._remove_rule(arguments)
            elif action == "list_rules":
                return await self._list_rules()

            # 告警管理操作
            elif action == "get_alerts":
                return await self._get_alerts()
            elif action == "acknowledge_alert":
                return await self._acknowledge_alert(arguments)
            elif action == "resolve_alert":
                return await self._resolve_alert(arguments)
            elif action == "get_alert_statistics":
                return await self._get_alert_statistics()

            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

        except Exception as e:
            self._logger.error(f"执行日志分析操作失败: {e}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _analyze_log(self, arguments: Dict[str, Any]) -> ToolResult:
        """分析日志"""
        try:
            log_data = arguments.get("log_data", {})

            # 验证必需参数
            required_fields = ["timestamp", "level", "message"]
            for field in required_fields:
                if field not in log_data:
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"缺少必需参数: {field}"
                    )

            # 创建日志条目
            log_entry = LogEntry(
                timestamp=datetime.fromisoformat(log_data["timestamp"].replace('Z', '+00:00')),
                level=LogLevel(log_data["level"].upper()),
                message=log_data["message"],
                source=log_data.get("source", ""),
                component=log_data.get("component", ""),
                user_id=log_data.get("user_id"),
                session_id=log_data.get("session_id"),
                request_id=log_data.get("request_id"),
                metadata=log_data.get("metadata", {})
            )

            # 分析日志
            analysis_result = self._analyzer.analyze_log(log_entry)

            # 评估告警规则
            triggered_alerts = self._alert_manager.evaluate_rules(analysis_result, log_entry)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "analysis": analysis_result,
                    "triggered_alerts": [
                        {
                            "id": alert.id,
                            "rule_name": alert.rule_name,
                            "severity": alert.severity.value,
                            "message": alert.message,
                            "status": alert.status.value
                        }
                        for alert in triggered_alerts
                    ],
                    "alert_count": len(triggered_alerts)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"分析日志失败: {str(e)}"
            )

    async def _add_pattern(self, arguments: Dict[str, Any]) -> ToolResult:
        """添加日志模式"""
        try:
            pattern_config = arguments.get("pattern_config", {})

            required_fields = ["name", "pattern"]
            for field in required_fields:
                if field not in pattern_config:
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"缺少必需参数: {field}"
                    )

            pattern = LogPattern(
                name=pattern_config["name"],
                pattern=pattern_config["pattern"],
                description=pattern_config.get("description", ""),
                category=pattern_config.get("category", "general"),
                severity=LogLevel(pattern_config.get("severity", "INFO").upper())
            )

            self._analyzer.add_pattern(pattern)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "pattern_id": pattern.id,
                    "message": f"日志模式添加成功: {pattern.name}"
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"添加日志模式失败: {str(e)}"
            )

    async def _remove_pattern(self, arguments: Dict[str, Any]) -> ToolResult:
        """移除日志模式"""
        try:
            pattern_id = arguments.get("pattern_id")
            if not pattern_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少pattern_id参数"
                )

            success = self._analyzer.remove_pattern(pattern_id)

            if success:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={"message": f"日志模式删除成功: {pattern_id}"}
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"日志模式不存在: {pattern_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"删除日志模式失败: {str(e)}"
            )

    async def _list_patterns(self) -> ToolResult:
        """列出日志模式"""
        try:
            patterns = [
                {
                    "id": pattern.id,
                    "name": pattern.name,
                    "pattern": pattern.pattern,
                    "category": pattern.category,
                    "severity": pattern.severity.value,
                    "count": pattern.count,
                    "first_seen": pattern.first_seen.isoformat() if pattern.first_seen else None,
                    "last_seen": pattern.last_seen.isoformat() if pattern.last_seen else None
                }
                for pattern in self._analyzer._patterns.values()
            ]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "patterns": patterns,
                    "count": len(patterns)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出日志模式失败: {str(e)}"
            )

    async def _get_statistics(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取统计信息"""
        try:
            time_window = arguments.get("time_window")
            statistics = self._analyzer.get_statistics(time_window)

            return ToolResult(
                tool_call_id="",
                success=True,
                result=statistics
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取统计信息失败: {str(e)}"
            )

    async def _get_alerts(self) -> ToolResult:
        """获取告警"""
        try:
            active_alerts = self._alert_manager.get_active_alerts()

            alerts_data = [
                {
                    "id": alert.id,
                    "rule_name": alert.rule_name,
                    "severity": alert.severity.value,
                    "status": alert.status.value,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat(),
                    "count": alert.count,
                    "acknowledged_at": alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                    "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
                }
                for alert in active_alerts
            ]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "alerts": alerts_data,
                    "count": len(alerts_data)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取告警失败: {str(e)}"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            if action == "analyze_log":
                log_data = arguments.get("log_data", {})
                required_fields = ["timestamp", "level", "message"]
                for field in required_fields:
                    if field not in log_data:
                        return False

            elif action == "add_pattern":
                pattern_config = arguments.get("pattern_config", {})
                required_fields = ["name", "pattern"]
                for field in required_fields:
                    if field not in pattern_config:
                        return False

            elif action in ["remove_pattern"]:
                if "pattern_id" not in arguments:
                    return False

            return True

        except Exception:
            return False
