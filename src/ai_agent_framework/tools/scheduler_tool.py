"""
定时任务调度工具

提供定时任务调度和管理功能，支持Cron表达式和复杂调度策略。
"""

import asyncio
import json
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union
from pydantic import BaseModel, Field
import uuid

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.utils.logging_system import logging_system


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消
    PAUSED = "paused"        # 已暂停


class TriggerType(str, Enum):
    """触发器类型枚举"""
    CRON = "cron"           # Cron表达式
    INTERVAL = "interval"   # 固定间隔
    DATE = "date"           # 指定日期时间
    ONCE = "once"           # 一次性任务


class ScheduledTask(BaseModel):
    """定时任务模型"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str = ""
    trigger_type: TriggerType
    trigger_config: Dict[str, Any]
    task_config: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0
    max_runs: Optional[int] = None
    timeout_seconds: Optional[int] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def update_status(self, status: TaskStatus) -> None:
        """更新任务状态"""
        self.status = status
        self.updated_at = datetime.now()


class TaskExecution(BaseModel):
    """任务执行记录"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    task_id: str
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    status: TaskStatus = TaskStatus.RUNNING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    duration_seconds: Optional[float] = None
    
    def complete(self, result: Optional[Dict[str, Any]] = None, error: Optional[str] = None) -> None:
        """完成执行"""
        self.end_time = datetime.now()
        self.duration_seconds = (self.end_time - self.start_time).total_seconds()
        self.status = TaskStatus.FAILED if error else TaskStatus.COMPLETED
        self.result = result
        self.error = error


class CronParser:
    """Cron表达式解析器"""
    
    @staticmethod
    def parse_cron(cron_expression: str) -> Dict[str, Any]:
        """
        解析Cron表达式
        
        Args:
            cron_expression: Cron表达式 (分 时 日 月 周)
            
        Returns:
            Dict: 解析后的时间配置
        """
        try:
            parts = cron_expression.strip().split()
            if len(parts) != 5:
                raise ValueError("Cron表达式必须包含5个部分：分 时 日 月 周")
            
            minute, hour, day, month, weekday = parts
            
            return {
                "minute": CronParser._parse_field(minute, 0, 59),
                "hour": CronParser._parse_field(hour, 0, 23),
                "day": CronParser._parse_field(day, 1, 31),
                "month": CronParser._parse_field(month, 1, 12),
                "weekday": CronParser._parse_field(weekday, 0, 6)
            }
        
        except Exception as e:
            raise ValueError(f"无效的Cron表达式: {e}")
    
    @staticmethod
    def _parse_field(field: str, min_val: int, max_val: int) -> List[int]:
        """解析Cron字段"""
        if field == "*":
            return list(range(min_val, max_val + 1))
        
        values = []
        for part in field.split(","):
            if "/" in part:
                # 步长表达式
                range_part, step = part.split("/")
                step = int(step)
                if range_part == "*":
                    values.extend(list(range(min_val, max_val + 1, step)))
                else:
                    start, end = map(int, range_part.split("-"))
                    values.extend(list(range(start, end + 1, step)))
            elif "-" in part:
                # 范围表达式
                start, end = map(int, part.split("-"))
                values.extend(list(range(start, end + 1)))
            else:
                # 单个值
                values.append(int(part))
        
        return sorted(list(set(values)))
    
    @staticmethod
    def next_run_time(cron_config: Dict[str, Any], from_time: Optional[datetime] = None) -> datetime:
        """计算下次执行时间"""
        if from_time is None:
            from_time = datetime.now()
        
        # 从下一分钟开始计算
        next_time = from_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        
        # 最多查找未来一年
        max_time = from_time + timedelta(days=365)
        
        while next_time < max_time:
            if (next_time.minute in cron_config["minute"] and
                next_time.hour in cron_config["hour"] and
                next_time.day in cron_config["day"] and
                next_time.month in cron_config["month"] and
                next_time.weekday() in cron_config["weekday"]):
                return next_time
            
            next_time += timedelta(minutes=1)
        
        raise ValueError("无法找到下次执行时间")


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self._tasks: Dict[str, ScheduledTask] = {}
        self._executions: Dict[str, TaskExecution] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._scheduler_task: Optional[asyncio.Task] = None
        self._running = False
        self._logger = logging_system.get_logger("task_scheduler")
    
    async def start(self) -> None:
        """启动调度器"""
        if self._running:
            return
        
        self._running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        self._logger.info("任务调度器已启动")
    
    async def stop(self) -> None:
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        
        # 停止调度器循环
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        for task in self._running_tasks.values():
            task.cancel()
        
        if self._running_tasks:
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)
        
        self._running_tasks.clear()
        self._logger.info("任务调度器已停止")
    
    async def _scheduler_loop(self) -> None:
        """调度器主循环"""
        try:
            while self._running:
                await self._check_and_execute_tasks()
                await asyncio.sleep(1)  # 每秒检查一次
        
        except asyncio.CancelledError:
            self._logger.info("调度器循环已取消")
        except Exception as e:
            self._logger.error(f"调度器循环异常: {e}")
    
    async def _check_and_execute_tasks(self) -> None:
        """检查并执行到期任务"""
        now = datetime.now()
        
        for task in self._tasks.values():
            if (task.status == TaskStatus.PENDING and 
                task.next_run and 
                task.next_run <= now and
                task.id not in self._running_tasks):
                
                # 检查最大执行次数
                if task.max_runs and task.run_count >= task.max_runs:
                    task.update_status(TaskStatus.COMPLETED)
                    continue
                
                # 执行任务
                execution_task = asyncio.create_task(self._execute_task(task))
                self._running_tasks[task.id] = execution_task
    
    async def _execute_task(self, task: ScheduledTask) -> None:
        """执行单个任务"""
        execution = TaskExecution(task_id=task.id)
        self._executions[execution.id] = execution
        
        try:
            task.update_status(TaskStatus.RUNNING)
            task.last_run = datetime.now()
            task.run_count += 1
            
            self._logger.info(f"开始执行任务: {task.name} ({task.id})")
            
            # 执行任务逻辑
            result = await self._run_task_logic(task, execution)
            
            execution.complete(result=result)
            task.update_status(TaskStatus.PENDING)
            
            # 计算下次执行时间
            self._calculate_next_run(task)
            
            self._logger.info(f"任务执行完成: {task.name} ({task.id})")
        
        except Exception as e:
            error_msg = str(e)
            execution.complete(error=error_msg)
            
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.update_status(TaskStatus.PENDING)
                # 延迟重试
                retry_delay = min(60 * (2 ** task.retry_count), 3600)  # 指数退避，最大1小时
                task.next_run = datetime.now() + timedelta(seconds=retry_delay)
                self._logger.warning(f"任务执行失败，将在{retry_delay}秒后重试: {task.name} - {error_msg}")
            else:
                task.update_status(TaskStatus.FAILED)
                self._logger.error(f"任务执行失败，已达到最大重试次数: {task.name} - {error_msg}")
        
        finally:
            # 清理运行中的任务记录
            self._running_tasks.pop(task.id, None)
    
    async def _run_task_logic(self, task: ScheduledTask, execution: TaskExecution) -> Dict[str, Any]:
        """执行任务逻辑"""
        # 这里可以根据task_config中的配置执行不同类型的任务
        # 例如：HTTP请求、数据库操作、文件处理等
        
        task_type = task.task_config.get("type", "custom")
        
        if task_type == "http_request":
            return await self._execute_http_task(task, execution)
        elif task_type == "shell_command":
            return await self._execute_shell_task(task, execution)
        elif task_type == "python_function":
            return await self._execute_python_task(task, execution)
        else:
            # 自定义任务类型
            return await self._execute_custom_task(task, execution)
    
    async def _execute_http_task(self, task: ScheduledTask, execution: TaskExecution) -> Dict[str, Any]:
        """执行HTTP请求任务"""
        import aiohttp
        
        config = task.task_config
        url = config.get("url")
        method = config.get("method", "GET").upper()
        headers = config.get("headers", {})
        data = config.get("data")
        timeout = task.timeout_seconds or 30
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.request(method, url, headers=headers, json=data) as response:
                result = {
                    "status_code": response.status,
                    "headers": dict(response.headers),
                    "content": await response.text()
                }
                
                if response.status >= 400:
                    raise Exception(f"HTTP请求失败: {response.status}")
                
                return result
    
    async def _execute_shell_task(self, task: ScheduledTask, execution: TaskExecution) -> Dict[str, Any]:
        """执行Shell命令任务"""
        config = task.task_config
        command = config.get("command")
        timeout = task.timeout_seconds or 60
        
        if not command:
            raise ValueError("Shell任务缺少command配置")
        
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
            
            result = {
                "return_code": process.returncode,
                "stdout": stdout.decode(),
                "stderr": stderr.decode()
            }
            
            if process.returncode != 0:
                raise Exception(f"命令执行失败: {stderr.decode()}")
            
            return result
        
        except asyncio.TimeoutError:
            process.kill()
            raise Exception("命令执行超时")
    
    async def _execute_python_task(self, task: ScheduledTask, execution: TaskExecution) -> Dict[str, Any]:
        """执行Python函数任务"""
        config = task.task_config
        function_name = config.get("function")
        module_name = config.get("module")
        args = config.get("args", [])
        kwargs = config.get("kwargs", {})
        
        if not function_name:
            raise ValueError("Python任务缺少function配置")
        
        # 动态导入模块和函数
        if module_name:
            import importlib
            module = importlib.import_module(module_name)
            func = getattr(module, function_name)
        else:
            # 从全局命名空间获取函数
            func = globals().get(function_name)
            if not func:
                raise ValueError(f"找不到函数: {function_name}")
        
        # 执行函数
        if asyncio.iscoroutinefunction(func):
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
        
        return {"result": result}
    
    async def _execute_custom_task(self, task: ScheduledTask, execution: TaskExecution) -> Dict[str, Any]:
        """执行自定义任务"""
        # 默认实现：记录任务执行
        return {
            "message": f"自定义任务 {task.name} 执行完成",
            "config": task.task_config
        }
    
    def _calculate_next_run(self, task: ScheduledTask) -> None:
        """计算下次执行时间"""
        try:
            if task.trigger_type == TriggerType.CRON:
                cron_expression = task.trigger_config.get("expression")
                if cron_expression:
                    cron_config = CronParser.parse_cron(cron_expression)
                    task.next_run = CronParser.next_run_time(cron_config)
            
            elif task.trigger_type == TriggerType.INTERVAL:
                interval_seconds = task.trigger_config.get("seconds", 60)
                task.next_run = datetime.now() + timedelta(seconds=interval_seconds)
            
            elif task.trigger_type == TriggerType.DATE:
                # 一次性任务，不需要计算下次执行时间
                task.next_run = None
                task.update_status(TaskStatus.COMPLETED)
            
            elif task.trigger_type == TriggerType.ONCE:
                # 一次性任务，不需要计算下次执行时间
                task.next_run = None
                task.update_status(TaskStatus.COMPLETED)
        
        except Exception as e:
            self._logger.error(f"计算下次执行时间失败: {e}")
            task.update_status(TaskStatus.FAILED)
    
    def add_task(self, task: ScheduledTask) -> str:
        """添加任务"""
        # 计算首次执行时间
        self._calculate_next_run(task)
        self._tasks[task.id] = task
        self._logger.info(f"任务已添加: {task.name} ({task.id})")
        return task.id
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_id in self._tasks:
            # 取消正在运行的任务
            if task_id in self._running_tasks:
                self._running_tasks[task_id].cancel()
                del self._running_tasks[task_id]
            
            del self._tasks[task_id]
            self._logger.info(f"任务已移除: {task_id}")
            return True
        return False
    
    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """获取任务"""
        return self._tasks.get(task_id)
    
    def list_tasks(self, status: Optional[TaskStatus] = None) -> List[ScheduledTask]:
        """列出任务"""
        if status:
            return [task for task in self._tasks.values() if task.status == status]
        return list(self._tasks.values())
    
    def get_executions(self, task_id: Optional[str] = None) -> List[TaskExecution]:
        """获取执行记录"""
        if task_id:
            return [exec for exec in self._executions.values() if exec.task_id == task_id]
        return list(self._executions.values())


class SchedulerConfig(BaseModel):
    """调度器配置"""

    auto_start: bool = True
    max_concurrent_tasks: int = 10
    execution_history_limit: int = 1000
    cleanup_interval_hours: int = 24


class SchedulerTool(ToolInterface):
    """定时任务调度工具"""

    def __init__(self, config: Optional[SchedulerConfig] = None):
        """
        初始化调度器工具

        Args:
            config: 调度器配置
        """
        self.config = config or SchedulerConfig()
        self._scheduler = TaskScheduler()
        self._logger = logging_system.get_logger("scheduler_tool")

        # 如果配置了自动启动，则启动调度器
        if self.config.auto_start:
            asyncio.create_task(self._scheduler.start())

    @property
    def name(self) -> str:
        """工具名称"""
        return "scheduler_tool"

    @property
    def description(self) -> str:
        """工具描述"""
        return "定时任务调度和管理工具，支持Cron表达式和复杂调度策略"

    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数模式"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "操作类型",
                    "enum": [
                        "create_task", "update_task", "delete_task", "get_task",
                        "list_tasks", "pause_task", "resume_task", "run_task_now",
                        "get_executions", "start_scheduler", "stop_scheduler",
                        "scheduler_status"
                    ]
                },
                "task_id": {
                    "type": "string",
                    "description": "任务ID"
                },
                "task_config": {
                    "type": "object",
                    "description": "任务配置",
                    "properties": {
                        "name": {"type": "string"},
                        "description": {"type": "string"},
                        "trigger_type": {
                            "type": "string",
                            "enum": ["cron", "interval", "date", "once"]
                        },
                        "trigger_config": {"type": "object"},
                        "task_config": {"type": "object"},
                        "max_runs": {"type": "integer"},
                        "timeout_seconds": {"type": "integer"},
                        "max_retries": {"type": "integer"}
                    }
                },
                "status_filter": {
                    "type": "string",
                    "enum": ["pending", "running", "completed", "failed", "cancelled", "paused"]
                }
            },
            "required": ["action"]
        }

    async def execute(self, arguments: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ToolResult:
        """执行调度器操作"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少action参数"
                )

            # 任务管理操作
            if action == "create_task":
                return await self._create_task(arguments)
            elif action == "update_task":
                return await self._update_task(arguments)
            elif action == "delete_task":
                return await self._delete_task(arguments)
            elif action == "get_task":
                return await self._get_task(arguments)
            elif action == "list_tasks":
                return await self._list_tasks(arguments)
            elif action == "pause_task":
                return await self._pause_task(arguments)
            elif action == "resume_task":
                return await self._resume_task(arguments)
            elif action == "run_task_now":
                return await self._run_task_now(arguments)

            # 执行记录操作
            elif action == "get_executions":
                return await self._get_executions(arguments)

            # 调度器管理操作
            elif action == "start_scheduler":
                return await self._start_scheduler()
            elif action == "stop_scheduler":
                return await self._stop_scheduler()
            elif action == "scheduler_status":
                return await self._scheduler_status()

            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )

        except Exception as e:
            self._logger.error(f"执行调度器操作失败: {e}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"操作失败: {str(e)}"
            )

    async def _create_task(self, arguments: Dict[str, Any]) -> ToolResult:
        """创建任务"""
        try:
            task_config = arguments.get("task_config", {})

            # 验证必需参数
            required_fields = ["name", "trigger_type", "trigger_config", "task_config"]
            for field in required_fields:
                if field not in task_config:
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"缺少必需参数: {field}"
                    )

            # 创建任务对象
            task = ScheduledTask(
                name=task_config["name"],
                description=task_config.get("description", ""),
                trigger_type=TriggerType(task_config["trigger_type"]),
                trigger_config=task_config["trigger_config"],
                task_config=task_config["task_config"],
                max_runs=task_config.get("max_runs"),
                timeout_seconds=task_config.get("timeout_seconds"),
                max_retries=task_config.get("max_retries", 3)
            )

            # 添加到调度器
            task_id = self._scheduler.add_task(task)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "task_id": task_id,
                    "message": f"任务创建成功: {task.name}",
                    "next_run": task.next_run.isoformat() if task.next_run else None
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"创建任务失败: {str(e)}"
            )

    async def _delete_task(self, arguments: Dict[str, Any]) -> ToolResult:
        """删除任务"""
        try:
            task_id = arguments.get("task_id")
            if not task_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少task_id参数"
                )

            success = self._scheduler.remove_task(task_id)

            if success:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={"message": f"任务删除成功: {task_id}"}
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"任务不存在: {task_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"删除任务失败: {str(e)}"
            )

    async def _get_task(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取任务信息"""
        try:
            task_id = arguments.get("task_id")
            if not task_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少task_id参数"
                )

            task = self._scheduler.get_task(task_id)

            if task:
                return ToolResult(
                    tool_call_id="",
                    success=True,
                    result={"task": task.model_dump()}
                )
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"任务不存在: {task_id}"
                )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取任务失败: {str(e)}"
            )

    async def _list_tasks(self, arguments: Dict[str, Any]) -> ToolResult:
        """列出任务"""
        try:
            status_filter = arguments.get("status_filter")
            status = TaskStatus(status_filter) if status_filter else None

            tasks = self._scheduler.list_tasks(status)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "tasks": [task.model_dump() for task in tasks],
                    "count": len(tasks)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出任务失败: {str(e)}"
            )

    async def _pause_task(self, arguments: Dict[str, Any]) -> ToolResult:
        """暂停任务"""
        try:
            task_id = arguments.get("task_id")
            if not task_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少task_id参数"
                )

            task = self._scheduler.get_task(task_id)
            if not task:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"任务不存在: {task_id}"
                )

            task.update_status(TaskStatus.PAUSED)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={"message": f"任务已暂停: {task_id}"}
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"暂停任务失败: {str(e)}"
            )

    async def _resume_task(self, arguments: Dict[str, Any]) -> ToolResult:
        """恢复任务"""
        try:
            task_id = arguments.get("task_id")
            if not task_id:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少task_id参数"
                )

            task = self._scheduler.get_task(task_id)
            if not task:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"任务不存在: {task_id}"
                )

            task.update_status(TaskStatus.PENDING)
            self._scheduler._calculate_next_run(task)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"任务已恢复: {task_id}",
                    "next_run": task.next_run.isoformat() if task.next_run else None
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"恢复任务失败: {str(e)}"
            )

    async def _get_executions(self, arguments: Dict[str, Any]) -> ToolResult:
        """获取执行记录"""
        try:
            task_id = arguments.get("task_id")
            executions = self._scheduler.get_executions(task_id)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "executions": [exec.model_dump() for exec in executions],
                    "count": len(executions)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取执行记录失败: {str(e)}"
            )

    async def _start_scheduler(self) -> ToolResult:
        """启动调度器"""
        try:
            await self._scheduler.start()
            return ToolResult(
                tool_call_id="",
                success=True,
                result={"message": "调度器已启动"}
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"启动调度器失败: {str(e)}"
            )

    async def _stop_scheduler(self) -> ToolResult:
        """停止调度器"""
        try:
            await self._scheduler.stop()
            return ToolResult(
                tool_call_id="",
                success=True,
                result={"message": "调度器已停止"}
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"停止调度器失败: {str(e)}"
            )

    async def _scheduler_status(self) -> ToolResult:
        """获取调度器状态"""
        try:
            tasks = self._scheduler.list_tasks()
            running_tasks = [t for t in tasks if t.status == TaskStatus.RUNNING]
            pending_tasks = [t for t in tasks if t.status == TaskStatus.PENDING]

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "running": self._scheduler._running,
                    "total_tasks": len(tasks),
                    "running_tasks": len(running_tasks),
                    "pending_tasks": len(pending_tasks),
                    "active_executions": len(self._scheduler._running_tasks)
                }
            )

        except Exception as e:
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"获取调度器状态失败: {str(e)}"
            )

    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证参数"""
        try:
            action = arguments.get("action")
            if not action:
                return False

            if action == "create_task":
                task_config = arguments.get("task_config", {})
                required_fields = ["name", "trigger_type", "trigger_config", "task_config"]
                for field in required_fields:
                    if field not in task_config:
                        return False

            elif action in ["delete_task", "get_task", "pause_task", "resume_task", "run_task_now"]:
                if "task_id" not in arguments:
                    return False

            return True

        except Exception:
            return False
