"""
云存储适配器实现

为文件存储工具提供AWS S3、阿里云OSS、腾讯云COS等云存储后端支持。
支持文件上传、下载、管理等操作，提供统一的接口。
"""

import asyncio
import hashlib
import mimetypes
import os
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, BinaryIO
from urllib.parse import urlparse

from ai_agent_framework.tools.file_storage_tool import StorageAdapter, FileInfo
from ai_agent_framework.utils.logging_system import logging_system


class AWSS3Adapter(StorageAdapter):
    """AWS S3存储适配器"""
    
    def __init__(
        self,
        access_key_id: str,
        secret_access_key: str,
        bucket_name: str,
        region: str = "us-east-1",
        endpoint_url: Optional[str] = None,
        **kwargs
    ):
        """
        初始化AWS S3适配器
        
        Args:
            access_key_id: AWS访问密钥ID
            secret_access_key: AWS秘密访问密钥
            bucket_name: S3存储桶名称
            region: AWS区域
            endpoint_url: 自定义端点URL（用于兼容S3的服务）
        """
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.bucket_name = bucket_name
        self.region = region
        self.endpoint_url = endpoint_url
        self._client = None
        self._connected = False
        self._logger = logging_system.get_logger("aws_s3_adapter")
        
        # 检查boto3依赖
        try:
            import boto3
            from botocore.exceptions import ClientError, NoCredentialsError
            self._boto3 = boto3
            self._ClientError = ClientError
            self._NoCredentialsError = NoCredentialsError
        except ImportError:
            raise ImportError("AWS S3适配器需要boto3库，请运行: pip install boto3")
    
    async def connect(self) -> bool:
        """连接到AWS S3"""
        try:
            # 创建S3客户端
            session = self._boto3.Session(
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            
            if self.endpoint_url:
                self._client = session.client('s3', endpoint_url=self.endpoint_url)
            else:
                self._client = session.client('s3')
            
            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._client.head_bucket, Bucket=self.bucket_name
            )
            
            self._connected = True
            self._logger.info(f"已连接到AWS S3存储桶: {self.bucket_name}")
            return True
            
        except self._NoCredentialsError:
            self._logger.error("AWS凭据未配置")
            return False
        except self._ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                self._logger.error(f"存储桶不存在: {self.bucket_name}")
            else:
                self._logger.error(f"连接AWS S3失败: {e}")
            return False
        except Exception as e:
            self._logger.error(f"连接AWS S3时发生未知错误: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._client = None
        self._logger.info("已断开AWS S3连接")
        return True
    
    async def upload_file(
        self,
        local_path: str,
        remote_path: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """上传文件到S3"""
        if not self._connected or not self._client:
            return False
        
        try:
            # 准备上传参数
            extra_args = {}
            if metadata:
                extra_args['Metadata'] = {k: str(v) for k, v in metadata.items()}
            
            # 设置内容类型
            content_type, _ = mimetypes.guess_type(local_path)
            if content_type:
                extra_args['ContentType'] = content_type
            
            # 执行上传
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.upload_file,
                local_path,
                self.bucket_name,
                remote_path,
                extra_args
            )
            
            self._logger.info(f"文件上传成功: {local_path} -> s3://{self.bucket_name}/{remote_path}")
            return True
            
        except Exception as e:
            self._logger.error(f"上传文件失败: {e}")
            return False
    
    async def download_file(
        self,
        remote_path: str,
        local_path: str
    ) -> bool:
        """从S3下载文件"""
        if not self._connected or not self._client:
            return False
        
        try:
            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # 执行下载
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.download_file,
                self.bucket_name,
                remote_path,
                local_path
            )
            
            self._logger.info(f"文件下载成功: s3://{self.bucket_name}/{remote_path} -> {local_path}")
            return True
            
        except Exception as e:
            self._logger.error(f"下载文件失败: {e}")
            return False
    
    async def delete_file(self, remote_path: str) -> bool:
        """删除S3文件"""
        if not self._connected or not self._client:
            return False
        
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.delete_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )
            
            self._logger.info(f"文件删除成功: s3://{self.bucket_name}/{remote_path}")
            return True
            
        except Exception as e:
            self._logger.error(f"删除文件失败: {e}")
            return False
    
    async def file_exists(self, remote_path: str) -> bool:
        """检查S3文件是否存在"""
        if not self._connected or not self._client:
            return False
        
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.head_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )
            return True
            
        except self._ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            raise
        except Exception:
            return False
    
    async def get_file_info(self, remote_path: str) -> Optional[FileInfo]:
        """获取S3文件信息"""
        if not self._connected or not self._client:
            return None
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.head_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )
            
            return FileInfo(
                name=os.path.basename(remote_path),
                path=remote_path,
                size=response.get('ContentLength', 0),
                modified_time=response.get('LastModified'),
                content_type=response.get('ContentType'),
                metadata=response.get('Metadata', {}),
                etag=response.get('ETag', '').strip('"')
            )
            
        except Exception as e:
            self._logger.error(f"获取文件信息失败: {e}")
            return None
    
    async def list_files(
        self,
        prefix: str = "",
        max_files: int = 1000
    ) -> List[FileInfo]:
        """列出S3文件"""
        if not self._connected or not self._client:
            return []
        
        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.list_objects_v2,
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_files
            )
            
            files = []
            for obj in response.get('Contents', []):
                file_info = FileInfo(
                    name=os.path.basename(obj['Key']),
                    path=obj['Key'],
                    size=obj['Size'],
                    modified_time=obj['LastModified'],
                    etag=obj.get('ETag', '').strip('"')
                )
                files.append(file_info)
            
            return files
            
        except Exception as e:
            self._logger.error(f"列出文件失败: {e}")
            return []
    
    async def generate_presigned_url(
        self,
        remote_path: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> Optional[str]:
        """生成预签名URL"""
        if not self._connected or not self._client:
            return None
        
        try:
            if method.upper() == "GET":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._client.generate_presigned_url,
                    'get_object',
                    {'Bucket': self.bucket_name, 'Key': remote_path},
                    expiration
                )
            elif method.upper() == "PUT":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._client.generate_presigned_url,
                    'put_object',
                    {'Bucket': self.bucket_name, 'Key': remote_path},
                    expiration
                )
            else:
                self._logger.error(f"不支持的HTTP方法: {method}")
                return None
            
            return url
            
        except Exception as e:
            self._logger.error(f"生成预签名URL失败: {e}")
            return None


class AliyunOSSAdapter(StorageAdapter):
    """阿里云OSS存储适配器"""

    def __init__(
        self,
        access_key_id: str,
        access_key_secret: str,
        bucket_name: str,
        endpoint: str,
        **kwargs
    ):
        """
        初始化阿里云OSS适配器

        Args:
            access_key_id: 阿里云访问密钥ID
            access_key_secret: 阿里云访问密钥Secret
            bucket_name: OSS存储桶名称
            endpoint: OSS端点地址
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.bucket_name = bucket_name
        self.endpoint = endpoint
        self._bucket = None
        self._connected = False
        self._logger = logging_system.get_logger("aliyun_oss_adapter")

        # 检查oss2依赖
        try:
            import oss2
            self._oss2 = oss2
        except ImportError:
            raise ImportError("阿里云OSS适配器需要oss2库，请运行: pip install oss2")

    async def connect(self) -> bool:
        """连接到阿里云OSS"""
        try:
            # 创建认证对象
            auth = self._oss2.Auth(self.access_key_id, self.access_key_secret)

            # 创建Bucket对象
            self._bucket = self._oss2.Bucket(auth, self.endpoint, self.bucket_name)

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None, self._bucket.get_bucket_info
            )

            self._connected = True
            self._logger.info(f"已连接到阿里云OSS存储桶: {self.bucket_name}")
            return True

        except self._oss2.exceptions.NoSuchBucket:
            self._logger.error(f"存储桶不存在: {self.bucket_name}")
            return False
        except self._oss2.exceptions.AccessDenied:
            self._logger.error("访问被拒绝，请检查访问密钥")
            return False
        except Exception as e:
            self._logger.error(f"连接阿里云OSS失败: {e}")
            return False

    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._bucket = None
        self._logger.info("已断开阿里云OSS连接")
        return True

    async def upload_file(
        self,
        local_path: str,
        remote_path: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """上传文件到OSS"""
        if not self._connected or not self._bucket:
            return False

        try:
            # 准备上传参数
            headers = {}
            if metadata:
                for key, value in metadata.items():
                    headers[f'x-oss-meta-{key}'] = str(value)

            # 设置内容类型
            content_type, _ = mimetypes.guess_type(local_path)
            if content_type:
                headers['Content-Type'] = content_type

            # 执行上传
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.put_object_from_file,
                remote_path,
                local_path,
                headers
            )

            self._logger.info(f"文件上传成功: {local_path} -> oss://{self.bucket_name}/{remote_path}")
            return True

        except Exception as e:
            self._logger.error(f"上传文件失败: {e}")
            return False

    async def download_file(
        self,
        remote_path: str,
        local_path: str
    ) -> bool:
        """从OSS下载文件"""
        if not self._connected or not self._bucket:
            return False

        try:
            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 执行下载
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.get_object_to_file,
                remote_path,
                local_path
            )

            self._logger.info(f"文件下载成功: oss://{self.bucket_name}/{remote_path} -> {local_path}")
            return True

        except Exception as e:
            self._logger.error(f"下载文件失败: {e}")
            return False

    async def delete_file(self, remote_path: str) -> bool:
        """删除OSS文件"""
        if not self._connected or not self._bucket:
            return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.delete_object,
                remote_path
            )

            self._logger.info(f"文件删除成功: oss://{self.bucket_name}/{remote_path}")
            return True

        except Exception as e:
            self._logger.error(f"删除文件失败: {e}")
            return False

    async def file_exists(self, remote_path: str) -> bool:
        """检查OSS文件是否存在"""
        if not self._connected or not self._bucket:
            return False

        try:
            return await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.object_exists,
                remote_path
            )

        except Exception:
            return False

    async def get_file_info(self, remote_path: str) -> Optional[FileInfo]:
        """获取OSS文件信息"""
        if not self._connected or not self._bucket:
            return None

        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.head_object,
                remote_path
            )

            # 提取元数据
            metadata = {}
            for key, value in result.headers.items():
                if key.startswith('x-oss-meta-'):
                    metadata[key[11:]] = value  # 移除 'x-oss-meta-' 前缀

            return FileInfo(
                name=os.path.basename(remote_path),
                path=remote_path,
                size=result.content_length,
                modified_time=result.last_modified,
                content_type=result.content_type,
                metadata=metadata,
                etag=result.etag.strip('"')
            )

        except Exception as e:
            self._logger.error(f"获取文件信息失败: {e}")
            return None

    async def list_files(
        self,
        prefix: str = "",
        max_files: int = 1000
    ) -> List[FileInfo]:
        """列出OSS文件"""
        if not self._connected or not self._bucket:
            return []

        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                self._bucket.list_objects,
                prefix,
                max_keys=max_files
            )

            files = []
            for obj in result.object_list:
                file_info = FileInfo(
                    name=os.path.basename(obj.key),
                    path=obj.key,
                    size=obj.size,
                    modified_time=obj.last_modified,
                    etag=obj.etag.strip('"')
                )
                files.append(file_info)

            return files

        except Exception as e:
            self._logger.error(f"列出文件失败: {e}")
            return []

    async def generate_presigned_url(
        self,
        remote_path: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> Optional[str]:
        """生成预签名URL"""
        if not self._connected or not self._bucket:
            return None

        try:
            if method.upper() == "GET":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._bucket.sign_url,
                    'GET',
                    remote_path,
                    expiration
                )
            elif method.upper() == "PUT":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._bucket.sign_url,
                    'PUT',
                    remote_path,
                    expiration
                )
            else:
                self._logger.error(f"不支持的HTTP方法: {method}")
                return None

            return url

        except Exception as e:
            self._logger.error(f"生成预签名URL失败: {e}")
            return None


class TencentCOSAdapter(StorageAdapter):
    """腾讯云COS存储适配器"""

    def __init__(
        self,
        secret_id: str,
        secret_key: str,
        bucket_name: str,
        region: str,
        **kwargs
    ):
        """
        初始化腾讯云COS适配器

        Args:
            secret_id: 腾讯云SecretId
            secret_key: 腾讯云SecretKey
            bucket_name: COS存储桶名称
            region: COS区域
        """
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.bucket_name = bucket_name
        self.region = region
        self._client = None
        self._connected = False
        self._logger = logging_system.get_logger("tencent_cos_adapter")

        # 检查cos-python-sdk-v5依赖
        try:
            from qcloud_cos import CosConfig, CosS3Client
            from qcloud_cos.cos_exception import CosServiceError, CosClientError
            self._CosConfig = CosConfig
            self._CosS3Client = CosS3Client
            self._CosServiceError = CosServiceError
            self._CosClientError = CosClientError
        except ImportError:
            raise ImportError("腾讯云COS适配器需要cos-python-sdk-v5库，请运行: pip install cos-python-sdk-v5")

    async def connect(self) -> bool:
        """连接到腾讯云COS"""
        try:
            # 创建COS配置
            config = self._CosConfig(
                Region=self.region,
                SecretId=self.secret_id,
                SecretKey=self.secret_key
            )

            # 创建COS客户端
            self._client = self._CosS3Client(config)

            # 测试连接
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.head_bucket,
                Bucket=self.bucket_name
            )

            self._connected = True
            self._logger.info(f"已连接到腾讯云COS存储桶: {self.bucket_name}")
            return True

        except self._CosServiceError as e:
            if e.get_error_code() == 'NoSuchBucket':
                self._logger.error(f"存储桶不存在: {self.bucket_name}")
            else:
                self._logger.error(f"连接腾讯云COS失败: {e}")
            return False
        except Exception as e:
            self._logger.error(f"连接腾讯云COS时发生未知错误: {e}")
            return False

    async def disconnect(self) -> bool:
        """断开连接"""
        self._connected = False
        self._client = None
        self._logger.info("已断开腾讯云COS连接")
        return True

    async def upload_file(
        self,
        local_path: str,
        remote_path: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """上传文件到COS"""
        if not self._connected or not self._client:
            return False

        try:
            # 准备上传参数
            kwargs = {
                'Bucket': self.bucket_name,
                'Key': remote_path,
                'Body': open(local_path, 'rb')
            }

            # 添加元数据
            if metadata:
                kwargs['Metadata'] = {k: str(v) for k, v in metadata.items()}

            # 设置内容类型
            content_type, _ = mimetypes.guess_type(local_path)
            if content_type:
                kwargs['ContentType'] = content_type

            # 执行上传
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.put_object,
                **kwargs
            )

            self._logger.info(f"文件上传成功: {local_path} -> cos://{self.bucket_name}/{remote_path}")
            return True

        except Exception as e:
            self._logger.error(f"上传文件失败: {e}")
            return False
        finally:
            # 确保文件句柄被关闭
            if 'Body' in locals() and hasattr(kwargs.get('Body'), 'close'):
                kwargs['Body'].close()

    async def download_file(
        self,
        remote_path: str,
        local_path: str
    ) -> bool:
        """从COS下载文件"""
        if not self._connected or not self._client:
            return False

        try:
            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 执行下载
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.get_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )

            # 写入本地文件
            with open(local_path, 'wb') as f:
                for chunk in response['Body'].iter_content(chunk_size=8192):
                    f.write(chunk)

            self._logger.info(f"文件下载成功: cos://{self.bucket_name}/{remote_path} -> {local_path}")
            return True

        except Exception as e:
            self._logger.error(f"下载文件失败: {e}")
            return False

    async def delete_file(self, remote_path: str) -> bool:
        """删除COS文件"""
        if not self._connected or not self._client:
            return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.delete_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )

            self._logger.info(f"文件删除成功: cos://{self.bucket_name}/{remote_path}")
            return True

        except Exception as e:
            self._logger.error(f"删除文件失败: {e}")
            return False

    async def file_exists(self, remote_path: str) -> bool:
        """检查COS文件是否存在"""
        if not self._connected or not self._client:
            return False

        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.head_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )
            return True

        except self._CosServiceError as e:
            if e.get_error_code() == 'NoSuchKey':
                return False
            raise
        except Exception:
            return False

    async def get_file_info(self, remote_path: str) -> Optional[FileInfo]:
        """获取COS文件信息"""
        if not self._connected or not self._client:
            return None

        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.head_object,
                Bucket=self.bucket_name,
                Key=remote_path
            )

            return FileInfo(
                name=os.path.basename(remote_path),
                path=remote_path,
                size=int(response.get('Content-Length', 0)),
                modified_time=response.get('Last-Modified'),
                content_type=response.get('Content-Type'),
                metadata=response.get('Metadata', {}),
                etag=response.get('ETag', '').strip('"')
            )

        except Exception as e:
            self._logger.error(f"获取文件信息失败: {e}")
            return None

    async def list_files(
        self,
        prefix: str = "",
        max_files: int = 1000
    ) -> List[FileInfo]:
        """列出COS文件"""
        if not self._connected or not self._client:
            return []

        try:
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                self._client.list_objects,
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_files
            )

            files = []
            for obj in response.get('Contents', []):
                file_info = FileInfo(
                    name=os.path.basename(obj['Key']),
                    path=obj['Key'],
                    size=obj['Size'],
                    modified_time=obj['LastModified'],
                    etag=obj.get('ETag', '').strip('"')
                )
                files.append(file_info)

            return files

        except Exception as e:
            self._logger.error(f"列出文件失败: {e}")
            return []

    async def generate_presigned_url(
        self,
        remote_path: str,
        expiration: int = 3600,
        method: str = "GET"
    ) -> Optional[str]:
        """生成预签名URL"""
        if not self._connected or not self._client:
            return None

        try:
            if method.upper() == "GET":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._client.get_presigned_url,
                    'get_object',
                    {'Bucket': self.bucket_name, 'Key': remote_path},
                    expiration
                )
            elif method.upper() == "PUT":
                url = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._client.get_presigned_url,
                    'put_object',
                    {'Bucket': self.bucket_name, 'Key': remote_path},
                    expiration
                )
            else:
                self._logger.error(f"不支持的HTTP方法: {method}")
                return None

            return url

        except Exception as e:
            self._logger.error(f"生成预签名URL失败: {e}")
            return None
