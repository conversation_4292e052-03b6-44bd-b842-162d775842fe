"""
Redis适配器实现

为消息队列和缓存工具提供Redis后端支持，包括集群和哨兵模式。
"""

import asyncio
import json
import pickle
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

try:
    import redis.asyncio as redis
    from redis.asyncio import Redis, RedisCluster
    from redis.asyncio.sentinel import Sentinel
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

from ai_agent_framework.tools.message_queue_tool import MessageQueueAdapter, QueueMessage, MessagePriority
from ai_agent_framework.tools.cache_tool import CacheAdapter, CacheEntry
from ai_agent_framework.utils.logging_system import logging_system


class RedisQueueAdapter(MessageQueueAdapter):
    """Redis消息队列适配器"""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        cluster_mode: bool = False,
        sentinel_hosts: Optional[List[tuple]] = None,
        sentinel_service: str = "mymaster",
        **kwargs
    ):
        """
        初始化Redis队列适配器
        
        Args:
            host: Redis主机地址
            port: Redis端口
            db: 数据库编号
            password: 密码
            cluster_mode: 是否使用集群模式
            sentinel_hosts: 哨兵主机列表 [(host, port), ...]
            sentinel_service: 哨兵服务名
            **kwargs: 其他Redis连接参数
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis库未安装，请运行: pip install redis")
        
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.cluster_mode = cluster_mode
        self.sentinel_hosts = sentinel_hosts
        self.sentinel_service = sentinel_service
        self.connection_kwargs = kwargs
        
        self._redis: Optional[Union[Redis, RedisCluster]] = None
        self._connected = False
        self._logger = logging_system.get_logger("redis_queue")
    
    async def connect(self) -> bool:
        """连接到Redis"""
        try:
            if self.sentinel_hosts:
                # 哨兵模式
                sentinel = Sentinel(self.sentinel_hosts, **self.connection_kwargs)
                self._redis = sentinel.master_for(
                    self.sentinel_service,
                    password=self.password,
                    db=self.db
                )
            elif self.cluster_mode:
                # 集群模式
                startup_nodes = [{"host": self.host, "port": self.port}]
                self._redis = RedisCluster(
                    startup_nodes=startup_nodes,
                    password=self.password,
                    **self.connection_kwargs
                )
            else:
                # 单机模式
                self._redis = Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    **self.connection_kwargs
                )
            
            # 测试连接
            await self._redis.ping()
            self._connected = True
            self._logger.info(f"已连接到Redis: {self.host}:{self.port}")
            return True
            
        except Exception as e:
            self._logger.error(f"连接Redis失败: {str(e)}")
            return False
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self._redis:
                await self._redis.close()
            self._connected = False
            self._logger.info("已断开Redis连接")
            return True
        except Exception as e:
            self._logger.error(f"断开Redis连接失败: {str(e)}")
            return False
    
    def _get_queue_key(self, queue_name: str) -> str:
        """获取队列键名"""
        return f"queue:{queue_name}"
    
    def _get_priority_queue_key(self, queue_name: str, priority: MessagePriority) -> str:
        """获取优先级队列键名"""
        return f"queue:{queue_name}:priority:{priority.value}"
    
    def _serialize_message(self, message: QueueMessage) -> str:
        """序列化消息"""
        return json.dumps(message.to_dict(), ensure_ascii=False)
    
    def _deserialize_message(self, data: str) -> QueueMessage:
        """反序列化消息"""
        message_dict = json.loads(data)
        return QueueMessage.from_dict(message_dict)
    
    async def send_message(self, queue_name: str, message: QueueMessage) -> bool:
        """发送消息到队列"""
        if not self._connected or not self._redis:
            return False
        
        try:
            # 处理延迟发送
            if message.delay_seconds > 0:
                # 使用Redis的延迟队列实现
                delay_key = f"delay:{queue_name}"
                score = time.time() + message.delay_seconds
                serialized = self._serialize_message(message)
                await self._redis.zadd(delay_key, {serialized: score})
            else:
                # 立即发送到优先级队列
                priority_key = self._get_priority_queue_key(queue_name, message.priority)
                serialized = self._serialize_message(message)
                await self._redis.lpush(priority_key, serialized)
            
            # 设置TTL
            if message.ttl_seconds:
                queue_key = self._get_queue_key(queue_name)
                await self._redis.expire(queue_key, message.ttl_seconds)
            
            self._logger.debug(f"消息已发送到Redis队列 {queue_name}: {message.message_id}")
            return True
            
        except Exception as e:
            self._logger.error(f"发送消息到Redis失败: {str(e)}")
            return False
    
    async def receive_message(
        self,
        queue_name: str,
        timeout_seconds: Optional[int] = None,
    ) -> Optional[QueueMessage]:
        """从队列接收消息"""
        if not self._connected or not self._redis:
            return None
        
        try:
            # 首先处理延迟消息
            await self._process_delayed_messages(queue_name)
            
            # 按优先级顺序检查队列
            priorities = [MessagePriority.URGENT, MessagePriority.HIGH, MessagePriority.NORMAL, MessagePriority.LOW]
            
            for priority in priorities:
                priority_key = self._get_priority_queue_key(queue_name, priority)
                
                if timeout_seconds:
                    # 阻塞接收
                    result = await self._redis.brpop(priority_key, timeout=timeout_seconds)
                    if result:
                        _, data = result
                        message = self._deserialize_message(data.decode('utf-8'))
                        message.attempts += 1
                        self._logger.debug(f"从Redis队列 {queue_name} 接收消息: {message.message_id}")
                        return message
                else:
                    # 非阻塞接收
                    data = await self._redis.rpop(priority_key)
                    if data:
                        message = self._deserialize_message(data.decode('utf-8'))
                        message.attempts += 1
                        self._logger.debug(f"从Redis队列 {queue_name} 接收消息: {message.message_id}")
                        return message
            
            return None
            
        except Exception as e:
            self._logger.error(f"从Redis接收消息失败: {str(e)}")
            return None
    
    async def _process_delayed_messages(self, queue_name: str) -> None:
        """处理延迟消息"""
        try:
            delay_key = f"delay:{queue_name}"
            current_time = time.time()
            
            # 获取到期的延迟消息
            messages = await self._redis.zrangebyscore(
                delay_key, 0, current_time, withscores=True
            )
            
            for data, score in messages:
                # 移动到正常队列
                message = self._deserialize_message(data.decode('utf-8'))
                priority_key = self._get_priority_queue_key(queue_name, message.priority)
                
                # 使用事务确保原子性
                async with self._redis.pipeline() as pipe:
                    await pipe.zrem(delay_key, data)
                    await pipe.lpush(priority_key, data)
                    await pipe.execute()
                    
        except Exception as e:
            self._logger.error(f"处理延迟消息失败: {str(e)}")
    
    async def create_queue(
        self,
        queue_name: str,
        durable: bool = True,
        max_size: Optional[int] = None,
    ) -> bool:
        """创建队列"""
        if not self._connected or not self._redis:
            return False
        
        try:
            # Redis中队列是动态创建的，这里主要是设置元数据
            queue_meta_key = f"queue_meta:{queue_name}"
            metadata = {
                "name": queue_name,
                "durable": durable,
                "max_size": max_size or -1,
                "created_at": datetime.now().isoformat(),
            }
            
            await self._redis.hset(queue_meta_key, mapping=metadata)
            self._logger.info(f"已创建Redis队列: {queue_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"创建Redis队列失败: {str(e)}")
            return False
    
    async def delete_queue(self, queue_name: str) -> bool:
        """删除队列"""
        if not self._connected or not self._redis:
            return False
        
        try:
            # 删除所有相关的键
            keys_to_delete = []
            
            # 优先级队列键
            for priority in MessagePriority:
                priority_key = self._get_priority_queue_key(queue_name, priority)
                keys_to_delete.append(priority_key)
            
            # 延迟队列键
            delay_key = f"delay:{queue_name}"
            keys_to_delete.append(delay_key)
            
            # 元数据键
            queue_meta_key = f"queue_meta:{queue_name}"
            keys_to_delete.append(queue_meta_key)
            
            # 批量删除
            if keys_to_delete:
                await self._redis.delete(*keys_to_delete)
            
            self._logger.info(f"已删除Redis队列: {queue_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"删除Redis队列失败: {str(e)}")
            return False
    
    async def get_queue_info(self, queue_name: str) -> Dict[str, Any]:
        """获取队列信息"""
        if not self._connected or not self._redis:
            return {}
        
        try:
            # 获取元数据
            queue_meta_key = f"queue_meta:{queue_name}"
            metadata = await self._redis.hgetall(queue_meta_key)
            
            if not metadata:
                return {}
            
            # 计算队列大小
            total_size = 0
            priority_sizes = {}
            
            for priority in MessagePriority:
                priority_key = self._get_priority_queue_key(queue_name, priority)
                size = await self._redis.llen(priority_key)
                priority_sizes[priority.name.lower()] = size
                total_size += size
            
            # 延迟消息数量
            delay_key = f"delay:{queue_name}"
            delayed_count = await self._redis.zcard(delay_key)
            
            return {
                "name": queue_name,
                "type": "redis",
                "size": total_size,
                "delayed_count": delayed_count,
                "priority_sizes": priority_sizes,
                "durable": metadata.get(b"durable", b"true").decode() == "true",
                "max_size": int(metadata.get(b"max_size", b"-1")),
                "created_at": metadata.get(b"created_at", b"").decode(),
            }
            
        except Exception as e:
            self._logger.error(f"获取Redis队列信息失败: {str(e)}")
            return {}
    
    async def purge_queue(self, queue_name: str) -> int:
        """清空队列"""
        if not self._connected or not self._redis:
            return 0
        
        try:
            total_removed = 0
            
            # 清空所有优先级队列
            for priority in MessagePriority:
                priority_key = self._get_priority_queue_key(queue_name, priority)
                size = await self._redis.llen(priority_key)
                if size > 0:
                    await self._redis.delete(priority_key)
                    total_removed += size
            
            # 清空延迟队列
            delay_key = f"delay:{queue_name}"
            delayed_count = await self._redis.zcard(delay_key)
            if delayed_count > 0:
                await self._redis.delete(delay_key)
                total_removed += delayed_count
            
            self._logger.info(f"已清空Redis队列 {queue_name}，清除 {total_removed} 条消息")
            return total_removed
            
        except Exception as e:
            self._logger.error(f"清空Redis队列失败: {str(e)}")
            return 0


class RedisCacheAdapter(CacheAdapter):
    """Redis缓存适配器"""

    def __init__(
        self,
        host: str = "localhost",
        port: int = 6379,
        db: int = 0,
        password: Optional[str] = None,
        cluster_mode: bool = False,
        sentinel_hosts: Optional[List[tuple]] = None,
        sentinel_service: str = "mymaster",
        key_prefix: str = "cache:",
        **kwargs
    ):
        """
        初始化Redis缓存适配器

        Args:
            host: Redis主机地址
            port: Redis端口
            db: 数据库编号
            password: 密码
            cluster_mode: 是否使用集群模式
            sentinel_hosts: 哨兵主机列表
            sentinel_service: 哨兵服务名
            key_prefix: 键前缀
            **kwargs: 其他Redis连接参数
        """
        if not REDIS_AVAILABLE:
            raise ImportError("Redis库未安装，请运行: pip install redis")

        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.cluster_mode = cluster_mode
        self.sentinel_hosts = sentinel_hosts
        self.sentinel_service = sentinel_service
        self.key_prefix = key_prefix
        self.connection_kwargs = kwargs

        self._redis: Optional[Union[Redis, RedisCluster]] = None
        self._connected = False
        self._logger = logging_system.get_logger("redis_cache")

        # 统计信息
        self._hits = 0
        self._misses = 0

    async def connect(self) -> bool:
        """连接到Redis"""
        try:
            if self.sentinel_hosts:
                # 哨兵模式
                sentinel = Sentinel(self.sentinel_hosts, **self.connection_kwargs)
                self._redis = sentinel.master_for(
                    self.sentinel_service,
                    password=self.password,
                    db=self.db
                )
            elif self.cluster_mode:
                # 集群模式
                startup_nodes = [{"host": self.host, "port": self.port}]
                self._redis = RedisCluster(
                    startup_nodes=startup_nodes,
                    password=self.password,
                    **self.connection_kwargs
                )
            else:
                # 单机模式
                self._redis = Redis(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    **self.connection_kwargs
                )

            # 测试连接
            await self._redis.ping()
            self._connected = True
            self._logger.info(f"已连接到Redis缓存: {self.host}:{self.port}")
            return True

        except Exception as e:
            self._logger.error(f"连接Redis缓存失败: {str(e)}")
            return False

    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self._redis:
                await self._redis.close()
            self._connected = False
            self._logger.info("已断开Redis缓存连接")
            return True
        except Exception as e:
            self._logger.error(f"断开Redis缓存连接失败: {str(e)}")
            return False

    def _get_cache_key(self, key: str) -> str:
        """获取完整的缓存键名"""
        return f"{self.key_prefix}{key}"

    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        try:
            # 尝试JSON序列化
            return json.dumps(value, ensure_ascii=False).encode('utf-8')
        except (TypeError, ValueError):
            # 回退到pickle序列化
            return pickle.dumps(value)

    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        try:
            # 尝试JSON反序列化
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 回退到pickle反序列化
            return pickle.loads(data)

    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._connected or not self._redis:
            return None

        try:
            cache_key = self._get_cache_key(key)
            data = await self._redis.get(cache_key)

            if data is None:
                self._misses += 1
                return None

            value = self._deserialize_value(data)
            self._hits += 1

            # 更新访问时间（如果需要LRU支持）
            await self._redis.touch(cache_key)

            self._logger.debug(f"缓存命中: {key}")
            return value

        except Exception as e:
            self._logger.error(f"获取Redis缓存失败: {str(e)}")
            self._misses += 1
            return None

    async def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
        tags: Optional[List[str]] = None,
    ) -> bool:
        """设置缓存值"""
        if not self._connected or not self._redis:
            return False

        try:
            cache_key = self._get_cache_key(key)
            serialized_value = self._serialize_value(value)

            # 设置值和TTL
            if ttl_seconds:
                await self._redis.setex(cache_key, ttl_seconds, serialized_value)
            else:
                await self._redis.set(cache_key, serialized_value)

            # 处理标签
            if tags:
                await self._set_tags(key, tags)

            self._logger.debug(f"缓存已设置: {key}")
            return True

        except Exception as e:
            self._logger.error(f"设置Redis缓存失败: {str(e)}")
            return False

    async def _set_tags(self, key: str, tags: List[str]) -> None:
        """设置标签关联"""
        try:
            # 为每个标签创建一个集合，存储相关的键
            for tag in tags:
                tag_key = f"{self.key_prefix}tag:{tag}"
                await self._redis.sadd(tag_key, key)

            # 为键存储标签信息
            key_tags_key = f"{self.key_prefix}key_tags:{key}"
            await self._redis.sadd(key_tags_key, *tags)

        except Exception as e:
            self._logger.error(f"设置标签失败: {str(e)}")

    async def delete(self, key: str) -> bool:
        """删除缓存项"""
        if not self._connected or not self._redis:
            return False

        try:
            cache_key = self._get_cache_key(key)

            # 删除标签关联
            await self._remove_tags(key)

            # 删除缓存值
            result = await self._redis.delete(cache_key)

            if result > 0:
                self._logger.debug(f"缓存已删除: {key}")
                return True
            return False

        except Exception as e:
            self._logger.error(f"删除Redis缓存失败: {str(e)}")
            return False

    async def _remove_tags(self, key: str) -> None:
        """移除标签关联"""
        try:
            # 获取键的标签
            key_tags_key = f"{self.key_prefix}key_tags:{key}"
            tags = await self._redis.smembers(key_tags_key)

            # 从标签集合中移除键
            for tag in tags:
                tag_key = f"{self.key_prefix}tag:{tag.decode()}"
                await self._redis.srem(tag_key, key)

            # 删除键的标签信息
            await self._redis.delete(key_tags_key)

        except Exception as e:
            self._logger.error(f"移除标签关联失败: {str(e)}")

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self._connected or not self._redis:
            return False

        try:
            cache_key = self._get_cache_key(key)
            result = await self._redis.exists(cache_key)
            return result > 0

        except Exception as e:
            self._logger.error(f"检查Redis缓存存在性失败: {str(e)}")
            return False

    async def clear(self) -> int:
        """清空所有缓存"""
        if not self._connected or not self._redis:
            return 0

        try:
            # 获取所有匹配的键
            pattern = f"{self.key_prefix}*"
            keys = []

            if self.cluster_mode:
                # 集群模式需要特殊处理
                for node in self._redis.get_nodes():
                    node_keys = await node.keys(pattern)
                    keys.extend(node_keys)
            else:
                keys = await self._redis.keys(pattern)

            if keys:
                count = await self._redis.delete(*keys)
                self._hits = 0
                self._misses = 0
                self._logger.info(f"已清空Redis缓存，清除 {count} 个项目")
                return count

            return 0

        except Exception as e:
            self._logger.error(f"清空Redis缓存失败: {str(e)}")
            return 0

    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配模式的键列表"""
        if not self._connected or not self._redis:
            return []

        try:
            # 添加前缀到模式
            full_pattern = f"{self.key_prefix}{pattern}"
            keys = []

            if self.cluster_mode:
                # 集群模式需要特殊处理
                for node in self._redis.get_nodes():
                    node_keys = await node.keys(full_pattern)
                    keys.extend(node_keys)
            else:
                keys = await self._redis.keys(full_pattern)

            # 移除前缀
            prefix_len = len(self.key_prefix)
            return [key.decode()[prefix_len:] for key in keys if key.decode().startswith(self.key_prefix)]

        except Exception as e:
            self._logger.error(f"获取Redis缓存键列表失败: {str(e)}")
            return []

    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / total_requests if total_requests > 0 else 0

            # 获取Redis信息
            info = {}
            if self._connected and self._redis:
                try:
                    redis_info = await self._redis.info()
                    info = {
                        "redis_version": redis_info.get("redis_version", "unknown"),
                        "used_memory": redis_info.get("used_memory", 0),
                        "connected_clients": redis_info.get("connected_clients", 0),
                        "total_commands_processed": redis_info.get("total_commands_processed", 0),
                    }
                except Exception:
                    pass

            return {
                "type": "redis",
                "connected": self._connected,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": hit_rate,
                "redis_info": info,
            }

        except Exception as e:
            self._logger.error(f"获取Redis缓存统计失败: {str(e)}")
            return {
                "type": "redis",
                "connected": self._connected,
                "hits": self._hits,
                "misses": self._misses,
                "hit_rate": 0,
                "error": str(e),
            }
