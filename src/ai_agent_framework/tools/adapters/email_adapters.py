"""
邮件服务适配器

支持多种邮件服务提供商的适配器实现。
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import aiohttp

from ai_agent_framework.tools.email_tool import EmailProvider, EmailMessage
from ai_agent_framework.utils.logging_system import logging_system


class SendGridProvider(EmailProvider):
    """SendGrid邮件服务提供商"""
    
    def __init__(self, api_key: str, timeout: int = 30):
        """
        初始化SendGrid提供商
        
        Args:
            api_key: SendGrid API密钥
            timeout: 请求超时时间
        """
        self.api_key = api_key
        self.timeout = timeout
        self.base_url = "https://api.sendgrid.com/v3"
        self._logger = logging_system.get_logger("sendgrid_provider")
    
    async def send_email(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """通过SendGrid API发送邮件"""
        try:
            # 构建SendGrid API请求数据
            data = {
                "personalizations": [
                    {
                        "to": [{"email": email} for email in message.to],
                        "subject": message.subject
                    }
                ],
                "from": {
                    "email": sender,
                    "name": sender_name or sender
                },
                "content": [
                    {
                        "type": "text/html" if message.is_html else "text/plain",
                        "value": message.body
                    }
                ]
            }
            
            # 添加抄送和密送
            if message.cc:
                data["personalizations"][0]["cc"] = [{"email": email} for email in message.cc]
            if message.bcc:
                data["personalizations"][0]["bcc"] = [{"email": email} for email in message.bcc]
            
            # 添加回复地址
            if message.reply_to:
                data["reply_to"] = {"email": message.reply_to}
            
            # 添加附件
            if message.attachments:
                data["attachments"] = []
                for attachment in message.attachments:
                    import base64
                    if isinstance(attachment.content, str):
                        content_b64 = base64.b64encode(attachment.content.encode()).decode()
                    else:
                        content_b64 = base64.b64encode(attachment.content).decode()
                    
                    data["attachments"].append({
                        "content": content_b64,
                        "filename": attachment.filename,
                        "type": attachment.content_type,
                        "disposition": "attachment"
                    })
            
            # 发送请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    f"{self.base_url}/mail/send",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 202:
                        self._logger.info(f"SendGrid邮件发送成功: {message.subject}")
                        return True
                    else:
                        error_text = await response.text()
                        self._logger.error(f"SendGrid邮件发送失败: {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            self._logger.error(f"SendGrid发送邮件失败: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """测试SendGrid API连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(f"{self.base_url}/user/profile", headers=headers) as response:
                    return response.status == 200
        
        except Exception as e:
            self._logger.error(f"SendGrid连接测试失败: {e}")
            return False


class AmazonSESProvider(EmailProvider):
    """Amazon SES邮件服务提供商"""
    
    def __init__(
        self,
        access_key_id: str,
        secret_access_key: str,
        region: str = "us-east-1",
        timeout: int = 30
    ):
        """
        初始化Amazon SES提供商
        
        Args:
            access_key_id: AWS访问密钥ID
            secret_access_key: AWS秘密访问密钥
            region: AWS区域
            timeout: 请求超时时间
        """
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.region = region
        self.timeout = timeout
        self._logger = logging_system.get_logger("amazon_ses_provider")
        
        # 检查boto3依赖
        try:
            import boto3
            self._boto3 = boto3
        except ImportError:
            raise ImportError("Amazon SES适配器需要boto3库，请运行: pip install boto3")
    
    async def send_email(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """通过Amazon SES发送邮件"""
        try:
            # 在线程池中执行boto3操作
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._send_email_sync, message, sender, sender_name)
        
        except Exception as e:
            self._logger.error(f"Amazon SES发送邮件失败: {e}")
            return False
    
    def _send_email_sync(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """同步发送邮件"""
        try:
            # 创建SES客户端
            ses_client = self._boto3.client(
                'ses',
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            
            # 构建发件人
            source = f"{sender_name} <{sender}>" if sender_name else sender
            
            # 构建目标地址
            destination = {
                'ToAddresses': message.to
            }
            if message.cc:
                destination['CcAddresses'] = message.cc
            if message.bcc:
                destination['BccAddresses'] = message.bcc
            
            # 构建邮件内容
            email_message = {
                'Subject': {
                    'Data': message.subject,
                    'Charset': 'UTF-8'
                },
                'Body': {}
            }
            
            if message.is_html:
                email_message['Body']['Html'] = {
                    'Data': message.body,
                    'Charset': 'UTF-8'
                }
            else:
                email_message['Body']['Text'] = {
                    'Data': message.body,
                    'Charset': 'UTF-8'
                }
            
            # 发送邮件
            response = ses_client.send_email(
                Source=source,
                Destination=destination,
                Message=email_message,
                ReplyToAddresses=[message.reply_to] if message.reply_to else []
            )
            
            message_id = response.get('MessageId')
            self._logger.info(f"Amazon SES邮件发送成功: {message_id}")
            return True
        
        except Exception as e:
            self._logger.error(f"Amazon SES同步发送失败: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """测试Amazon SES连接"""
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._test_connection_sync)
        
        except Exception as e:
            self._logger.error(f"Amazon SES连接测试失败: {e}")
            return False
    
    def _test_connection_sync(self) -> bool:
        """同步测试连接"""
        try:
            ses_client = self._boto3.client(
                'ses',
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            
            # 获取发送配额来测试连接
            ses_client.get_send_quota()
            return True
        
        except Exception as e:
            self._logger.error(f"Amazon SES连接测试失败: {e}")
            return False


class MailgunProvider(EmailProvider):
    """Mailgun邮件服务提供商"""
    
    def __init__(
        self,
        api_key: str,
        domain: str,
        base_url: str = "https://api.mailgun.net/v3",
        timeout: int = 30
    ):
        """
        初始化Mailgun提供商
        
        Args:
            api_key: Mailgun API密钥
            domain: Mailgun域名
            base_url: API基础URL
            timeout: 请求超时时间
        """
        self.api_key = api_key
        self.domain = domain
        self.base_url = base_url
        self.timeout = timeout
        self._logger = logging_system.get_logger("mailgun_provider")
    
    async def send_email(self, message: EmailMessage, sender: str, sender_name: Optional[str] = None) -> bool:
        """通过Mailgun API发送邮件"""
        try:
            # 构建请求数据
            data = {
                "from": f"{sender_name} <{sender}>" if sender_name else sender,
                "to": message.to,
                "subject": message.subject,
                "text" if not message.is_html else "html": message.body
            }
            
            # 添加抄送和密送
            if message.cc:
                data["cc"] = message.cc
            if message.bcc:
                data["bcc"] = message.bcc
            
            # 添加回复地址
            if message.reply_to:
                data["h:Reply-To"] = message.reply_to
            
            # 设置优先级
            if message.priority == "high":
                data["o:priority"] = "high"
            elif message.priority == "low":
                data["o:priority"] = "low"
            
            # 准备文件上传（附件）
            files = []
            if message.attachments:
                for attachment in message.attachments:
                    if isinstance(attachment.content, str):
                        content = attachment.content.encode()
                    else:
                        content = attachment.content
                    
                    files.append(
                        ("attachment", (attachment.filename, content, attachment.content_type))
                    )
            
            # 发送请求
            auth = aiohttp.BasicAuth("api", self.api_key)
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                if files:
                    # 使用multipart/form-data上传附件
                    form_data = aiohttp.FormData()
                    for key, value in data.items():
                        if isinstance(value, list):
                            for item in value:
                                form_data.add_field(key, item)
                        else:
                            form_data.add_field(key, value)
                    
                    for field_name, (filename, content, content_type) in files:
                        form_data.add_field(field_name, content, filename=filename, content_type=content_type)
                    
                    async with session.post(
                        f"{self.base_url}/{self.domain}/messages",
                        auth=auth,
                        data=form_data
                    ) as response:
                        success = response.status == 200
                else:
                    # 普通表单数据
                    async with session.post(
                        f"{self.base_url}/{self.domain}/messages",
                        auth=auth,
                        data=data
                    ) as response:
                        success = response.status == 200
                
                if success:
                    self._logger.info(f"Mailgun邮件发送成功: {message.subject}")
                    return True
                else:
                    error_text = await response.text()
                    self._logger.error(f"Mailgun邮件发送失败: {response.status} - {error_text}")
                    return False
        
        except Exception as e:
            self._logger.error(f"Mailgun发送邮件失败: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """测试Mailgun API连接"""
        try:
            auth = aiohttp.BasicAuth("api", self.api_key)
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.get(f"{self.base_url}/{self.domain}", auth=auth) as response:
                    return response.status == 200
        
        except Exception as e:
            self._logger.error(f"Mailgun连接测试失败: {e}")
            return False
