"""
企业级消息队列适配器实现

为消息队列工具提供RabbitMQ和Kafka等企业级消息队列后端支持。
支持高可用、持久化、事务等企业级特性。
"""

import asyncio
import json
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Callable, Union
from urllib.parse import urlparse

from ai_agent_framework.tools.message_queue_tool import QueueAdapter, Message
from ai_agent_framework.utils.logging_system import logging_system


class RabbitMQAdapter(QueueAdapter):
    """RabbitMQ消息队列适配器"""
    
    def __init__(
        self,
        connection_url: str = "amqp://guest:guest@localhost:5672/",
        exchange_name: str = "ai_agent_exchange",
        exchange_type: str = "direct",
        durable: bool = True,
        auto_delete: bool = False,
        **kwargs
    ):
        """
        初始化RabbitMQ适配器
        
        Args:
            connection_url: RabbitMQ连接URL
            exchange_name: 交换机名称
            exchange_type: 交换机类型 (direct, fanout, topic, headers)
            durable: 是否持久化
            auto_delete: 是否自动删除
        """
        self.connection_url = connection_url
        self.exchange_name = exchange_name
        self.exchange_type = exchange_type
        self.durable = durable
        self.auto_delete = auto_delete
        self._connection = None
        self._channel = None
        self._exchange = None
        self._connected = False
        self._consumers = {}  # 存储消费者回调
        self._logger = logging_system.get_logger("rabbitmq_adapter")
        
        # 检查aio-pika依赖
        try:
            import aio_pika
            from aio_pika import ExchangeType, DeliveryMode, Message as AioPikaMessage
            self._aio_pika = aio_pika
            self._ExchangeType = ExchangeType
            self._DeliveryMode = DeliveryMode
            self._AioPikaMessage = AioPikaMessage
        except ImportError:
            raise ImportError("RabbitMQ适配器需要aio-pika库，请运行: pip install aio-pika")
    
    async def connect(self) -> bool:
        """连接到RabbitMQ"""
        try:
            # 建立连接
            self._connection = await self._aio_pika.connect_robust(self.connection_url)
            
            # 创建通道
            self._channel = await self._connection.channel()
            
            # 设置QoS（公平分发）
            await self._channel.set_qos(prefetch_count=1)
            
            # 声明交换机
            exchange_type_map = {
                "direct": self._ExchangeType.DIRECT,
                "fanout": self._ExchangeType.FANOUT,
                "topic": self._ExchangeType.TOPIC,
                "headers": self._ExchangeType.HEADERS
            }
            
            self._exchange = await self._channel.declare_exchange(
                self.exchange_name,
                exchange_type_map.get(self.exchange_type, self._ExchangeType.DIRECT),
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            self._connected = True
            self._logger.info(f"已连接到RabbitMQ: {self.connection_url}")
            return True
            
        except Exception as e:
            self._logger.error(f"连接RabbitMQ失败: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            if self._connection and not self._connection.is_closed:
                await self._connection.close()
            
            self._connected = False
            self._connection = None
            self._channel = None
            self._exchange = None
            self._consumers.clear()
            
            self._logger.info("已断开RabbitMQ连接")
            return True
            
        except Exception as e:
            self._logger.error(f"断开RabbitMQ连接失败: {e}")
            return False
    
    async def send_message(
        self,
        queue_name: str,
        message: Message,
        routing_key: Optional[str] = None,
        priority: int = 0,
        delay: int = 0
    ) -> bool:
        """发送消息到队列"""
        if not self._connected or not self._exchange:
            return False
        
        try:
            # 声明队列
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            # 绑定队列到交换机
            routing_key = routing_key or queue_name
            await queue.bind(self._exchange, routing_key)
            
            # 准备消息
            message_body = json.dumps({
                "id": message.id,
                "content": message.content,
                "metadata": message.metadata,
                "timestamp": message.timestamp.isoformat() if message.timestamp else None,
                "priority": priority
            }).encode('utf-8')
            
            # 设置消息属性
            properties = {
                "delivery_mode": self._DeliveryMode.PERSISTENT if self.durable else self._DeliveryMode.NOT_PERSISTENT,
                "priority": priority,
                "message_id": message.id,
                "timestamp": message.timestamp,
                "headers": message.metadata or {}
            }
            
            # 处理延迟消息（需要RabbitMQ延迟插件）
            if delay > 0:
                properties["headers"]["x-delay"] = delay * 1000  # 转换为毫秒
            
            aio_pika_message = self._AioPikaMessage(
                body=message_body,
                **properties
            )
            
            # 发送消息
            await self._exchange.publish(aio_pika_message, routing_key=routing_key)
            
            self._logger.debug(f"消息已发送到队列 {queue_name}: {message.id}")
            return True
            
        except Exception as e:
            self._logger.error(f"发送消息失败: {e}")
            return False
    
    async def receive_message(
        self,
        queue_name: str,
        timeout: Optional[int] = None
    ) -> Optional[Message]:
        """从队列接收单个消息"""
        if not self._connected or not self._channel:
            return None
        
        try:
            # 声明队列
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            # 获取消息
            incoming_message = await queue.get(timeout=timeout or 30)
            if not incoming_message:
                return None
            
            # 解析消息
            try:
                message_data = json.loads(incoming_message.body.decode('utf-8'))
                message = Message(
                    id=message_data.get("id"),
                    content=message_data.get("content"),
                    metadata=message_data.get("metadata", {}),
                    timestamp=datetime.fromisoformat(message_data["timestamp"]) if message_data.get("timestamp") else None
                )
                
                # 确认消息
                await incoming_message.ack()
                
                self._logger.debug(f"从队列 {queue_name} 接收到消息: {message.id}")
                return message
                
            except (json.JSONDecodeError, KeyError) as e:
                self._logger.error(f"解析消息失败: {e}")
                await incoming_message.nack(requeue=False)  # 拒绝并丢弃无效消息
                return None
            
        except Exception as e:
            self._logger.error(f"接收消息失败: {e}")
            return None
    
    async def subscribe(
        self,
        queue_name: str,
        callback: Callable[[Message], None],
        routing_key: Optional[str] = None
    ) -> bool:
        """订阅队列消息"""
        if not self._connected or not self._channel:
            return False
        
        try:
            # 声明队列
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            # 绑定队列到交换机
            routing_key = routing_key or queue_name
            await queue.bind(self._exchange, routing_key)
            
            # 定义消息处理函数
            async def message_handler(incoming_message):
                try:
                    # 解析消息
                    message_data = json.loads(incoming_message.body.decode('utf-8'))
                    message = Message(
                        id=message_data.get("id"),
                        content=message_data.get("content"),
                        metadata=message_data.get("metadata", {}),
                        timestamp=datetime.fromisoformat(message_data["timestamp"]) if message_data.get("timestamp") else None
                    )
                    
                    # 调用回调函数
                    if asyncio.iscoroutinefunction(callback):
                        await callback(message)
                    else:
                        callback(message)
                    
                    # 确认消息
                    await incoming_message.ack()
                    
                    self._logger.debug(f"处理队列 {queue_name} 消息: {message.id}")
                    
                except Exception as e:
                    self._logger.error(f"处理消息失败: {e}")
                    await incoming_message.nack(requeue=True)  # 重新入队
            
            # 开始消费
            consumer_tag = await queue.consume(message_handler)
            self._consumers[queue_name] = consumer_tag
            
            self._logger.info(f"已订阅队列: {queue_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"订阅队列失败: {e}")
            return False
    
    async def unsubscribe(self, queue_name: str) -> bool:
        """取消订阅队列"""
        try:
            if queue_name in self._consumers:
                consumer_tag = self._consumers[queue_name]
                await self._channel.basic_cancel(consumer_tag)
                del self._consumers[queue_name]
                
                self._logger.info(f"已取消订阅队列: {queue_name}")
                return True
            
            return False
            
        except Exception as e:
            self._logger.error(f"取消订阅失败: {e}")
            return False
    
    async def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """获取队列信息"""
        if not self._connected or not self._channel:
            return None
        
        try:
            # 声明队列（被动声明，不会创建）
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete,
                passive=True
            )
            
            # 获取队列信息
            info = {
                "name": queue_name,
                "durable": self.durable,
                "auto_delete": self.auto_delete,
                "exchange": self.exchange_name,
                "connected": self._connected
            }
            
            return info
            
        except Exception as e:
            self._logger.error(f"获取队列信息失败: {e}")
            return None
    
    async def purge_queue(self, queue_name: str) -> bool:
        """清空队列"""
        if not self._connected or not self._channel:
            return False
        
        try:
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            await queue.purge()
            
            self._logger.info(f"已清空队列: {queue_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"清空队列失败: {e}")
            return False
    
    async def delete_queue(self, queue_name: str) -> bool:
        """删除队列"""
        if not self._connected or not self._channel:
            return False
        
        try:
            queue = await self._channel.declare_queue(
                queue_name,
                durable=self.durable,
                auto_delete=self.auto_delete
            )
            
            await queue.delete()
            
            self._logger.info(f"已删除队列: {queue_name}")
            return True
            
        except Exception as e:
            self._logger.error(f"删除队列失败: {e}")
            return False


class KafkaAdapter(QueueAdapter):
    """Kafka消息队列适配器"""

    def __init__(
        self,
        bootstrap_servers: str = "localhost:9092",
        client_id: str = "ai_agent_kafka_client",
        group_id: Optional[str] = None,
        auto_offset_reset: str = "latest",
        enable_auto_commit: bool = True,
        security_protocol: str = "PLAINTEXT",
        **kwargs
    ):
        """
        初始化Kafka适配器

        Args:
            bootstrap_servers: Kafka服务器地址
            client_id: 客户端ID
            group_id: 消费者组ID
            auto_offset_reset: 自动偏移重置策略
            enable_auto_commit: 是否启用自动提交
            security_protocol: 安全协议
        """
        self.bootstrap_servers = bootstrap_servers
        self.client_id = client_id
        self.group_id = group_id or f"ai_agent_group_{uuid.uuid4().hex[:8]}"
        self.auto_offset_reset = auto_offset_reset
        self.enable_auto_commit = enable_auto_commit
        self.security_protocol = security_protocol
        self._producer = None
        self._consumer = None
        self._connected = False
        self._consumers = {}  # 存储消费者任务
        self._logger = logging_system.get_logger("kafka_adapter")

        # 检查aiokafka依赖
        try:
            from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
            from aiokafka.errors import KafkaError, KafkaTimeoutError
            self._AIOKafkaProducer = AIOKafkaProducer
            self._AIOKafkaConsumer = AIOKafkaConsumer
            self._KafkaError = KafkaError
            self._KafkaTimeoutError = KafkaTimeoutError
        except ImportError:
            raise ImportError("Kafka适配器需要aiokafka库，请运行: pip install aiokafka")

    async def connect(self) -> bool:
        """连接到Kafka"""
        try:
            # 创建生产者
            self._producer = self._AIOKafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                client_id=f"{self.client_id}_producer",
                security_protocol=self.security_protocol,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None
            )

            # 启动生产者
            await self._producer.start()

            # 创建消费者
            self._consumer = self._AIOKafkaConsumer(
                bootstrap_servers=self.bootstrap_servers,
                client_id=f"{self.client_id}_consumer",
                group_id=self.group_id,
                auto_offset_reset=self.auto_offset_reset,
                enable_auto_commit=self.enable_auto_commit,
                security_protocol=self.security_protocol,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')) if m else None,
                key_deserializer=lambda k: k.decode('utf-8') if k else None
            )

            # 启动消费者
            await self._consumer.start()

            self._connected = True
            self._logger.info(f"已连接到Kafka: {self.bootstrap_servers}")
            return True

        except Exception as e:
            self._logger.error(f"连接Kafka失败: {e}")
            return False

    async def disconnect(self) -> bool:
        """断开连接"""
        try:
            # 停止所有消费者任务
            for topic, task in self._consumers.items():
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            self._consumers.clear()

            # 停止生产者
            if self._producer:
                await self._producer.stop()
                self._producer = None

            # 停止消费者
            if self._consumer:
                await self._consumer.stop()
                self._consumer = None

            self._connected = False
            self._logger.info("已断开Kafka连接")
            return True

        except Exception as e:
            self._logger.error(f"断开Kafka连接失败: {e}")
            return False

    async def send_message(
        self,
        queue_name: str,  # 在Kafka中对应topic
        message: Message,
        routing_key: Optional[str] = None,  # 在Kafka中对应partition key
        priority: int = 0,
        delay: int = 0
    ) -> bool:
        """发送消息到主题"""
        if not self._connected or not self._producer:
            return False

        try:
            # 准备消息数据
            message_data = {
                "id": message.id,
                "content": message.content,
                "metadata": message.metadata or {},
                "timestamp": message.timestamp.isoformat() if message.timestamp else None,
                "priority": priority
            }

            # 处理延迟消息（Kafka本身不支持延迟，这里记录延迟时间）
            if delay > 0:
                message_data["metadata"]["delay"] = delay
                message_data["metadata"]["scheduled_time"] = (
                    datetime.now().timestamp() + delay
                )

            # 发送消息
            future = await self._producer.send(
                queue_name,
                value=message_data,
                key=routing_key,
                timestamp_ms=int(message.timestamp.timestamp() * 1000) if message.timestamp else None
            )

            # 等待发送完成
            record_metadata = await future

            self._logger.debug(
                f"消息已发送到主题 {queue_name} "
                f"分区 {record_metadata.partition} "
                f"偏移量 {record_metadata.offset}: {message.id}"
            )
            return True

        except self._KafkaTimeoutError:
            self._logger.error("发送消息超时")
            return False
        except self._KafkaError as e:
            self._logger.error(f"发送消息失败: {e}")
            return False
        except Exception as e:
            self._logger.error(f"发送消息时发生未知错误: {e}")
            return False

    async def receive_message(
        self,
        queue_name: str,  # 在Kafka中对应topic
        timeout: Optional[int] = None
    ) -> Optional[Message]:
        """从主题接收单个消息"""
        if not self._connected or not self._consumer:
            return None

        try:
            # 订阅主题
            self._consumer.subscribe([queue_name])

            # 获取消息
            timeout_ms = (timeout * 1000) if timeout else 30000
            msg_batch = await self._consumer.getmany(timeout_ms=timeout_ms)

            if not msg_batch:
                return None

            # 处理第一个消息
            for topic_partition, messages in msg_batch.items():
                if messages:
                    kafka_message = messages[0]
                    message_data = kafka_message.value

                    if not message_data:
                        continue

                    # 检查延迟消息
                    if "scheduled_time" in message_data.get("metadata", {}):
                        scheduled_time = message_data["metadata"]["scheduled_time"]
                        if datetime.now().timestamp() < scheduled_time:
                            # 消息还未到执行时间，跳过
                            continue

                    message = Message(
                        id=message_data.get("id"),
                        content=message_data.get("content"),
                        metadata=message_data.get("metadata", {}),
                        timestamp=datetime.fromisoformat(message_data["timestamp"]) if message_data.get("timestamp") else None
                    )

                    # 手动提交偏移量（如果禁用了自动提交）
                    if not self.enable_auto_commit:
                        await self._consumer.commit()

                    self._logger.debug(f"从主题 {queue_name} 接收到消息: {message.id}")
                    return message

            return None

        except Exception as e:
            self._logger.error(f"接收消息失败: {e}")
            return None

    async def subscribe(
        self,
        queue_name: str,  # 在Kafka中对应topic
        callback: Callable[[Message], None],
        routing_key: Optional[str] = None  # 在Kafka中不使用
    ) -> bool:
        """订阅主题消息"""
        if not self._connected or not self._consumer:
            return False

        try:
            # 订阅主题
            self._consumer.subscribe([queue_name])

            # 定义消息处理任务
            async def message_consumer():
                try:
                    async for kafka_message in self._consumer:
                        try:
                            message_data = kafka_message.value
                            if not message_data:
                                continue

                            # 检查延迟消息
                            if "scheduled_time" in message_data.get("metadata", {}):
                                scheduled_time = message_data["metadata"]["scheduled_time"]
                                if datetime.now().timestamp() < scheduled_time:
                                    # 消息还未到执行时间，跳过
                                    continue

                            message = Message(
                                id=message_data.get("id"),
                                content=message_data.get("content"),
                                metadata=message_data.get("metadata", {}),
                                timestamp=datetime.fromisoformat(message_data["timestamp"]) if message_data.get("timestamp") else None
                            )

                            # 调用回调函数
                            if asyncio.iscoroutinefunction(callback):
                                await callback(message)
                            else:
                                callback(message)

                            # 手动提交偏移量（如果禁用了自动提交）
                            if not self.enable_auto_commit:
                                await self._consumer.commit()

                            self._logger.debug(f"处理主题 {queue_name} 消息: {message.id}")

                        except Exception as e:
                            self._logger.error(f"处理消息失败: {e}")

                except asyncio.CancelledError:
                    self._logger.info(f"主题 {queue_name} 消费者任务已取消")
                except Exception as e:
                    self._logger.error(f"消费者任务异常: {e}")

            # 启动消费者任务
            task = asyncio.create_task(message_consumer())
            self._consumers[queue_name] = task

            self._logger.info(f"已订阅主题: {queue_name}")
            return True

        except Exception as e:
            self._logger.error(f"订阅主题失败: {e}")
            return False

    async def unsubscribe(self, queue_name: str) -> bool:
        """取消订阅主题"""
        try:
            if queue_name in self._consumers:
                task = self._consumers[queue_name]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

                del self._consumers[queue_name]

                # 取消订阅主题
                self._consumer.unsubscribe()

                self._logger.info(f"已取消订阅主题: {queue_name}")
                return True

            return False

        except Exception as e:
            self._logger.error(f"取消订阅失败: {e}")
            return False

    async def get_queue_info(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """获取主题信息"""
        if not self._connected or not self._consumer:
            return None

        try:
            # 获取主题分区信息
            partitions = await self._producer.partitions_for(queue_name)

            if partitions is None:
                return None

            info = {
                "name": queue_name,
                "type": "kafka_topic",
                "partitions": list(partitions),
                "partition_count": len(partitions),
                "group_id": self.group_id,
                "connected": self._connected
            }

            return info

        except Exception as e:
            self._logger.error(f"获取主题信息失败: {e}")
            return None

    async def purge_queue(self, queue_name: str) -> bool:
        """清空主题（Kafka不支持直接清空主题）"""
        self._logger.warning("Kafka不支持直接清空主题，请使用Kafka管理工具")
        return False

    async def delete_queue(self, queue_name: str) -> bool:
        """删除主题（需要管理员权限）"""
        self._logger.warning("删除Kafka主题需要管理员权限，请使用Kafka管理工具")
        return False

    async def create_topic(
        self,
        topic_name: str,
        num_partitions: int = 1,
        replication_factor: int = 1
    ) -> bool:
        """创建主题（需要管理员权限）"""
        self._logger.warning("创建Kafka主题需要管理员权限，请使用Kafka管理工具")
        return False

    async def get_consumer_lag(self, topic_name: str) -> Optional[Dict[str, int]]:
        """获取消费者滞后信息"""
        if not self._connected or not self._consumer:
            return None

        try:
            # 获取主题分区
            partitions = await self._producer.partitions_for(topic_name)
            if not partitions:
                return None

            lag_info = {}

            for partition in partitions:
                from aiokafka import TopicPartition
                tp = TopicPartition(topic_name, partition)

                # 获取当前位置
                position = await self._consumer.position(tp)

                # 获取最新偏移量
                end_offsets = await self._consumer.end_offsets([tp])
                end_offset = end_offsets.get(tp, 0)

                # 计算滞后
                lag = end_offset - position
                lag_info[f"partition_{partition}"] = lag

            return lag_info

        except Exception as e:
            self._logger.error(f"获取消费者滞后信息失败: {e}")
            return None
