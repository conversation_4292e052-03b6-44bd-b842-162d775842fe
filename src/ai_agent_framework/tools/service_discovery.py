"""
AI Agent Framework 服务发现工具

提供自动发现和配置管理功能，支持从多种来源发现服务：
- OpenAPI/Swagger规范
- 服务注册中心（Consul、Eureka等）
- Kubernetes服务发现
- 配置文件扫描
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

import aiohttp
import yaml
from pydantic import BaseModel, Field, HttpUrl

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.tools.service_registry_tool import ServiceConfig, ServiceType, AuthType
from ai_agent_framework.utils.logging_system import logging_system


class DiscoverySource(BaseModel):
    """服务发现源配置"""
    
    name: str = Field(..., description="发现源名称")
    type: str = Field(..., description="发现源类型")
    url: Optional[HttpUrl] = Field(None, description="发现源URL")
    config: Dict[str, Any] = Field(default_factory=dict, description="发现源配置")
    enabled: bool = Field(default=True, description="是否启用")


class ServiceDiscoveryTool(ToolInterface):
    """
    服务发现工具
    
    自动发现网络中的服务并生成配置文件，支持多种发现方式。
    """
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化服务发现工具
        
        Args:
            config_dir: 配置文件目录
        """
        self._logger = logging_system.get_logger("service_discovery")
        self._config_dir = Path(config_dir or "./configs/services")
        self._config_dir.mkdir(parents=True, exist_ok=True)
        
        # 发现源配置
        self._discovery_sources: List[DiscoverySource] = []
        self._session: Optional[aiohttp.ClientSession] = None
        
        # 加载发现源配置
        asyncio.create_task(self._load_discovery_sources())
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "service_discovery"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "自动发现网络服务并生成配置文件，支持OpenAPI、服务注册中心等多种发现方式"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["scan", "discover_openapi", "discover_consul", "discover_k8s", "generate_config"],
                    "description": "发现操作类型"
                },
                "target": {
                    "type": "string",
                    "description": "发现目标（URL、IP范围、命名空间等）"
                },
                "discovery_type": {
                    "type": "string",
                    "enum": ["openapi", "consul", "eureka", "kubernetes", "network_scan"],
                    "description": "发现类型"
                },
                "options": {
                    "type": "object",
                    "description": "发现选项",
                    "properties": {
                        "port_range": {
                            "type": "string",
                            "description": "端口范围（如：8000-9000）"
                        },
                        "timeout": {
                            "type": "number",
                            "description": "超时时间（秒）",
                            "default": 10
                        },
                        "include_health_check": {
                            "type": "boolean",
                            "description": "是否包含健康检查",
                            "default": true
                        },
                        "generate_examples": {
                            "type": "boolean",
                            "description": "是否生成示例参数",
                            "default": true
                        }
                    }
                },
                "output_file": {
                    "type": "string",
                    "description": "输出配置文件路径"
                }
            },
            "required": ["action"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """执行服务发现"""
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )
            
            # 确保HTTP会话已初始化
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 根据操作类型执行相应功能
            if action == "scan":
                return await self._scan_network(arguments)
            elif action == "discover_openapi":
                return await self._discover_openapi_services(arguments)
            elif action == "discover_consul":
                return await self._discover_consul_services(arguments)
            elif action == "discover_k8s":
                return await self._discover_kubernetes_services(arguments)
            elif action == "generate_config":
                return await self._generate_service_config(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )
                
        except Exception as e:
            self._logger.error(f"服务发现执行失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"执行失败: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        return "action" in arguments and arguments["action"] in [
            "scan", "discover_openapi", "discover_consul", "discover_k8s", "generate_config"
        ]
    
    async def _load_discovery_sources(self) -> None:
        """加载发现源配置"""
        try:
            discovery_config_file = self._config_dir / "discovery_sources.yaml"
            if discovery_config_file.exists():
                with open(discovery_config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                sources = config_data.get("sources", [])
                for source_data in sources:
                    source = DiscoverySource(**source_data)
                    self._discovery_sources.append(source)
                    
                self._logger.info(f"已加载 {len(self._discovery_sources)} 个发现源")
            else:
                # 创建默认配置
                await self._create_default_discovery_config()
                
        except Exception as e:
            self._logger.error(f"加载发现源配置失败: {str(e)}")
    
    async def _create_default_discovery_config(self) -> None:
        """创建默认发现源配置"""
        default_sources = [
            {
                "name": "local_openapi",
                "type": "openapi",
                "url": None,
                "config": {
                    "common_paths": [
                        "/swagger.json",
                        "/openapi.json",
                        "/api-docs",
                        "/docs/swagger.json"
                    ]
                },
                "enabled": True
            },
            {
                "name": "consul_local",
                "type": "consul",
                "url": "http://localhost:8500",
                "config": {
                    "datacenter": "dc1",
                    "health_check": True
                },
                "enabled": False
            }
        ]
        
        config_data = {"sources": default_sources}
        discovery_config_file = self._config_dir / "discovery_sources.yaml"
        
        with open(discovery_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
        
        self._logger.info("已创建默认发现源配置")
    
    async def _scan_network(self, arguments: Dict[str, Any]) -> ToolResult:
        """扫描网络中的服务"""
        try:
            target = arguments.get("target", "localhost")
            options = arguments.get("options", {})
            port_range = options.get("port_range", "8000-9000")
            timeout = options.get("timeout", 5)
            
            # 解析端口范围
            start_port, end_port = map(int, port_range.split("-"))
            
            discovered_services = []
            
            # 扫描端口范围
            for port in range(start_port, end_port + 1):
                try:
                    url = f"http://{target}:{port}"
                    
                    # 尝试连接
                    async with self._session.get(
                        url,
                        timeout=aiohttp.ClientTimeout(total=timeout)
                    ) as response:
                        if response.status < 400:
                            service_info = {
                                "url": url,
                                "port": port,
                                "status": response.status,
                                "content_type": response.headers.get("content-type", ""),
                                "server": response.headers.get("server", "")
                            }
                            
                            # 尝试发现API文档
                            api_docs = await self._discover_api_docs(url)
                            if api_docs:
                                service_info["api_docs"] = api_docs
                            
                            discovered_services.append(service_info)
                            
                except Exception:
                    continue  # 端口不可访问，继续下一个
            
            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"在 {target} 上发现 {len(discovered_services)} 个服务",
                    "target": target,
                    "port_range": port_range,
                    "discovered_services": discovered_services
                }
            )
            
        except Exception as e:
            self._logger.error(f"网络扫描失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"网络扫描失败: {str(e)}"
            )
    
    async def _discover_api_docs(self, base_url: str) -> Optional[Dict[str, Any]]:
        """发现API文档"""
        common_paths = [
            "/swagger.json",
            "/openapi.json",
            "/api-docs",
            "/docs",
            "/swagger-ui",
            "/api/swagger.json",
            "/v1/swagger.json"
        ]
        
        for path in common_paths:
            try:
                url = urljoin(base_url, path)
                async with self._session.get(
                    url,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        content_type = response.headers.get("content-type", "")
                        
                        if "application/json" in content_type:
                            try:
                                api_spec = await response.json()
                                return {
                                    "type": "openapi",
                                    "url": url,
                                    "spec": api_spec
                                }
                            except Exception:
                                continue
                        elif "text/html" in content_type:
                            return {
                                "type": "swagger_ui",
                                "url": url
                            }
                            
            except Exception:
                continue
        
        return None

    async def _discover_openapi_services(self, arguments: Dict[str, Any]) -> ToolResult:
        """发现OpenAPI服务"""
        try:
            target = arguments.get("target")
            if not target:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少目标URL"
                )

            options = arguments.get("options", {})

            # 尝试从目标URL发现OpenAPI规范
            api_docs = await self._discover_api_docs(target)

            if not api_docs or api_docs.get("type") != "openapi":
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"无法从 {target} 发现OpenAPI规范"
                )

            # 解析OpenAPI规范生成服务配置
            service_config = await self._parse_openapi_to_config(
                api_docs["spec"],
                target,
                options
            )

            # 可选：保存配置文件
            output_file = arguments.get("output_file")
            if output_file:
                await self._save_service_config(service_config, output_file)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功发现OpenAPI服务: {service_config['name']}",
                    "service_config": service_config,
                    "api_docs_url": api_docs["url"],
                    "endpoints_count": len(service_config.get("endpoints", []))
                }
            )

        except Exception as e:
            self._logger.error(f"OpenAPI服务发现失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"OpenAPI服务发现失败: {str(e)}"
            )

    async def _discover_consul_services(self, arguments: Dict[str, Any]) -> ToolResult:
        """发现Consul注册的服务"""
        try:
            target = arguments.get("target", "http://localhost:8500")
            options = arguments.get("options", {})

            # 获取Consul服务列表
            services_url = urljoin(target, "/v1/catalog/services")

            async with self._session.get(services_url) as response:
                if response.status != 200:
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"无法连接到Consul: {response.status}"
                    )

                services = await response.json()

            discovered_services = []

            # 获取每个服务的详细信息
            for service_name in services.keys():
                try:
                    service_url = urljoin(target, f"/v1/catalog/service/{service_name}")

                    async with self._session.get(service_url) as response:
                        if response.status == 200:
                            service_instances = await response.json()

                            for instance in service_instances:
                                service_info = {
                                    "name": service_name,
                                    "address": instance.get("ServiceAddress", instance.get("Address")),
                                    "port": instance.get("ServicePort"),
                                    "tags": instance.get("ServiceTags", []),
                                    "meta": instance.get("ServiceMeta", {}),
                                    "node": instance.get("Node")
                                }

                                # 构建服务URL
                                if service_info["address"] and service_info["port"]:
                                    base_url = f"http://{service_info['address']}:{service_info['port']}"
                                    service_info["base_url"] = base_url

                                    # 尝试发现API文档
                                    if options.get("include_health_check", True):
                                        api_docs = await self._discover_api_docs(base_url)
                                        if api_docs:
                                            service_info["api_docs"] = api_docs

                                discovered_services.append(service_info)

                except Exception as e:
                    self._logger.warning(f"获取服务 {service_name} 信息失败: {str(e)}")
                    continue

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"从Consul发现 {len(discovered_services)} 个服务实例",
                    "consul_url": target,
                    "discovered_services": discovered_services
                }
            )

        except Exception as e:
            self._logger.error(f"Consul服务发现失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"Consul服务发现失败: {str(e)}"
            )

    async def _discover_kubernetes_services(self, arguments: Dict[str, Any]) -> ToolResult:
        """发现Kubernetes服务"""
        try:
            # 这里需要kubernetes客户端库，暂时返回占位符实现
            return ToolResult(
                tool_call_id="",
                success=False,
                error="Kubernetes服务发现功能需要安装kubernetes客户端库"
            )

        except Exception as e:
            self._logger.error(f"Kubernetes服务发现失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"Kubernetes服务发现失败: {str(e)}"
            )

    async def _generate_service_config(self, arguments: Dict[str, Any]) -> ToolResult:
        """生成服务配置文件"""
        try:
            target = arguments.get("target")
            if not target:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少目标URL或服务信息"
                )

            options = arguments.get("options", {})
            output_file = arguments.get("output_file")

            # 尝试自动发现服务信息
            api_docs = await self._discover_api_docs(target)

            if api_docs and api_docs.get("type") == "openapi":
                # 基于OpenAPI规范生成配置
                service_config = await self._parse_openapi_to_config(
                    api_docs["spec"],
                    target,
                    options
                )
            else:
                # 生成基础配置模板
                service_config = await self._generate_basic_config(target, options)

            # 保存配置文件
            if output_file:
                config_path = await self._save_service_config(service_config, output_file)
            else:
                # 使用服务名作为文件名
                service_name = service_config.get("name", "unknown_service")
                config_path = await self._save_service_config(
                    service_config,
                    f"{service_name}.yaml"
                )

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功生成服务配置: {service_config['name']}",
                    "config_file": str(config_path),
                    "service_config": service_config
                }
            )

        except Exception as e:
            self._logger.error(f"生成服务配置失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"生成服务配置失败: {str(e)}"
            )

    async def _parse_openapi_to_config(
        self,
        api_spec: Dict[str, Any],
        base_url: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """将OpenAPI规范转换为服务配置"""
        try:
            info = api_spec.get("info", {})

            # 基础服务信息
            service_config = {
                "name": info.get("title", "").replace(" ", "_").lower() or "discovered_service",
                "description": info.get("description", "通过OpenAPI自动发现的服务"),
                "base_url": base_url,
                "service_type": ServiceType.REST_API.value,
                "version": info.get("version", "1.0.0"),
                "endpoints": [],
                "timeout": 30.0,
                "max_retries": 3,
                "headers": {
                    "Content-Type": "application/json",
                    "User-Agent": "AI-Agent-Framework/1.0"
                },
                "tags": ["auto-discovered", "openapi"],
                "metadata": {
                    "openapi_version": api_spec.get("openapi", "3.0.0"),
                    "discovery_time": "auto-generated",
                    "source": "openapi_spec"
                }
            }

            # 解析认证信息
            security_schemes = api_spec.get("components", {}).get("securitySchemes", {})
            if security_schemes:
                auth_config = self._parse_openapi_auth(security_schemes)
                if auth_config:
                    service_config["auth"] = auth_config

            # 解析服务器信息
            servers = api_spec.get("servers", [])
            if servers and servers[0].get("url"):
                service_config["base_url"] = servers[0]["url"]

            # 解析路径和操作
            paths = api_spec.get("paths", {})
            for path, path_item in paths.items():
                for method, operation in path_item.items():
                    if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']:
                        endpoint = self._parse_openapi_operation(path, method, operation, options)
                        if endpoint:
                            service_config["endpoints"].append(endpoint)

            return service_config

        except Exception as e:
            self._logger.error(f"解析OpenAPI规范失败: {str(e)}")
            raise

    def _parse_openapi_auth(self, security_schemes: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析OpenAPI认证配置"""
        # 优先选择API Key认证
        for scheme_name, scheme in security_schemes.items():
            scheme_type = scheme.get("type", "").lower()

            if scheme_type == "apikey":
                return {
                    "type": AuthType.API_KEY.value,
                    "api_key": "${API_KEY}",
                    "api_key_header": scheme.get("name", "X-API-Key")
                }
            elif scheme_type == "http":
                scheme_scheme = scheme.get("scheme", "").lower()
                if scheme_scheme == "bearer":
                    return {
                        "type": AuthType.BEARER_TOKEN.value,
                        "token": "${BEARER_TOKEN}"
                    }
                elif scheme_scheme == "basic":
                    return {
                        "type": AuthType.BASIC_AUTH.value,
                        "username": "${USERNAME}",
                        "password": "${PASSWORD}"
                    }
            elif scheme_type == "oauth2":
                flows = scheme.get("flows", {})
                if flows:
                    # 选择第一个可用的OAuth2流程
                    flow_type, flow_config = next(iter(flows.items()))
                    return {
                        "type": AuthType.OAUTH2.value,
                        "client_id": "${CLIENT_ID}",
                        "client_secret": "${CLIENT_SECRET}",
                        "token_url": flow_config.get("tokenUrl", ""),
                        "scope": " ".join(flow_config.get("scopes", {}).keys())
                    }

        return None

    def _parse_openapi_operation(
        self,
        path: str,
        method: str,
        operation: Dict[str, Any],
        options: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """解析OpenAPI操作为端点配置"""
        try:
            endpoint = {
                "name": operation.get("operationId", f"{method}_{path.replace('/', '_').replace('{', '').replace('}', '')}"),
                "path": path,
                "method": method.upper(),
                "description": operation.get("summary", operation.get("description", "")),
                "requires_auth": True,  # 默认需要认证
                "timeout": 30.0,
                "parameters": self._extract_openapi_parameters(operation),
                "response_schema": self._extract_openapi_response_schema(operation)
            }

            # 生成示例参数
            if options.get("generate_examples", True):
                endpoint["parameter_examples"] = self._generate_parameter_examples(
                    endpoint["parameters"]
                )

            return endpoint

        except Exception as e:
            self._logger.warning(f"解析操作失败 {method} {path}: {str(e)}")
            return None

    def _extract_openapi_parameters(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """从OpenAPI操作中提取参数"""
        parameters_schema = {
            "type": "object",
            "properties": {},
            "required": []
        }

        # 处理路径参数、查询参数、头部参数
        parameters = operation.get("parameters", [])
        for param in parameters:
            param_name = param.get("name")
            param_schema = param.get("schema", {})
            param_in = param.get("in", "query")

            if param_name:
                parameters_schema["properties"][param_name] = {
                    "type": param_schema.get("type", "string"),
                    "description": param.get("description", f"{param_in} parameter"),
                    "in": param_in
                }

                if param.get("required", False):
                    parameters_schema["required"].append(param_name)

        # 处理请求体
        request_body = operation.get("requestBody", {})
        if request_body:
            content = request_body.get("content", {})
            for content_type, content_schema in content.items():
                if "application/json" in content_type:
                    schema = content_schema.get("schema", {})
                    if schema.get("type") == "object":
                        properties = schema.get("properties", {})
                        for prop_name, prop_schema in properties.items():
                            parameters_schema["properties"][prop_name] = {
                                **prop_schema,
                                "in": "body"
                            }

                        required = schema.get("required", [])
                        parameters_schema["required"].extend(required)
                    break

        return parameters_schema

    def _extract_openapi_response_schema(self, operation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从OpenAPI操作中提取响应结构"""
        responses = operation.get("responses", {})

        # 查找成功响应
        for status_code in ["200", "201", "202"]:
            if status_code in responses:
                response = responses[status_code]
                content = response.get("content", {})

                for content_type, content_schema in content.items():
                    if "application/json" in content_type:
                        return content_schema.get("schema")

        return None

    def _generate_parameter_examples(self, parameters_schema: Dict[str, Any]) -> Dict[str, Any]:
        """生成参数示例"""
        examples = {}
        properties = parameters_schema.get("properties", {})

        for param_name, param_schema in properties.items():
            param_type = param_schema.get("type", "string")

            if param_type == "string":
                if "email" in param_schema.get("format", ""):
                    examples[param_name] = "<EMAIL>"
                elif "date" in param_schema.get("format", ""):
                    examples[param_name] = "2024-01-01"
                else:
                    examples[param_name] = f"example_{param_name}"
            elif param_type == "integer":
                examples[param_name] = 1
            elif param_type == "number":
                examples[param_name] = 1.0
            elif param_type == "boolean":
                examples[param_name] = True
            elif param_type == "array":
                examples[param_name] = ["example_item"]
            elif param_type == "object":
                examples[param_name] = {"key": "value"}

        return examples

    async def _generate_basic_config(self, base_url: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """生成基础服务配置"""
        try:
            parsed_url = urlparse(base_url)
            service_name = parsed_url.netloc.replace(".", "_").replace(":", "_")

            service_config = {
                "name": service_name,
                "description": f"自动发现的服务: {base_url}",
                "base_url": base_url,
                "service_type": ServiceType.REST_API.value,
                "version": "1.0.0",
                "timeout": 30.0,
                "max_retries": 3,
                "headers": {
                    "Content-Type": "application/json",
                    "User-Agent": "AI-Agent-Framework/1.0"
                },
                "endpoints": [],
                "tags": ["auto-discovered", "basic"],
                "metadata": {
                    "discovery_time": "auto-generated",
                    "source": "basic_discovery"
                }
            }

            # 添加基础端点
            basic_endpoints = [
                {
                    "name": "health_check",
                    "path": "/health",
                    "method": "GET",
                    "description": "健康检查端点",
                    "requires_auth": False,
                    "timeout": 10.0,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                },
                {
                    "name": "status",
                    "path": "/status",
                    "method": "GET",
                    "description": "状态查询端点",
                    "requires_auth": False,
                    "timeout": 10.0,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            ]

            # 测试哪些端点可用
            for endpoint in basic_endpoints:
                try:
                    test_url = urljoin(base_url, endpoint["path"])
                    async with self._session.get(
                        test_url,
                        timeout=aiohttp.ClientTimeout(total=5)
                    ) as response:
                        if response.status < 400:
                            service_config["endpoints"].append(endpoint)
                            if endpoint["name"] == "health_check":
                                service_config["health_check_path"] = endpoint["path"]
                except Exception:
                    continue

            # 如果没有发现任何端点，添加根路径
            if not service_config["endpoints"]:
                service_config["endpoints"].append({
                    "name": "root",
                    "path": "/",
                    "method": "GET",
                    "description": "根路径端点",
                    "requires_auth": False,
                    "timeout": 30.0,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                })

            return service_config

        except Exception as e:
            self._logger.error(f"生成基础配置失败: {str(e)}")
            raise

    async def _save_service_config(self, service_config: Dict[str, Any], filename: str) -> Path:
        """保存服务配置到文件"""
        try:
            if not filename.endswith(('.yaml', '.yml')):
                filename += '.yaml'

            config_path = self._config_dir / filename

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    service_config,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    sort_keys=False
                )

            self._logger.info(f"服务配置已保存到: {config_path}")
            return config_path

        except Exception as e:
            self._logger.error(f"保存服务配置失败: {str(e)}")
            raise

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self._session is None:
            self._session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.close()
            self._session = None
