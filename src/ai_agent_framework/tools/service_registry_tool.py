"""
AI Agent Framework 服务注册工具

这个工具提供了将现有微服务或API快速注册为AI Agent可调用工具的能力。
支持REST API、GraphQL、gRPC等多种服务类型的自动发现和注册。
"""

import asyncio
import json
import logging
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

import aiohttp
import yaml
from pydantic import BaseModel, Field, HttpUrl, validator

from ai_agent_framework.core.interfaces import ToolInterface
from ai_agent_framework.core.messages import ToolResult
from ai_agent_framework.exceptions import ToolError
from ai_agent_framework.utils.logging_system import logging_system


class ServiceType(str, Enum):
    """服务类型枚举"""
    REST_API = "rest_api"
    GRAPHQL = "graphql"
    GRPC = "grpc"
    WEBSOCKET = "websocket"
    SOAP = "soap"


class AuthType(str, Enum):
    """认证类型枚举"""
    NONE = "none"
    API_KEY = "api_key"
    BEARER_TOKEN = "bearer_token"
    BASIC_AUTH = "basic_auth"
    OAUTH2 = "oauth2"
    JWT = "jwt"
    CUSTOM = "custom"


class ServiceEndpoint(BaseModel):
    """服务端点定义"""
    
    name: str = Field(..., description="端点名称")
    path: str = Field(..., description="端点路径")
    method: str = Field(default="GET", description="HTTP方法")
    description: str = Field(..., description="端点描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="参数定义")
    response_schema: Optional[Dict[str, Any]] = Field(None, description="响应结构")
    requires_auth: bool = Field(default=True, description="是否需要认证")
    timeout: Optional[float] = Field(default=30.0, description="超时时间")
    
    @validator('method')
    def validate_method(cls, v):
        """验证HTTP方法"""
        allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
        if v.upper() not in allowed_methods:
            raise ValueError(f"不支持的HTTP方法: {v}")
        return v.upper()


class ServiceAuth(BaseModel):
    """服务认证配置"""
    
    type: AuthType = Field(..., description="认证类型")
    config: Dict[str, Any] = Field(default_factory=dict, description="认证配置")
    
    # API Key认证
    api_key: Optional[str] = Field(None, description="API密钥")
    api_key_header: str = Field(default="X-API-Key", description="API密钥头部名称")
    
    # Bearer Token认证
    token: Optional[str] = Field(None, description="Bearer令牌")
    
    # Basic认证
    username: Optional[str] = Field(None, description="用户名")
    password: Optional[str] = Field(None, description="密码")
    
    # OAuth2认证
    client_id: Optional[str] = Field(None, description="客户端ID")
    client_secret: Optional[str] = Field(None, description="客户端密钥")
    token_url: Optional[HttpUrl] = Field(None, description="令牌获取URL")
    scope: Optional[str] = Field(None, description="权限范围")


class ServiceConfig(BaseModel):
    """服务配置定义"""
    
    name: str = Field(..., description="服务名称")
    description: str = Field(..., description="服务描述")
    base_url: HttpUrl = Field(..., description="服务基础URL")
    service_type: ServiceType = Field(default=ServiceType.REST_API, description="服务类型")
    version: str = Field(default="1.0.0", description="服务版本")
    
    # 认证配置
    auth: Optional[ServiceAuth] = Field(None, description="认证配置")
    
    # 端点配置
    endpoints: List[ServiceEndpoint] = Field(default_factory=list, description="服务端点列表")
    
    # 全局配置
    timeout: float = Field(default=30.0, description="默认超时时间")
    max_retries: int = Field(default=3, description="最大重试次数")
    headers: Dict[str, str] = Field(default_factory=dict, description="默认请求头")
    
    # 健康检查
    health_check_path: Optional[str] = Field(None, description="健康检查路径")
    health_check_interval: int = Field(default=60, description="健康检查间隔(秒)")
    
    # 元数据
    tags: List[str] = Field(default_factory=list, description="服务标签")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class ServiceRegistryTool(ToolInterface):
    """
    服务注册工具
    
    提供将现有微服务或API注册为AI Agent工具的能力。
    支持多种服务类型和认证方式的自动发现和注册。
    """
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化服务注册工具
        
        Args:
            config_dir: 配置文件目录，默认为 ./configs/services
        """
        self._logger = logging_system.get_logger("service_registry_tool")
        self._config_dir = Path(config_dir or "./configs/services")
        self._config_dir.mkdir(parents=True, exist_ok=True)
        
        # 已注册的服务配置
        self._registered_services: Dict[str, ServiceConfig] = {}
        
        # HTTP会话
        self._session: Optional[aiohttp.ClientSession] = None
        
        # 加载已有的服务配置
        asyncio.create_task(self._load_existing_services())
    
    @property
    def name(self) -> str:
        """工具名称"""
        return "service_registry"
    
    @property
    def description(self) -> str:
        """工具描述"""
        return "注册和管理外部服务，将微服务或API转换为AI Agent可调用的工具"
    
    @property
    def parameters_schema(self) -> Dict[str, Any]:
        """工具参数JSON Schema"""
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["register", "unregister", "list", "discover", "test", "update"],
                    "description": "要执行的操作：注册、注销、列表、发现、测试、更新"
                },
                "service_config": {
                    "type": "object",
                    "description": "服务配置（注册和更新时需要）",
                    "properties": {
                        "name": {"type": "string", "description": "服务名称"},
                        "description": {"type": "string", "description": "服务描述"},
                        "base_url": {"type": "string", "description": "服务基础URL"},
                        "service_type": {
                            "type": "string",
                            "enum": ["rest_api", "graphql", "grpc", "websocket", "soap"],
                            "description": "服务类型"
                        },
                        "auth": {
                            "type": "object",
                            "description": "认证配置",
                            "properties": {
                                "type": {
                                    "type": "string",
                                    "enum": ["none", "api_key", "bearer_token", "basic_auth", "oauth2", "jwt"],
                                    "description": "认证类型"
                                },
                                "api_key": {"type": "string", "description": "API密钥"},
                                "token": {"type": "string", "description": "Bearer令牌"},
                                "username": {"type": "string", "description": "用户名"},
                                "password": {"type": "string", "description": "密码"}
                            }
                        },
                        "endpoints": {
                            "type": "array",
                            "description": "服务端点列表",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string", "description": "端点名称"},
                                    "path": {"type": "string", "description": "端点路径"},
                                    "method": {"type": "string", "description": "HTTP方法"},
                                    "description": {"type": "string", "description": "端点描述"},
                                    "parameters": {"type": "object", "description": "参数定义"}
                                }
                            }
                        }
                    }
                },
                "service_name": {
                    "type": "string",
                    "description": "服务名称（注销、测试时需要）"
                },
                "discovery_url": {
                    "type": "string",
                    "description": "服务发现URL（自动发现时需要）"
                },
                "config_file": {
                    "type": "string",
                    "description": "配置文件路径（从文件加载时需要）"
                }
            },
            "required": ["action"]
        }
    
    async def execute(
        self,
        arguments: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> ToolResult:
        """
        执行服务注册操作
        
        Args:
            arguments: 工具参数
            context: 执行上下文
            
        Returns:
            ToolResult: 执行结果
        """
        try:
            action = arguments.get("action")
            if not action:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少必需参数: action"
                )
            
            # 确保HTTP会话已初始化
            if self._session is None:
                self._session = aiohttp.ClientSession()
            
            # 根据操作类型执行相应的功能
            if action == "register":
                return await self._register_service(arguments)
            elif action == "unregister":
                return await self._unregister_service(arguments)
            elif action == "list":
                return await self._list_services()
            elif action == "discover":
                return await self._discover_service(arguments)
            elif action == "test":
                return await self._test_service(arguments)
            elif action == "update":
                return await self._update_service(arguments)
            else:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"不支持的操作: {action}"
                )
                
        except Exception as e:
            self._logger.error(f"服务注册工具执行失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"执行失败: {str(e)}"
            )
    
    async def validate_arguments(self, arguments: Dict[str, Any]) -> bool:
        """验证工具参数"""
        return "action" in arguments and arguments["action"] in [
            "register", "unregister", "list", "discover", "test", "update"
        ]
    
    async def _load_existing_services(self) -> None:
        """加载已有的服务配置"""
        try:
            for config_file in self._config_dir.glob("*.yaml"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    service_config = ServiceConfig(**config_data)
                    self._registered_services[service_config.name] = service_config
                    self._logger.info(f"已加载服务配置: {service_config.name}")
                    
                except Exception as e:
                    self._logger.error(f"加载服务配置失败 {config_file}: {str(e)}")
                    
        except Exception as e:
            self._logger.error(f"加载服务配置目录失败: {str(e)}")

    async def _register_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """注册服务"""
        try:
            # 从参数或配置文件加载服务配置
            if "config_file" in arguments:
                config_file = Path(arguments["config_file"])
                if not config_file.exists():
                    return ToolResult(
                        tool_call_id="",
                        success=False,
                        error=f"配置文件不存在: {config_file}"
                    )

                with open(config_file, 'r', encoding='utf-8') as f:
                    if config_file.suffix.lower() in ['.yaml', '.yml']:
                        config_data = yaml.safe_load(f)
                    else:
                        config_data = json.load(f)
            else:
                config_data = arguments.get("service_config", {})

            if not config_data:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务配置信息"
                )

            # 验证和创建服务配置
            service_config = ServiceConfig(**config_data)

            # 检查服务是否已存在
            if service_config.name in self._registered_services:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"服务已存在: {service_config.name}"
                )

            # 测试服务连接
            if not await self._test_service_connection(service_config):
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"无法连接到服务: {service_config.base_url}"
                )

            # 保存服务配置
            await self._save_service_config(service_config)

            # 注册到内存
            self._registered_services[service_config.name] = service_config

            self._logger.info(f"成功注册服务: {service_config.name}")

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功注册服务: {service_config.name}",
                    "service_name": service_config.name,
                    "base_url": str(service_config.base_url),
                    "endpoints_count": len(service_config.endpoints)
                }
            )

        except Exception as e:
            self._logger.error(f"注册服务失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"注册服务失败: {str(e)}"
            )

    async def _unregister_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """注销服务"""
        try:
            service_name = arguments.get("service_name")
            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._registered_services:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"服务不存在: {service_name}"
                )

            # 从内存中移除
            del self._registered_services[service_name]

            # 删除配置文件
            config_file = self._config_dir / f"{service_name}.yaml"
            if config_file.exists():
                config_file.unlink()

            self._logger.info(f"成功注销服务: {service_name}")

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功注销服务: {service_name}",
                    "service_name": service_name
                }
            )

        except Exception as e:
            self._logger.error(f"注销服务失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"注销服务失败: {str(e)}"
            )

    async def _list_services(self) -> ToolResult:
        """列出所有已注册的服务"""
        try:
            services_info = []

            for service_name, service_config in self._registered_services.items():
                # 执行健康检查
                is_healthy = await self._test_service_connection(service_config)

                services_info.append({
                    "name": service_config.name,
                    "description": service_config.description,
                    "base_url": str(service_config.base_url),
                    "service_type": service_config.service_type.value,
                    "version": service_config.version,
                    "endpoints_count": len(service_config.endpoints),
                    "is_healthy": is_healthy,
                    "tags": service_config.tags
                })

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"找到 {len(services_info)} 个已注册的服务",
                    "services": services_info,
                    "total_count": len(services_info)
                }
            )

        except Exception as e:
            self._logger.error(f"列出服务失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"列出服务失败: {str(e)}"
            )

    async def _discover_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """自动发现服务"""
        try:
            discovery_url = arguments.get("discovery_url")
            if not discovery_url:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务发现URL"
                )

            # 尝试从不同的标准端点发现服务信息
            discovered_info = await self._auto_discover_service_info(discovery_url)

            if not discovered_info:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"无法从 {discovery_url} 发现服务信息"
                )

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": "成功发现服务信息",
                    "discovered_info": discovered_info,
                    "suggestion": "请检查发现的信息，然后使用register操作注册服务"
                }
            )

        except Exception as e:
            self._logger.error(f"服务发现失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"服务发现失败: {str(e)}"
            )

    async def _test_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """测试服务连接和功能"""
        try:
            service_name = arguments.get("service_name")
            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._registered_services:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"服务不存在: {service_name}"
                )

            service_config = self._registered_services[service_name]

            # 执行全面的服务测试
            test_results = await self._comprehensive_service_test(service_config)

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"服务测试完成: {service_name}",
                    "service_name": service_name,
                    "test_results": test_results
                }
            )

        except Exception as e:
            self._logger.error(f"测试服务失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"测试服务失败: {str(e)}"
            )

    async def _update_service(self, arguments: Dict[str, Any]) -> ToolResult:
        """更新服务配置"""
        try:
            service_config_data = arguments.get("service_config", {})
            service_name = service_config_data.get("name")

            if not service_name:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error="缺少服务名称"
                )

            if service_name not in self._registered_services:
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"服务不存在: {service_name}"
                )

            # 创建新的服务配置
            service_config = ServiceConfig(**service_config_data)

            # 测试更新后的服务连接
            if not await self._test_service_connection(service_config):
                return ToolResult(
                    tool_call_id="",
                    success=False,
                    error=f"无法连接到更新后的服务: {service_config.base_url}"
                )

            # 保存更新的配置
            await self._save_service_config(service_config)

            # 更新内存中的配置
            self._registered_services[service_name] = service_config

            self._logger.info(f"成功更新服务: {service_name}")

            return ToolResult(
                tool_call_id="",
                success=True,
                result={
                    "message": f"成功更新服务: {service_name}",
                    "service_name": service_name,
                    "base_url": str(service_config.base_url),
                    "endpoints_count": len(service_config.endpoints)
                }
            )

        except Exception as e:
            self._logger.error(f"更新服务失败: {str(e)}")
            return ToolResult(
                tool_call_id="",
                success=False,
                error=f"更新服务失败: {str(e)}"
            )

    async def _test_service_connection(self, service_config: ServiceConfig) -> bool:
        """测试服务连接"""
        try:
            # 构建测试URL
            test_url = str(service_config.base_url)
            if service_config.health_check_path:
                test_url = urljoin(test_url, service_config.health_check_path)

            # 准备认证头
            headers = dict(service_config.headers)
            if service_config.auth:
                auth_headers = await self._get_auth_headers(service_config.auth)
                headers.update(auth_headers)

            # 发送测试请求
            async with self._session.get(
                test_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=service_config.timeout)
            ) as response:
                return response.status < 400

        except Exception as e:
            self._logger.warning(f"服务连接测试失败 {service_config.name}: {str(e)}")
            return False

    async def _get_auth_headers(self, auth_config: ServiceAuth) -> Dict[str, str]:
        """获取认证头"""
        headers = {}

        if auth_config.type == AuthType.API_KEY and auth_config.api_key:
            headers[auth_config.api_key_header] = auth_config.api_key
        elif auth_config.type == AuthType.BEARER_TOKEN and auth_config.token:
            headers["Authorization"] = f"Bearer {auth_config.token}"
        elif auth_config.type == AuthType.BASIC_AUTH and auth_config.username and auth_config.password:
            import base64
            credentials = base64.b64encode(
                f"{auth_config.username}:{auth_config.password}".encode()
            ).decode()
            headers["Authorization"] = f"Basic {credentials}"

        return headers

    async def _save_service_config(self, service_config: ServiceConfig) -> None:
        """保存服务配置到文件"""
        config_file = self._config_dir / f"{service_config.name}.yaml"

        # 转换为字典并处理特殊类型
        config_dict = service_config.dict()
        config_dict['base_url'] = str(config_dict['base_url'])

        if config_dict.get('auth') and config_dict['auth'].get('token_url'):
            config_dict['auth']['token_url'] = str(config_dict['auth']['token_url'])

        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)

    async def _auto_discover_service_info(self, base_url: str) -> Optional[Dict[str, Any]]:
        """自动发现服务信息"""
        try:
            discovered_info = {
                "name": "",
                "description": "",
                "base_url": base_url,
                "service_type": ServiceType.REST_API.value,
                "endpoints": []
            }

            # 尝试常见的API文档端点
            doc_endpoints = [
                "/swagger.json",
                "/openapi.json",
                "/api-docs",
                "/docs",
                "/swagger-ui",
                "/.well-known/openapi",
                "/v1/swagger.json",
                "/api/v1/swagger.json"
            ]

            for endpoint in doc_endpoints:
                try:
                    url = urljoin(base_url, endpoint)
                    async with self._session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')

                            if 'application/json' in content_type:
                                api_spec = await response.json()
                                return await self._parse_openapi_spec(api_spec, base_url)
                            elif 'text/html' in content_type:
                                # 可能是Swagger UI页面
                                discovered_info["description"] = f"发现Swagger UI文档: {url}"

                except Exception:
                    continue

            # 如果没有找到API文档，尝试基本的健康检查
            health_endpoints = ["/health", "/ping", "/status", "/"]
            for endpoint in health_endpoints:
                try:
                    url = urljoin(base_url, endpoint)
                    async with self._session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                        if response.status == 200:
                            discovered_info["name"] = urlparse(base_url).netloc.replace('.', '_')
                            discovered_info["description"] = f"发现可访问的服务端点: {url}"
                            discovered_info["health_check_path"] = endpoint
                            return discovered_info

                except Exception:
                    continue

            return None

        except Exception as e:
            self._logger.error(f"自动发现服务信息失败: {str(e)}")
            return None

    async def _parse_openapi_spec(self, api_spec: Dict[str, Any], base_url: str) -> Dict[str, Any]:
        """解析OpenAPI规范"""
        try:
            discovered_info = {
                "name": api_spec.get("info", {}).get("title", "").replace(" ", "_").lower(),
                "description": api_spec.get("info", {}).get("description", ""),
                "base_url": base_url,
                "service_type": ServiceType.REST_API.value,
                "version": api_spec.get("info", {}).get("version", "1.0.0"),
                "endpoints": []
            }

            # 解析路径和操作
            paths = api_spec.get("paths", {})
            for path, path_item in paths.items():
                for method, operation in path_item.items():
                    if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                        endpoint = {
                            "name": operation.get("operationId", f"{method}_{path.replace('/', '_')}"),
                            "path": path,
                            "method": method.upper(),
                            "description": operation.get("summary", operation.get("description", "")),
                            "parameters": self._extract_parameters_from_openapi(operation),
                            "response_schema": self._extract_response_schema_from_openapi(operation)
                        }
                        discovered_info["endpoints"].append(endpoint)

            return discovered_info

        except Exception as e:
            self._logger.error(f"解析OpenAPI规范失败: {str(e)}")
            return None

    def _extract_parameters_from_openapi(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """从OpenAPI操作中提取参数"""
        parameters_schema = {
            "type": "object",
            "properties": {},
            "required": []
        }

        parameters = operation.get("parameters", [])
        for param in parameters:
            param_name = param.get("name")
            param_schema = param.get("schema", {})

            parameters_schema["properties"][param_name] = {
                "type": param_schema.get("type", "string"),
                "description": param.get("description", "")
            }

            if param.get("required", False):
                parameters_schema["required"].append(param_name)

        # 处理请求体
        request_body = operation.get("requestBody", {})
        if request_body:
            content = request_body.get("content", {})
            for content_type, content_schema in content.items():
                if "application/json" in content_type:
                    schema = content_schema.get("schema", {})
                    if schema.get("type") == "object":
                        properties = schema.get("properties", {})
                        parameters_schema["properties"].update(properties)
                        required = schema.get("required", [])
                        parameters_schema["required"].extend(required)

        return parameters_schema

    def _extract_response_schema_from_openapi(self, operation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从OpenAPI操作中提取响应结构"""
        responses = operation.get("responses", {})
        success_response = responses.get("200") or responses.get("201")

        if success_response:
            content = success_response.get("content", {})
            for content_type, content_schema in content.items():
                if "application/json" in content_type:
                    return content_schema.get("schema")

        return None

    async def _comprehensive_service_test(self, service_config: ServiceConfig) -> Dict[str, Any]:
        """执行全面的服务测试"""
        test_results = {
            "connection_test": False,
            "auth_test": False,
            "endpoints_test": [],
            "performance_metrics": {},
            "errors": []
        }

        try:
            # 1. 连接测试
            test_results["connection_test"] = await self._test_service_connection(service_config)

            # 2. 认证测试
            if service_config.auth:
                test_results["auth_test"] = await self._test_service_auth(service_config)
            else:
                test_results["auth_test"] = True  # 无需认证

            # 3. 端点测试
            for endpoint in service_config.endpoints:
                endpoint_result = await self._test_service_endpoint(service_config, endpoint)
                test_results["endpoints_test"].append(endpoint_result)

            # 4. 性能测试
            test_results["performance_metrics"] = await self._test_service_performance(service_config)

        except Exception as e:
            test_results["errors"].append(f"测试执行失败: {str(e)}")

        return test_results

    async def _test_service_auth(self, service_config: ServiceConfig) -> bool:
        """测试服务认证"""
        try:
            headers = await self._get_auth_headers(service_config.auth)
            test_url = str(service_config.base_url)

            async with self._session.get(
                test_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                return response.status != 401  # 非401表示认证成功或不需要认证

        except Exception:
            return False

    async def _test_service_endpoint(self, service_config: ServiceConfig, endpoint: ServiceEndpoint) -> Dict[str, Any]:
        """测试服务端点"""
        result = {
            "name": endpoint.name,
            "path": endpoint.path,
            "method": endpoint.method,
            "success": False,
            "status_code": None,
            "response_time": None,
            "error": None
        }

        try:
            url = urljoin(str(service_config.base_url), endpoint.path)
            headers = dict(service_config.headers)

            if service_config.auth:
                auth_headers = await self._get_auth_headers(service_config.auth)
                headers.update(auth_headers)

            start_time = asyncio.get_event_loop().time()

            async with self._session.request(
                endpoint.method,
                url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=endpoint.timeout or 30)
            ) as response:
                end_time = asyncio.get_event_loop().time()

                result["success"] = response.status < 400
                result["status_code"] = response.status
                result["response_time"] = round((end_time - start_time) * 1000, 2)  # 毫秒

        except Exception as e:
            result["error"] = str(e)

        return result

    async def _test_service_performance(self, service_config: ServiceConfig) -> Dict[str, Any]:
        """测试服务性能"""
        metrics = {
            "avg_response_time": 0,
            "min_response_time": float('inf'),
            "max_response_time": 0,
            "success_rate": 0,
            "total_requests": 0
        }

        try:
            test_url = str(service_config.base_url)
            if service_config.health_check_path:
                test_url = urljoin(test_url, service_config.health_check_path)

            headers = dict(service_config.headers)
            if service_config.auth:
                auth_headers = await self._get_auth_headers(service_config.auth)
                headers.update(auth_headers)

            # 执行5次测试请求
            response_times = []
            success_count = 0

            for _ in range(5):
                try:
                    start_time = asyncio.get_event_loop().time()
                    async with self._session.get(
                        test_url,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=10)
                    ) as response:
                        end_time = asyncio.get_event_loop().time()
                        response_time = (end_time - start_time) * 1000
                        response_times.append(response_time)

                        if response.status < 400:
                            success_count += 1

                except Exception:
                    pass

            if response_times:
                metrics["avg_response_time"] = round(sum(response_times) / len(response_times), 2)
                metrics["min_response_time"] = round(min(response_times), 2)
                metrics["max_response_time"] = round(max(response_times), 2)
                metrics["success_rate"] = round((success_count / len(response_times)) * 100, 2)
                metrics["total_requests"] = len(response_times)

        except Exception as e:
            metrics["error"] = str(e)

        return metrics

    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self._session is None:
            self._session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.close()
            self._session = None
