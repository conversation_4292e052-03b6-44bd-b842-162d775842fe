# 天气服务配置示例
# 基于OpenWeatherMap API的天气查询服务

name: "weather_service"
description: "提供天气信息查询的服务，支持当前天气、天气预报等功能"
base_url: "https://api.openweathermap.org/data/2.5"
service_type: "rest_api"
version: "2.5"

# API Key认证
auth:
  type: "api_key"
  api_key: "${OPENWEATHER_API_KEY}"
  api_key_header: "appid"

# 服务端点
endpoints:
  - name: "get_current_weather"
    path: "/weather"
    method: "GET"
    description: "获取指定城市的当前天气信息"
    requires_auth: true
    timeout: 15.0
    
    parameters:
      type: "object"
      properties:
        q:
          type: "string"
          description: "城市名称，格式：城市名,国家代码（如：Beijing,CN）"
        lat:
          type: "number"
          description: "纬度"
        lon:
          type: "number"
          description: "经度"
        units:
          type: "string"
          enum: ["standard", "metric", "imperial"]
          description: "温度单位：standard(开尔文), metric(摄氏度), imperial(华氏度)"
          default: "metric"
        lang:
          type: "string"
          description: "语言代码（如：zh_cn为中文）"
          default: "zh_cn"
      # 必须提供城市名或经纬度
      anyOf:
        - required: ["q"]
        - required: ["lat", "lon"]
    
    response_schema:
      type: "object"
      properties:
        coord:
          type: "object"
          properties:
            lon: { type: "number", description: "经度" }
            lat: { type: "number", description: "纬度" }
        weather:
          type: "array"
          items:
            type: "object"
            properties:
              id: { type: "integer", description: "天气条件ID" }
              main: { type: "string", description: "天气主要描述" }
              description: { type: "string", description: "天气详细描述" }
              icon: { type: "string", description: "天气图标代码" }
        main:
          type: "object"
          properties:
            temp: { type: "number", description: "温度" }
            feels_like: { type: "number", description: "体感温度" }
            temp_min: { type: "number", description: "最低温度" }
            temp_max: { type: "number", description: "最高温度" }
            pressure: { type: "integer", description: "大气压力(hPa)" }
            humidity: { type: "integer", description: "湿度(%)" }
        wind:
          type: "object"
          properties:
            speed: { type: "number", description: "风速(m/s)" }
            deg: { type: "integer", description: "风向(度)" }
        name: { type: "string", description: "城市名称" }

  - name: "get_weather_forecast"
    path: "/forecast"
    method: "GET"
    description: "获取5天天气预报，每3小时一个数据点"
    requires_auth: true
    timeout: 20.0
    
    parameters:
      type: "object"
      properties:
        q:
          type: "string"
          description: "城市名称，格式：城市名,国家代码"
        lat:
          type: "number"
          description: "纬度"
        lon:
          type: "number"
          description: "经度"
        cnt:
          type: "integer"
          description: "返回的数据点数量（最大40）"
          minimum: 1
          maximum: 40
          default: 40
        units:
          type: "string"
          enum: ["standard", "metric", "imperial"]
          description: "温度单位"
          default: "metric"
        lang:
          type: "string"
          description: "语言代码"
          default: "zh_cn"
      anyOf:
        - required: ["q"]
        - required: ["lat", "lon"]

  - name: "get_air_pollution"
    path: "/air_pollution"
    method: "GET"
    description: "获取空气质量数据"
    requires_auth: true
    timeout: 15.0
    
    parameters:
      type: "object"
      properties:
        lat:
          type: "number"
          description: "纬度"
        lon:
          type: "number"
          description: "经度"
      required: ["lat", "lon"]

# 全局配置
timeout: 20.0
max_retries: 3
headers:
  "User-Agent": "AI-Agent-Weather-Service/1.0"

# 健康检查（使用一个简单的天气查询）
health_check_path: "/weather?q=London&appid=${OPENWEATHER_API_KEY}"
health_check_interval: 300  # 5分钟检查一次

# 服务标签
tags:
  - "weather"
  - "api"
  - "external"
  - "real-time"

metadata:
  provider: "OpenWeatherMap"
  documentation: "https://openweathermap.org/api"
  rate_limit: "1000 calls/day (free tier)"
  data_source: "Global weather stations and satellites"

# 高级配置
advanced:
  retry_config:
    max_attempts: 3
    backoff_factor: 1.5
    retry_on_status: [429, 500, 502, 503, 504]  # 包含429限流错误
  
  cache_config:
    enabled: true
    ttl: 600  # 10分钟缓存，天气数据更新不频繁
    cache_key_prefix: "weather_cache"
  
  monitoring:
    enabled: true
    metrics_prefix: "weather_service"
    track_response_time: true
    track_error_rate: true
    track_cache_hit_rate: true

# 环境配置
environments:
  development:
    timeout: 30.0
    cache_config:
      ttl: 300  # 开发环境缓存时间短一些
  
  production:
    timeout: 15.0
    max_retries: 5
    cache_config:
      ttl: 900  # 生产环境可以缓存更久

# 验证配置
validation:
  required_env_vars:
    - "OPENWEATHER_API_KEY"
  connectivity_check: true
  auth_validation: true
  endpoint_validation: false  # 跳过端点验证，避免消耗API配额
