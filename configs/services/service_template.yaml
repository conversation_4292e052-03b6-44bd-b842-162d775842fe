# AI Agent Framework 服务配置模板
# 这个文件展示了如何配置一个外部服务以供AI Agent调用

# 基础服务信息
name: "example_service"                    # 服务名称（必需，唯一标识）
description: "示例服务描述"                 # 服务描述（必需）
base_url: "https://api.example.com"        # 服务基础URL（必需）
service_type: "rest_api"                   # 服务类型：rest_api, graphql, grpc, websocket, soap
version: "1.0.0"                          # 服务版本

# 认证配置（可选）
auth:
  type: "api_key"                         # 认证类型：none, api_key, bearer_token, basic_auth, oauth2, jwt
  api_key: "${API_KEY}"                   # API密钥（从环境变量读取）
  api_key_header: "X-API-Key"             # API密钥头部名称
  
  # Bearer Token认证示例
  # type: "bearer_token"
  # token: "${BEARER_TOKEN}"
  
  # Basic认证示例
  # type: "basic_auth"
  # username: "${USERNAME}"
  # password: "${PASSWORD}"
  
  # OAuth2认证示例
  # type: "oauth2"
  # client_id: "${CLIENT_ID}"
  # client_secret: "${CLIENT_SECRET}"
  # token_url: "https://api.example.com/oauth/token"
  # scope: "read write"

# 服务端点配置
endpoints:
  - name: "get_user"                      # 端点名称（必需）
    path: "/users/{user_id}"              # 端点路径（必需）
    method: "GET"                         # HTTP方法（默认GET）
    description: "获取用户信息"            # 端点描述（必需）
    requires_auth: true                   # 是否需要认证（默认true）
    timeout: 30.0                         # 超时时间（秒）
    
    # 参数定义（JSON Schema格式）
    parameters:
      type: "object"
      properties:
        user_id:
          type: "string"
          description: "用户ID"
        include_profile:
          type: "boolean"
          description: "是否包含用户资料"
          default: false
      required: ["user_id"]
    
    # 响应结构定义（可选）
    response_schema:
      type: "object"
      properties:
        id:
          type: "string"
          description: "用户ID"
        name:
          type: "string"
          description: "用户名"
        email:
          type: "string"
          description: "邮箱地址"
        profile:
          type: "object"
          description: "用户资料"

  - name: "create_user"
    path: "/users"
    method: "POST"
    description: "创建新用户"
    requires_auth: true
    timeout: 30.0
    
    parameters:
      type: "object"
      properties:
        name:
          type: "string"
          description: "用户名"
        email:
          type: "string"
          format: "email"
          description: "邮箱地址"
        password:
          type: "string"
          description: "密码"
          minLength: 8
      required: ["name", "email", "password"]
    
    response_schema:
      type: "object"
      properties:
        id:
          type: "string"
          description: "新创建的用户ID"
        message:
          type: "string"
          description: "创建结果消息"

  - name: "search_users"
    path: "/users/search"
    method: "GET"
    description: "搜索用户"
    requires_auth: true
    timeout: 15.0
    
    parameters:
      type: "object"
      properties:
        query:
          type: "string"
          description: "搜索关键词"
        limit:
          type: "integer"
          description: "返回结果数量限制"
          default: 10
          minimum: 1
          maximum: 100
        offset:
          type: "integer"
          description: "结果偏移量"
          default: 0
          minimum: 0
      required: ["query"]

# 全局配置
timeout: 30.0                            # 默认超时时间（秒）
max_retries: 3                           # 最大重试次数
headers:                                 # 默认请求头
  "Content-Type": "application/json"
  "User-Agent": "AI-Agent-Framework/1.0"

# 健康检查配置
health_check_path: "/health"             # 健康检查路径（可选）
health_check_interval: 60                # 健康检查间隔（秒）

# 服务标签和元数据
tags:                                    # 服务标签
  - "user-management"
  - "api"
  - "production"

metadata:                                # 额外元数据
  team: "backend-team"
  contact: "<EMAIL>"
  documentation: "https://docs.example.com/api"
  swagger_url: "https://api.example.com/swagger.json"

# 高级配置（可选）
advanced:
  # 连接池配置
  connection_pool:
    max_connections: 100
    max_connections_per_host: 30
    keepalive_timeout: 30
  
  # 重试配置
  retry_config:
    max_attempts: 3
    backoff_factor: 2.0
    retry_on_status: [500, 502, 503, 504]
  
  # 缓存配置
  cache_config:
    enabled: true
    ttl: 300                            # 缓存时间（秒）
    cache_key_prefix: "service_cache"
  
  # 监控配置
  monitoring:
    enabled: true
    metrics_prefix: "service_example"
    track_response_time: true
    track_error_rate: true

# 环境特定配置
environments:
  development:
    base_url: "https://dev-api.example.com"
    timeout: 60.0
    debug: true
  
  staging:
    base_url: "https://staging-api.example.com"
    timeout: 45.0
  
  production:
    base_url: "https://api.example.com"
    timeout: 30.0
    max_retries: 5

# 配置验证规则（可选）
validation:
  required_env_vars:                     # 必需的环境变量
    - "API_KEY"
  
  connectivity_check: true               # 是否在注册时检查连接性
  auth_validation: true                  # 是否验证认证配置
  endpoint_validation: true              # 是否验证端点可访问性
