# 邮件服务工具使用指南

## 概述

AI Agent Framework 现已集成完整的邮件服务工具，提供企业级的邮件发送和管理功能。邮件工具支持：

- **多种邮件服务提供商** - SMTP、SendGrid、Amazon SES、Mailgun
- **丰富的邮件格式** - 文本、HTML、附件支持
- **高级邮件功能** - 抄送、密送、回复地址、优先级
- **邮箱验证** - 格式验证和连接测试
- **批量发送** - 支持并发批量邮件发送
- **错误处理** - 完善的错误处理和重试机制

## 功能特性

### 🚀 核心功能
- **统一接口**：所有邮件服务使用相同的API接口
- **异步操作**：支持高性能的异步邮件发送
- **多格式支持**：文本和HTML格式邮件
- **附件支持**：支持多种类型的文件附件

### 🔧 高级功能
- **邮件优先级**：支持高、中、低优先级设置
- **抄送密送**：完整的收件人管理功能
- **回复地址**：自定义回复地址设置
- **批量发送**：高效的并发邮件发送
- **连接测试**：邮件服务连接状态检测

## 安装依赖

根据您要使用的邮件服务提供商，安装相应的依赖库：

```bash
# 基础邮件功能（SMTP + HTTP API）
pip install aiohttp

# Amazon SES支持
pip install boto3

# 安装所有邮件服务依赖
pip install ai-agent-framework[email-full]
```

## 快速开始

### SMTP 邮件发送示例

```python
import asyncio
from ai_agent_framework.tools import EmailTool, EmailConfig

async def smtp_example():
    # 配置SMTP邮件服务
    config = EmailConfig(
        provider_type="smtp",
        smtp_host="smtp.gmail.com",
        smtp_port=587,
        smtp_username="<EMAIL>",
        smtp_password="your_app_password",
        smtp_use_tls=True,
        default_sender="<EMAIL>",
        default_sender_name="Your Name"
    )
    
    # 创建邮件工具
    email_tool = EmailTool(config)
    
    # 发送邮件
    result = await email_tool.execute({
        "action": "send",
        "to": ["<EMAIL>"],
        "subject": "测试邮件",
        "body": "这是一封测试邮件",
        "is_html": False
    })
    
    if result.success:
        print("邮件发送成功！")
    else:
        print(f"邮件发送失败: {result.error}")

# 运行示例
asyncio.run(smtp_example())
```

## 支持的邮件服务提供商

### 1. SMTP 服务

支持所有标准SMTP服务，包括Gmail、Outlook、企业邮箱等。

```python
config = EmailConfig(
    provider_type="smtp",
    smtp_host="smtp.gmail.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your_app_password",
    smtp_use_tls=True
)
```

### 2. SendGrid

专业的邮件发送服务，适合大量邮件发送。

```python
config = EmailConfig(
    provider_type="sendgrid",
    api_key="your_sendgrid_api_key",
    default_sender="<EMAIL>"
)
```

### 3. Amazon SES

AWS的邮件服务，成本低廉且可靠。

```python
config = EmailConfig(
    provider_type="amazon_ses",
    api_key="your_aws_access_key_id",
    api_secret="your_aws_secret_access_key",
    region="us-east-1",
    default_sender="<EMAIL>"
)
```

### 4. Mailgun

功能强大的邮件API服务。

```python
config = EmailConfig(
    provider_type="mailgun",
    api_key="your_mailgun_api_key",
    region="your_mailgun_domain",  # 使用region字段存储domain
    default_sender="<EMAIL>"
)
```

## 支持的操作

### 邮件发送操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `send` | 发送邮件 | `to`, `subject`, `body` |
| `test_connection` | 测试邮件服务连接 | 无 |
| `validate_email` | 验证邮箱地址格式 | `email` |

## 详细使用指南

### 1. 发送基础邮件

```python
# 发送文本邮件
result = await email_tool.execute({
    "action": "send",
    "to": ["<EMAIL>"],
    "subject": "文本邮件",
    "body": "这是一封纯文本邮件",
    "is_html": False
})

# 发送HTML邮件
html_content = """
<html>
<body>
    <h2>HTML邮件标题</h2>
    <p>这是一封<strong>HTML格式</strong>的邮件。</p>
    <a href="https://example.com">点击链接</a>
</body>
</html>
"""

result = await email_tool.execute({
    "action": "send",
    "to": ["<EMAIL>"],
    "subject": "HTML邮件",
    "body": html_content,
    "is_html": True
})
```

### 2. 发送带附件的邮件

```python
# 准备附件内容
attachment_content = "这是附件的内容"

result = await email_tool.execute({
    "action": "send",
    "to": ["<EMAIL>"],
    "subject": "带附件的邮件",
    "body": "请查收附件",
    "attachments": [
        {
            "filename": "document.txt",
            "content": attachment_content,
            "content_type": "text/plain"
        }
    ]
})
```

### 3. 发送带抄送和密送的邮件

```python
result = await email_tool.execute({
    "action": "send",
    "to": ["<EMAIL>"],
    "cc": ["<EMAIL>", "<EMAIL>"],
    "bcc": ["<EMAIL>"],
    "subject": "重要通知",
    "body": "这是一封重要通知邮件",
    "reply_to": "<EMAIL>",
    "priority": "high"
})
```

### 4. 邮箱地址验证

```python
# 验证邮箱格式
result = await email_tool.execute({
    "action": "validate_email",
    "email": "<EMAIL>"
})

if result.success:
    is_valid = result.result["is_valid"]
    print(f"邮箱地址有效性: {is_valid}")
```

### 5. 测试邮件服务连接

```python
# 测试邮件服务连接
result = await email_tool.execute({
    "action": "test_connection"
})

if result.success:
    print("邮件服务连接正常")
else:
    print(f"连接失败: {result.error}")
```

## 高级用法

### 1. 批量邮件发送

```python
async def send_bulk_emails():
    """批量发送邮件"""
    recipients = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # 并发发送邮件
    tasks = []
    for recipient in recipients:
        task = email_tool.execute({
            "action": "send",
            "to": [recipient],
            "subject": "批量邮件",
            "body": f"发送给 {recipient} 的邮件"
        })
        tasks.append(task)
    
    # 等待所有邮件发送完成
    results = await asyncio.gather(*tasks)
    
    # 统计结果
    success_count = sum(1 for r in results if r.success)
    print(f"成功发送 {success_count}/{len(recipients)} 封邮件")
```

### 2. 邮件模板系统

```python
class EmailTemplate:
    """邮件模板类"""
    
    @staticmethod
    def welcome_email(user_name: str, login_url: str) -> str:
        return f"""
        <html>
        <body>
            <h2>欢迎 {user_name}！</h2>
            <p>感谢您注册我们的服务。</p>
            <p><a href="{login_url}">点击这里登录</a></p>
        </body>
        </html>
        """
    
    @staticmethod
    def notification_email(title: str, content: str) -> str:
        return f"""
        <html>
        <body>
            <h3>{title}</h3>
            <p>{content}</p>
            <hr>
            <small>这是系统自动发送的邮件，请勿回复。</small>
        </body>
        </html>
        """

# 使用模板发送邮件
async def send_welcome_email(user_email: str, user_name: str):
    html_content = EmailTemplate.welcome_email(
        user_name=user_name,
        login_url="https://example.com/login"
    )
    
    result = await email_tool.execute({
        "action": "send",
        "to": [user_email],
        "subject": f"欢迎 {user_name}！",
        "body": html_content,
        "is_html": True
    })
    
    return result.success
```

### 3. 错误处理和重试

```python
async def reliable_send_email(email_tool, email_data, max_retries=3):
    """可靠的邮件发送，包含重试机制"""
    
    for attempt in range(max_retries):
        try:
            result = await email_tool.execute(email_data)
            
            if result.success:
                return True
            else:
                print(f"发送失败，尝试 {attempt + 1}/{max_retries}: {result.error}")
                
        except Exception as e:
            print(f"发送异常，尝试 {attempt + 1}/{max_retries}: {e}")
        
        if attempt < max_retries - 1:
            await asyncio.sleep(2 ** attempt)  # 指数退避
    
    return False

# 使用可靠发送
success = await reliable_send_email(email_tool, {
    "action": "send",
    "to": ["<EMAIL>"],
    "subject": "重要邮件",
    "body": "这是一封重要邮件"
})
```

## 配置参数详解

### EmailConfig 配置选项

```python
config = EmailConfig(
    # 基础配置
    provider_type="smtp",  # 邮件服务类型
    
    # SMTP配置
    smtp_host="smtp.gmail.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your_password",
    smtp_use_tls=True,
    smtp_use_ssl=False,
    smtp_timeout=30,
    
    # 默认发件人
    default_sender="<EMAIL>",
    default_sender_name="Your Name",
    
    # API配置（用于第三方服务）
    api_key="your_api_key",
    api_secret="your_api_secret",
    region="us-east-1"
)
```

## 常见邮件服务配置

### Gmail SMTP

```python
EmailConfig(
    provider_type="smtp",
    smtp_host="smtp.gmail.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your_app_password",  # 使用应用专用密码
    smtp_use_tls=True
)
```

### Outlook SMTP

```python
EmailConfig(
    provider_type="smtp",
    smtp_host="smtp-mail.outlook.com",
    smtp_port=587,
    smtp_username="<EMAIL>",
    smtp_password="your_password",
    smtp_use_tls=True
)
```

### 企业邮箱 SMTP

```python
EmailConfig(
    provider_type="smtp",
    smtp_host="smtp.company.com",
    smtp_port=465,
    smtp_username="<EMAIL>",
    smtp_password="your_password",
    smtp_use_ssl=True  # 使用SSL而不是TLS
)
```

## 最佳实践

### 1. 安全配置

- **使用应用专用密码**：对于Gmail等服务，使用应用专用密码而不是账户密码
- **环境变量存储**：将敏感信息存储在环境变量中
- **API密钥管理**：定期轮换API密钥

```python
import os

config = EmailConfig(
    provider_type="smtp",
    smtp_host=os.getenv("SMTP_HOST"),
    smtp_username=os.getenv("SMTP_USERNAME"),
    smtp_password=os.getenv("SMTP_PASSWORD")
)
```

### 2. 性能优化

- **连接复用**：对于大量邮件，复用邮件工具实例
- **批量发送**：使用异步并发发送多封邮件
- **错误处理**：实现重试机制和错误恢复

### 3. 邮件内容优化

- **HTML格式**：使用HTML格式提供更好的视觉效果
- **响应式设计**：确保邮件在移动设备上正常显示
- **文本备选**：为HTML邮件提供纯文本版本

## 故障排除

### 常见问题

1. **SMTP认证失败**
   - 检查用户名和密码是否正确
   - 确认是否需要使用应用专用密码
   - 验证SMTP服务器地址和端口

2. **邮件发送失败**
   - 检查网络连接
   - 验证收件人邮箱地址格式
   - 确认邮件服务配额限制

3. **附件发送问题**
   - 检查附件大小限制
   - 验证附件内容编码
   - 确认MIME类型设置

### 调试技巧

```python
# 启用详细日志
from ai_agent_framework.utils.logging_system import logging_system
logging_system.configure(level="DEBUG")

# 测试连接
result = await email_tool.execute({"action": "test_connection"})
print(f"连接测试结果: {result.success}")

# 验证邮箱格式
result = await email_tool.execute({
    "action": "validate_email",
    "email": "<EMAIL>"
})
print(f"邮箱验证结果: {result.result}")
```

## 更多资源

- [AI Agent Framework 完整文档](./README.md)
- [邮件服务最佳实践](./邮件服务最佳实践.md)
- [API参考文档](./API参考文档.md)
