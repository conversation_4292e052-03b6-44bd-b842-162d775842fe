# 服务注册API参考文档

## 概述

本文档详细描述了AI Agent Framework服务注册功能的所有API接口、参数和返回值。

## 核心工具类

### ServiceRegistryTool

服务注册工具，用于注册、管理和查询外部服务。

#### 构造函数

```python
ServiceRegistryTool(config_dir: Optional[Union[str, Path]] = None)
```

**参数：**
- `config_dir`: 配置文件目录，默认为 `./configs/services`

#### execute方法

```python
async def execute(
    arguments: Dict[str, Any],
    context: Optional[Dict[str, Any]] = None
) -> ToolResult
```

**支持的操作：**

##### register - 注册服务

```python
await service_registry.execute({
    "action": "register",
    "service_config": {
        "name": "service_name",
        "description": "服务描述",
        "base_url": "https://api.example.com",
        "service_type": "rest_api",
        "auth": {"type": "none"},
        "endpoints": [...]
    },
    "test_url": "https://api.example.com/health"  # 可选
})
```

**参数：**
- `service_config`: 服务配置对象
- `config_file`: 配置文件路径（与service_config二选一）
- `test_url`: 测试URL（可选）

**返回值：**
```python
{
    "success": True,
    "result": {
        "message": "成功注册服务: service_name",
        "service_name": "service_name",
        "base_url": "https://api.example.com",
        "endpoints_count": 3,
        "test_result": {...}  # 如果提供了test_url
    }
}
```

##### unregister - 注销服务

```python
await service_registry.execute({
    "action": "unregister",
    "service_name": "service_name"
})
```

**参数：**
- `service_name`: 要注销的服务名称

**返回值：**
```python
{
    "success": True,
    "result": {
        "message": "成功注销服务: service_name",
        "service_name": "service_name"
    }
}
```

##### list - 列出服务

```python
await service_registry.execute({
    "action": "list"
})
```

**返回值：**
```python
{
    "success": True,
    "result": {
        "message": "找到 3 个已注册的服务",
        "services": [
            {
                "name": "service1",
                "description": "服务1描述",
                "base_url": "https://api1.example.com",
                "service_type": "rest_api",
                "version": "1.0.0",
                "endpoints_count": 5,
                "is_healthy": True,
                "tags": ["api", "production"]
            }
        ],
        "total_count": 3
    }
}
```

##### discover - 自动发现服务

```python
await service_registry.execute({
    "action": "discover",
    "discovery_url": "https://api.example.com"
})
```

**参数：**
- `discovery_url`: 服务发现URL

**返回值：**
```python
{
    "success": True,
    "result": {
        "message": "成功发现服务信息",
        "discovered_info": {
            "name": "discovered_service",
            "description": "自动发现的服务",
            "base_url": "https://api.example.com",
            "endpoints": [...]
        },
        "suggestion": "请检查发现的信息，然后使用register操作注册服务"
    }
}
```

##### test - 测试服务

```python
await service_registry.execute({
    "action": "test",
    "service_name": "service_name"
})
```

**参数：**
- `service_name`: 要测试的服务名称

**返回值：**
```python
{
    "success": True,
    "result": {
        "message": "服务测试完成: service_name",
        "service_name": "service_name",
        "test_results": {
            "connection_test": True,
            "auth_test": True,
            "endpoints_test": [...],
            "performance_metrics": {...}
        }
    }
}
```

##### update - 更新服务

```python
await service_registry.execute({
    "action": "update",
    "service_config": {
        "name": "existing_service",
        # 更新的配置...
    }
})
```

### ServiceDiscoveryTool

服务发现工具，用于自动发现网络中的服务。

#### 构造函数

```python
ServiceDiscoveryTool(config_dir: Optional[Union[str, Path]] = None)
```

#### execute方法

**支持的操作：**

##### scan - 网络扫描

```python
await service_discovery.execute({
    "action": "scan",
    "target": "localhost",
    "options": {
        "port_range": "8000-9000",
        "timeout": 5,
        "include_health_check": True
    }
})
```

**参数：**
- `target`: 扫描目标（IP地址或域名）
- `options.port_range`: 端口范围，格式："开始端口-结束端口"
- `options.timeout`: 超时时间（秒）
- `options.include_health_check`: 是否包含健康检查

##### discover_openapi - 发现OpenAPI服务

```python
await service_discovery.execute({
    "action": "discover_openapi",
    "target": "https://api.example.com",
    "options": {
        "generate_examples": True
    },
    "output_file": "discovered_service.yaml"  # 可选
})
```

**参数：**
- `target`: 目标URL
- `options.generate_examples`: 是否生成参数示例
- `output_file`: 输出配置文件路径（可选）

##### discover_consul - 发现Consul服务

```python
await service_discovery.execute({
    "action": "discover_consul",
    "target": "http://localhost:8500",
    "options": {
        "include_health_check": True
    }
})
```

##### generate_config - 生成配置文件

```python
await service_discovery.execute({
    "action": "generate_config",
    "target": "https://api.example.com",
    "output_file": "generated_config.yaml",
    "options": {
        "generate_examples": True
    }
})
```

### ServiceAuthManager

认证管理工具，用于管理服务的认证凭据。

#### 构造函数

```python
ServiceAuthManager(credentials_dir: Optional[Union[str, Path]] = None)
```

#### execute方法

**支持的操作：**

##### store - 存储认证凭据

```python
await auth_manager.execute({
    "action": "store",
    "service_name": "my_service",
    "auth_type": "api_key",
    "credentials": {
        "api_key": "your-api-key",
        "api_key_header": "X-API-Key"
    },
    "test_url": "https://api.example.com/test"  # 可选
})
```

**支持的认证类型：**

###### API Key认证
```python
{
    "auth_type": "api_key",
    "credentials": {
        "api_key": "your-api-key",
        "api_key_header": "X-API-Key"  # 默认为"X-API-Key"
    }
}
```

###### Bearer Token认证
```python
{
    "auth_type": "bearer_token",
    "credentials": {
        "token": "your-bearer-token"
    }
}
```

###### Basic认证
```python
{
    "auth_type": "basic_auth",
    "credentials": {
        "username": "your-username",
        "password": "your-password"
    }
}
```

###### OAuth2认证
```python
{
    "auth_type": "oauth2",
    "credentials": {
        "client_id": "your-client-id",
        "client_secret": "your-client-secret",
        "token_url": "https://api.example.com/oauth/token",
        "scope": "read write"
    }
}
```

###### JWT认证
```python
{
    "auth_type": "jwt",
    "credentials": {
        "jwt_secret": "your-jwt-secret",
        "jwt_algorithm": "HS256",
        "jwt_payload": {
            "sub": "ai-agent",
            "role": "service"
        }
    }
}
```

###### 自定义认证
```python
{
    "auth_type": "custom",
    "credentials": {
        "custom_headers": {
            "X-Custom-Auth": "custom-token",
            "X-Client-ID": "client-id"
        }
    }
}
```

##### get - 获取认证凭据

```python
await auth_manager.execute({
    "action": "get",
    "service_name": "my_service"
})
```

**返回值：**
```python
{
    "success": True,
    "result": {
        "service_name": "my_service",
        "auth_type": "api_key",
        "auth_headers": {
            "X-API-Key": "your-api-key"
        },
        "expires_at": None
    }
}
```

##### refresh - 刷新认证凭据

```python
await auth_manager.execute({
    "action": "refresh",
    "service_name": "oauth_service"
})
```

##### validate - 验证认证凭据

```python
await auth_manager.execute({
    "action": "validate",
    "service_name": "my_service",
    "test_url": "https://api.example.com/protected"
})
```

##### list - 列出所有认证凭据

```python
await auth_manager.execute({
    "action": "list"
})
```

##### delete - 删除认证凭据

```python
await auth_manager.execute({
    "action": "delete",
    "service_name": "my_service"
})
```

### ServiceValidator

服务验证工具，用于测试和验证注册的服务。

#### 构造函数

```python
ServiceValidator(
    auth_manager: Optional[ServiceAuthManager] = None,
    reports_dir: Optional[Union[str, Path]] = None
)
```

#### execute方法

**支持的操作：**

##### validate_service - 验证整个服务

```python
await validator.execute({
    "action": "validate_service",
    "service_config": service_config,
    "options": {
        "timeout": 30,
        "max_retries": 3,
        "generate_report": True
    }
})
```

##### validate_endpoint - 验证单个端点

```python
await validator.execute({
    "action": "validate_endpoint",
    "service_config": service_config,
    "endpoint_name": "get_users",
    "options": {
        "test_parameters": {
            "page": 1,
            "limit": 10
        }
    }
})
```

##### health_check - 健康检查

```python
await validator.execute({
    "action": "health_check",
    "service_config": service_config
})
```

##### performance_test - 性能测试

```python
await validator.execute({
    "action": "performance_test",
    "service_config": service_config,
    "options": {
        "performance_iterations": 10,
        "timeout": 30
    }
})
```

**返回值：**
```python
{
    "success": True,
    "result": {
        "service_name": "my_service",
        "performance_metrics": {
            "iterations": 10,
            "avg_response_time": 150.5,
            "min_response_time": 120.2,
            "max_response_time": 200.8,
            "success_rate": 100.0,
            "successful_requests": 10,
            "failed_requests": 0
        },
        "performance_grade": "优秀"
    }
}
```

##### full_validation - 完整验证

```python
await validator.execute({
    "action": "full_validation",
    "service_config": service_config,
    "options": {
        "timeout": 30,
        "performance_iterations": 5,
        "generate_report": True
    }
})
```

### ServiceToolFactory

服务工具工厂，用于创建动态服务工具。

#### 构造函数

```python
ServiceToolFactory(auth_manager: Optional[ServiceAuthManager] = None)
```

#### 主要方法

##### create_tools_from_config

```python
def create_tools_from_config(
    service_config: ServiceConfig
) -> List[DynamicServiceTool]
```

从服务配置创建工具列表。

##### create_tools_from_config_file

```python
def create_tools_from_config_file(
    config_file: Union[str, Path]
) -> List[DynamicServiceTool]
```

从配置文件创建工具列表。

##### create_and_register_tools

```python
async def create_and_register_tools(
    service_configs: List[ServiceConfig],
    tool_registry: ToolRegistry
) -> int
```

批量创建并注册工具到工具注册表。

## 数据模型

### ServiceConfig

服务配置模型。

```python
class ServiceConfig(BaseModel):
    name: str                                    # 服务名称
    description: str                             # 服务描述
    base_url: HttpUrl                           # 服务基础URL
    service_type: ServiceType                   # 服务类型
    version: str = "1.0.0"                     # 服务版本
    auth: Optional[ServiceAuth] = None          # 认证配置
    endpoints: List[ServiceEndpoint] = []       # 端点列表
    timeout: float = 30.0                      # 默认超时时间
    max_retries: int = 3                       # 最大重试次数
    headers: Dict[str, str] = {}               # 默认请求头
    health_check_path: Optional[str] = None    # 健康检查路径
    health_check_interval: int = 60            # 健康检查间隔
    tags: List[str] = []                       # 服务标签
    metadata: Dict[str, Any] = {}              # 额外元数据
```

### ServiceEndpoint

服务端点模型。

```python
class ServiceEndpoint(BaseModel):
    name: str                                   # 端点名称
    path: str                                   # 端点路径
    method: str = "GET"                        # HTTP方法
    description: str                           # 端点描述
    parameters: Dict[str, Any] = {}            # 参数定义
    response_schema: Optional[Dict[str, Any]] = None  # 响应结构
    requires_auth: bool = True                 # 是否需要认证
    timeout: Optional[float] = 30.0            # 超时时间
```

### ServiceAuth

服务认证配置模型。

```python
class ServiceAuth(BaseModel):
    type: AuthType                             # 认证类型
    config: Dict[str, Any] = {}               # 认证配置
    api_key: Optional[str] = None             # API密钥
    api_key_header: str = "X-API-Key"         # API密钥头部名称
    token: Optional[str] = None               # Bearer令牌
    username: Optional[str] = None            # 用户名
    password: Optional[str] = None            # 密码
    client_id: Optional[str] = None           # OAuth2客户端ID
    client_secret: Optional[str] = None       # OAuth2客户端密钥
    token_url: Optional[HttpUrl] = None       # OAuth2令牌URL
    scope: Optional[str] = None               # OAuth2权限范围
```

## 错误处理

所有工具都返回统一的`ToolResult`对象：

```python
class ToolResult:
    tool_call_id: str                         # 工具调用ID
    success: bool                             # 是否成功
    result: Optional[Dict[str, Any]] = None   # 成功时的结果
    error: Optional[str] = None               # 失败时的错误信息
```

### 常见错误码

- `TOOL_VALIDATION_FAILED`: 工具验证失败
- `TOOL_REGISTRATION_ERROR`: 工具注册错误
- `CONFIG_NOT_LOADED`: 配置未加载
- `SERVICE_NOT_FOUND`: 服务不存在
- `AUTH_FAILED`: 认证失败
- `CONNECTION_TIMEOUT`: 连接超时
- `INVALID_RESPONSE`: 无效响应

## 配置文件格式

详细的配置文件格式请参考：
- [服务配置模板](../configs/services/service_template.yaml)
- [天气服务示例](../configs/services/weather_service.yaml)

## 示例代码

完整的示例代码请参考：
- [服务注册示例](../examples/service_registration_example.py)
- [快速注册示例](../examples/quick_service_registration.py)
- [认证配置示例](../examples/service_auth_example.py)
- [服务验证示例](../examples/service_validation_example.py)
