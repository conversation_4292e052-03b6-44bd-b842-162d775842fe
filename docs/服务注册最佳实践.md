# 服务注册最佳实践

## 概述

本文档提供了使用AI Agent Framework服务注册功能的最佳实践指南，帮助您构建稳定、安全、高性能的服务集成方案。

## 1. 项目结构组织

### 推荐的目录结构

```
project/
├── configs/
│   ├── services/
│   │   ├── production/
│   │   │   ├── user_service.yaml
│   │   │   ├── order_service.yaml
│   │   │   └── payment_service.yaml
│   │   ├── staging/
│   │   │   ├── user_service.yaml
│   │   │   └── order_service.yaml
│   │   ├── development/
│   │   │   └── mock_services.yaml
│   │   └── templates/
│   │       └── service_template.yaml
│   ├── credentials/
│   │   ├── .gitignore          # 重要：排除敏感文件
│   │   └── credentials.json    # 加密存储
│   └── discovery/
│       └── discovery_sources.yaml
├── reports/
│   ├── service_validation/
│   └── performance/
├── scripts/
│   ├── register_services.py
│   ├── validate_services.py
│   └── health_check.py
└── docs/
    ├── services/
    └── api/
```

### 配置文件命名规范

```
# 服务配置文件
{service_name}_{environment}.yaml
user_service_prod.yaml
order_service_staging.yaml

# 认证配置文件
{service_name}_auth.json
user_service_auth.json

# 发现配置文件
discovery_{source_type}.yaml
discovery_consul.yaml
```

## 2. 配置管理最佳实践

### 环境变量使用

```yaml
# 服务配置文件中使用环境变量
name: "user_service"
base_url: "${USER_SERVICE_URL}"
auth:
  type: "api_key"
  api_key: "${USER_SERVICE_API_KEY}"
  
# 数据库连接
database:
  url: "${DATABASE_URL}"
  
# 外部服务
external_apis:
  payment_gateway: "${PAYMENT_GATEWAY_URL}"
```

```bash
# .env 文件
USER_SERVICE_URL=https://api.example.com
USER_SERVICE_API_KEY=your-secret-api-key
DATABASE_URL=postgresql://user:pass@localhost/db
PAYMENT_GATEWAY_URL=https://payment.example.com
```

### 配置验证

```python
import os
from pathlib import Path

def validate_environment():
    """验证必需的环境变量"""
    required_vars = [
        'USER_SERVICE_URL',
        'USER_SERVICE_API_KEY',
        'DATABASE_URL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")

def load_config_with_validation(config_file: Path):
    """加载并验证配置文件"""
    validate_environment()
    
    # 加载配置
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    
    # 替换环境变量
    config = substitute_env_vars(config)
    
    # 验证配置结构
    validate_config_structure(config)
    
    return config
```

## 3. 安全最佳实践

### 认证凭据管理

```python
# 使用专门的凭据管理器
class SecureCredentialsManager:
    def __init__(self, encryption_key: str):
        self.encryption_key = encryption_key
        self.credentials_file = Path("configs/credentials/credentials.enc")
    
    def store_credentials(self, service_name: str, credentials: dict):
        """安全存储认证凭据"""
        encrypted_data = self.encrypt(credentials)
        # 存储到加密文件
        
    def get_credentials(self, service_name: str) -> dict:
        """安全获取认证凭据"""
        encrypted_data = self.load_encrypted_data(service_name)
        return self.decrypt(encrypted_data)

# 使用示例
creds_manager = SecureCredentialsManager(os.getenv("ENCRYPTION_KEY"))
creds_manager.store_credentials("payment_api", {
    "api_key": "secret-key",
    "client_secret": "secret-value"
})
```

### 权限控制

```python
# 为不同的工具设置不同的权限
tool_registry.register_tool(
    tool=user_service_tool,
    permissions={"read_users", "write_users"},
    max_concurrent=2
)

tool_registry.register_tool(
    tool=payment_service_tool,
    permissions={"process_payments"},  # 更严格的权限
    max_concurrent=1  # 限制并发
)

# 在Agent中检查权限
class SecureAgent(Agent):
    def __init__(self, user_permissions: Set[str], **kwargs):
        super().__init__(**kwargs)
        self.user_permissions = user_permissions
    
    async def execute_tool(self, tool_name: str, arguments: dict):
        tool = self.tool_registry.get_tool(tool_name)
        required_permissions = self.tool_registry.get_tool_permissions(tool_name)
        
        if not required_permissions.issubset(self.user_permissions):
            raise PermissionError(f"权限不足，需要权限: {required_permissions}")
        
        return await tool.execute(arguments)
```

### 敏感数据处理

```python
# 日志中屏蔽敏感信息
import logging
import re

class SensitiveDataFilter(logging.Filter):
    def filter(self, record):
        # 屏蔽API密钥
        record.msg = re.sub(r'api_key["\']:\s*["\'][^"\']+["\']', 
                           'api_key": "***"', str(record.msg))
        # 屏蔽密码
        record.msg = re.sub(r'password["\']:\s*["\'][^"\']+["\']', 
                           'password": "***"', str(record.msg))
        return True

# 应用过滤器
logger = logging.getLogger("service_registry")
logger.addFilter(SensitiveDataFilter())
```

## 4. 性能优化

### 连接池配置

```python
# 配置HTTP连接池
import aiohttp

async def create_optimized_session():
    connector = aiohttp.TCPConnector(
        limit=100,              # 总连接数限制
        limit_per_host=30,      # 每个主机连接数限制
        ttl_dns_cache=300,      # DNS缓存时间
        use_dns_cache=True,     # 启用DNS缓存
        keepalive_timeout=30,   # 保持连接时间
        enable_cleanup_closed=True
    )
    
    timeout = aiohttp.ClientTimeout(
        total=30,               # 总超时时间
        connect=10,             # 连接超时时间
        sock_read=20            # 读取超时时间
    )
    
    return aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={
            "User-Agent": "AI-Agent-Framework/1.0",
            "Accept": "application/json",
            "Accept-Encoding": "gzip, deflate"
        }
    )
```

### 缓存策略

```python
from functools import lru_cache
import asyncio
import time

class ServiceCache:
    def __init__(self, ttl: int = 300):
        self.cache = {}
        self.ttl = ttl
    
    def get(self, key: str):
        if key in self.cache:
            value, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl:
                return value
            else:
                del self.cache[key]
        return None
    
    def set(self, key: str, value):
        self.cache[key] = (value, time.time())

# 使用缓存装饰器
def cached_service_call(cache_key: str, ttl: int = 300):
    def decorator(func):
        cache = ServiceCache(ttl)
        
        async def wrapper(*args, **kwargs):
            key = f"{cache_key}:{hash(str(args) + str(kwargs))}"
            result = cache.get(key)
            
            if result is None:
                result = await func(*args, **kwargs)
                cache.set(key, result)
            
            return result
        return wrapper
    return decorator

# 应用缓存
@cached_service_call("user_service", ttl=600)
async def get_user_info(user_id: int):
    # 调用用户服务
    pass
```

### 批量操作优化

```python
class BatchServiceCaller:
    def __init__(self, batch_size: int = 10, delay: float = 0.1):
        self.batch_size = batch_size
        self.delay = delay
        self.pending_calls = []
    
    async def add_call(self, service_tool, arguments):
        """添加到批量调用队列"""
        self.pending_calls.append((service_tool, arguments))
        
        if len(self.pending_calls) >= self.batch_size:
            await self.execute_batch()
    
    async def execute_batch(self):
        """执行批量调用"""
        if not self.pending_calls:
            return
        
        # 并发执行所有调用
        tasks = []
        for tool, args in self.pending_calls:
            tasks.append(tool.execute(args))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"批量调用失败 {i}: {result}")
        
        self.pending_calls.clear()
        
        # 添加延迟避免过于频繁的请求
        await asyncio.sleep(self.delay)
```

## 5. 错误处理和重试

### 智能重试策略

```python
import asyncio
import random
from typing import Callable, Any

class RetryStrategy:
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    def calculate_delay(self, attempt: int) -> float:
        """计算重试延迟时间"""
        delay = self.base_delay * (self.exponential_base ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            delay *= (0.5 + random.random() * 0.5)  # 添加抖动
        
        return delay
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """执行函数并在失败时重试"""
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_attempts - 1:
                    delay = self.calculate_delay(attempt)
                    logger.warning(
                        f"调用失败，{delay:.2f}秒后重试 "
                        f"(尝试 {attempt + 1}/{self.max_attempts}): {e}"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"所有重试都失败了: {e}")
        
        raise last_exception

# 使用示例
retry_strategy = RetryStrategy(max_attempts=3, base_delay=1.0)

async def call_service_with_retry(service_tool, arguments):
    return await retry_strategy.execute_with_retry(
        service_tool.execute,
        arguments
    )
```

### 断路器模式

```python
import time
from enum import Enum

class CircuitState(Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 断路状态
    HALF_OPEN = "half_open"  # 半开状态

class CircuitBreaker:
    def __init__(
        self,
        failure_threshold: int = 5,
        timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    async def call(self, func, *args, **kwargs):
        """通过断路器调用函数"""
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("断路器开启，服务不可用")
        
        try:
            result = await func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """检查是否应该尝试重置断路器"""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.timeout
        )
    
    def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN

# 为每个服务创建断路器
service_breakers = {
    "user_service": CircuitBreaker(failure_threshold=3, timeout=30),
    "payment_service": CircuitBreaker(failure_threshold=2, timeout=60),
}

async def call_service_with_breaker(service_name: str, service_tool, arguments):
    breaker = service_breakers.get(service_name)
    if breaker:
        return await breaker.call(service_tool.execute, arguments)
    else:
        return await service_tool.execute(arguments)
```

## 6. 监控和日志

### 结构化日志

```python
import json
import logging
from datetime import datetime

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # 创建结构化格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_service_call(
        self,
        service_name: str,
        endpoint: str,
        method: str,
        status_code: int,
        response_time: float,
        success: bool,
        error: str = None
    ):
        """记录服务调用日志"""
        log_data = {
            "event_type": "service_call",
            "timestamp": datetime.now().isoformat(),
            "service_name": service_name,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "response_time_ms": round(response_time * 1000, 2),
            "success": success,
            "error": error
        }
        
        if success:
            self.logger.info(json.dumps(log_data))
        else:
            self.logger.error(json.dumps(log_data))
    
    def log_auth_event(
        self,
        service_name: str,
        auth_type: str,
        success: bool,
        error: str = None
    ):
        """记录认证事件日志"""
        log_data = {
            "event_type": "authentication",
            "timestamp": datetime.now().isoformat(),
            "service_name": service_name,
            "auth_type": auth_type,
            "success": success,
            "error": error
        }
        
        if success:
            self.logger.info(json.dumps(log_data))
        else:
            self.logger.warning(json.dumps(log_data))

# 使用示例
logger = StructuredLogger("service_registry")

# 在服务调用时记录日志
async def call_service_with_logging(service_tool, arguments):
    start_time = time.time()
    
    try:
        result = await service_tool.execute(arguments)
        response_time = time.time() - start_time
        
        logger.log_service_call(
            service_name=service_tool.service_config.name,
            endpoint=service_tool.endpoint.path,
            method=service_tool.endpoint.method,
            status_code=result.result.get("status_code", 0),
            response_time=response_time,
            success=result.success
        )
        
        return result
    except Exception as e:
        response_time = time.time() - start_time
        
        logger.log_service_call(
            service_name=service_tool.service_config.name,
            endpoint=service_tool.endpoint.path,
            method=service_tool.endpoint.method,
            status_code=0,
            response_time=response_time,
            success=False,
            error=str(e)
        )
        
        raise
```

### 性能监控

```python
import time
from collections import defaultdict, deque
from typing import Dict, List

class PerformanceMonitor:
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.response_times: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=window_size)
        )
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.total_calls: Dict[str, int] = defaultdict(int)
    
    def record_call(
        self,
        service_name: str,
        response_time: float,
        success: bool
    ):
        """记录服务调用性能数据"""
        self.response_times[service_name].append(response_time)
        self.total_calls[service_name] += 1
        
        if not success:
            self.error_counts[service_name] += 1
    
    def get_metrics(self, service_name: str) -> Dict:
        """获取服务性能指标"""
        response_times = list(self.response_times[service_name])
        
        if not response_times:
            return {"error": "没有性能数据"}
        
        return {
            "avg_response_time": sum(response_times) / len(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "p95_response_time": self._percentile(response_times, 95),
            "p99_response_time": self._percentile(response_times, 99),
            "error_rate": self.error_counts[service_name] / self.total_calls[service_name],
            "total_calls": self.total_calls[service_name],
            "recent_calls": len(response_times)
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def get_all_metrics(self) -> Dict:
        """获取所有服务的性能指标"""
        return {
            service_name: self.get_metrics(service_name)
            for service_name in self.response_times.keys()
        }

# 全局性能监控器
performance_monitor = PerformanceMonitor()

# 在服务调用时记录性能数据
async def call_service_with_monitoring(service_tool, arguments):
    start_time = time.time()
    
    try:
        result = await service_tool.execute(arguments)
        response_time = time.time() - start_time
        
        performance_monitor.record_call(
            service_tool.service_config.name,
            response_time,
            result.success
        )
        
        return result
    except Exception as e:
        response_time = time.time() - start_time
        
        performance_monitor.record_call(
            service_tool.service_config.name,
            response_time,
            False
        )
        
        raise
```

## 7. 测试策略

### 单元测试

```python
import pytest
from unittest.mock import AsyncMock, patch

class TestServiceRegistry:
    @pytest.fixture
    async def service_registry(self):
        return ServiceRegistryTool()
    
    @pytest.fixture
    def sample_service_config(self):
        return {
            "name": "test_service",
            "description": "测试服务",
            "base_url": "https://api.test.com",
            "service_type": "rest_api",
            "auth": {"type": "none"},
            "endpoints": [
                {
                    "name": "get_data",
                    "path": "/data",
                    "method": "GET",
                    "description": "获取数据",
                    "requires_auth": False,
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
            ]
        }
    
    async def test_register_service_success(
        self,
        service_registry,
        sample_service_config
    ):
        """测试成功注册服务"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await service_registry.execute({
                "action": "register",
                "service_config": sample_service_config
            })
            
            assert result.success
            assert result.result["service_name"] == "test_service"
    
    async def test_register_service_connection_failed(
        self,
        service_registry,
        sample_service_config
    ):
        """测试连接失败时的注册"""
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.side_effect = Exception("连接失败")
            
            result = await service_registry.execute({
                "action": "register",
                "service_config": sample_service_config
            })
            
            assert not result.success
            assert "无法连接到服务" in result.error
```

### 集成测试

```python
class TestServiceIntegration:
    @pytest.fixture
    async def test_environment(self):
        """设置测试环境"""
        # 启动测试服务器
        test_server = await start_test_server()
        
        # 创建测试组件
        auth_manager = ServiceAuthManager()
        service_registry = ServiceRegistryTool()
        tool_factory = ServiceToolFactory(auth_manager)
        
        yield {
            "server": test_server,
            "auth_manager": auth_manager,
            "service_registry": service_registry,
            "tool_factory": tool_factory
        }
        
        # 清理测试环境
        await test_server.stop()
    
    async def test_full_service_lifecycle(self, test_environment):
        """测试完整的服务生命周期"""
        env = test_environment
        
        # 1. 注册服务
        service_config = create_test_service_config(env["server"].url)
        register_result = await env["service_registry"].execute({
            "action": "register",
            "service_config": service_config
        })
        assert register_result.success
        
        # 2. 创建工具
        tools = env["tool_factory"].create_tools_from_config(
            ServiceConfig(**service_config)
        )
        assert len(tools) > 0
        
        # 3. 测试工具调用
        tool = tools[0]
        result = await tool.execute({})
        assert result.success
        
        # 4. 注销服务
        unregister_result = await env["service_registry"].execute({
            "action": "unregister",
            "service_name": service_config["name"]
        })
        assert unregister_result.success
```

### 性能测试

```python
import asyncio
import time

class TestServicePerformance:
    async def test_concurrent_calls(self):
        """测试并发调用性能"""
        service_tool = create_test_service_tool()
        
        # 并发调用数量
        concurrent_calls = 50
        
        async def make_call():
            start_time = time.time()
            result = await service_tool.execute({})
            end_time = time.time()
            return result.success, end_time - start_time
        
        # 执行并发调用
        start_time = time.time()
        tasks = [make_call() for _ in range(concurrent_calls)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # 分析结果
        successful_calls = sum(1 for success, _ in results if success)
        response_times = [rt for _, rt in results]
        
        assert successful_calls >= concurrent_calls * 0.95  # 95%成功率
        assert max(response_times) < 5.0  # 最大响应时间不超过5秒
        assert total_time < 10.0  # 总时间不超过10秒
        
        print(f"并发调用测试结果:")
        print(f"  成功率: {successful_calls/concurrent_calls*100:.1f}%")
        print(f"  平均响应时间: {sum(response_times)/len(response_times):.3f}s")
        print(f"  最大响应时间: {max(response_times):.3f}s")
        print(f"  总执行时间: {total_time:.3f}s")
```

## 8. 部署和运维

### 健康检查脚本

```python
#!/usr/bin/env python3
"""
服务健康检查脚本
定期检查所有注册服务的健康状态
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

async def health_check_all_services():
    """检查所有服务的健康状态"""
    service_registry = ServiceRegistryTool()
    validator = ServiceValidator()
    
    # 获取所有服务
    services_result = await service_registry.execute({"action": "list"})
    
    if not services_result.success:
        print(f"❌ 获取服务列表失败: {services_result.error}")
        return False
    
    services = services_result.result.get("services", [])
    unhealthy_services = []
    
    print(f"🔍 开始检查 {len(services)} 个服务的健康状态...")
    
    for service in services:
        service_name = service["name"]
        
        # 执行健康检查
        health_result = await validator.execute({
            "action": "health_check",
            "config_file": f"configs/services/{service_name}.yaml"
        })
        
        if health_result.success:
            status = health_result.result.get("health_status")
            print(f"✅ {service_name}: {status}")
        else:
            print(f"❌ {service_name}: 不健康 - {health_result.error}")
            unhealthy_services.append({
                "name": service_name,
                "error": health_result.error
            })
    
    # 生成健康检查报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_services": len(services),
        "healthy_services": len(services) - len(unhealthy_services),
        "unhealthy_services": unhealthy_services
    }
    
    # 保存报告
    report_file = Path("reports/health_check.json")
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 健康检查完成:")
    print(f"   健康服务: {report['healthy_services']}/{report['total_services']}")
    print(f"   报告文件: {report_file}")
    
    return len(unhealthy_services) == 0

if __name__ == "__main__":
    success = asyncio.run(health_check_all_services())
    sys.exit(0 if success else 1)
```

### 自动化部署脚本

```bash
#!/bin/bash
# deploy_services.sh - 服务部署脚本

set -e

ENVIRONMENT=${1:-development}
CONFIG_DIR="configs/services/${ENVIRONMENT}"

echo "🚀 开始部署 ${ENVIRONMENT} 环境的服务..."

# 验证环境变量
echo "📋 验证环境变量..."
python3 scripts/validate_env.py --env ${ENVIRONMENT}

# 注册所有服务
echo "📝 注册服务..."
for config_file in ${CONFIG_DIR}/*.yaml; do
    if [ -f "$config_file" ]; then
        echo "注册服务: $(basename $config_file)"
        python3 scripts/register_service.py --config "$config_file"
    fi
done

# 验证服务
echo "✅ 验证服务..."
python3 scripts/validate_all_services.py --env ${ENVIRONMENT}

# 运行健康检查
echo "💓 健康检查..."
python3 scripts/health_check.py

echo "🎉 服务部署完成!"
```

### 监控告警

```python
# monitoring/alerts.py
import asyncio
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timedelta

class AlertManager:
    def __init__(self, smtp_config: dict):
        self.smtp_config = smtp_config
        self.alert_history = {}
    
    async def check_service_alerts(self):
        """检查服务告警"""
        performance_monitor = PerformanceMonitor()
        
        for service_name in performance_monitor.response_times.keys():
            metrics = performance_monitor.get_metrics(service_name)
            
            # 检查错误率告警
            if metrics["error_rate"] > 0.1:  # 错误率超过10%
                await self.send_alert(
                    f"服务错误率告警: {service_name}",
                    f"服务 {service_name} 错误率达到 {metrics['error_rate']*100:.1f}%"
                )
            
            # 检查响应时间告警
            if metrics["avg_response_time"] > 5.0:  # 平均响应时间超过5秒
                await self.send_alert(
                    f"服务响应时间告警: {service_name}",
                    f"服务 {service_name} 平均响应时间达到 {metrics['avg_response_time']:.2f}秒"
                )
    
    async def send_alert(self, subject: str, message: str):
        """发送告警邮件"""
        # 防止重复告警
        alert_key = f"{subject}:{message}"
        last_sent = self.alert_history.get(alert_key)
        
        if last_sent and datetime.now() - last_sent < timedelta(hours=1):
            return  # 1小时内不重复发送
        
        try:
            msg = MIMEText(message)
            msg['Subject'] = subject
            msg['From'] = self.smtp_config['from']
            msg['To'] = self.smtp_config['to']
            
            with smtplib.SMTP(self.smtp_config['host'], self.smtp_config['port']) as server:
                server.starttls()
                server.login(self.smtp_config['username'], self.smtp_config['password'])
                server.send_message(msg)
            
            self.alert_history[alert_key] = datetime.now()
            print(f"📧 告警邮件已发送: {subject}")
            
        except Exception as e:
            print(f"❌ 发送告警邮件失败: {e}")

# 定期运行告警检查
async def run_alert_monitoring():
    alert_manager = AlertManager({
        'host': 'smtp.example.com',
        'port': 587,
        'username': '<EMAIL>',
        'password': 'password',
        'from': '<EMAIL>',
        'to': '<EMAIL>'
    })
    
    while True:
        try:
            await alert_manager.check_service_alerts()
        except Exception as e:
            print(f"❌ 告警检查失败: {e}")
        
        await asyncio.sleep(300)  # 每5分钟检查一次

if __name__ == "__main__":
    asyncio.run(run_alert_monitoring())
```

## 总结

遵循这些最佳实践可以帮助您：

1. **提高系统稳定性**：通过合理的错误处理、重试机制和断路器模式
2. **增强安全性**：通过安全的认证管理和权限控制
3. **优化性能**：通过连接池、缓存和批量操作
4. **简化运维**：通过自动化脚本和监控告警
5. **保证质量**：通过全面的测试策略

记住，最佳实践需要根据具体的业务场景和技术栈进行调整。建议从简单开始，逐步完善系统的各个方面。
