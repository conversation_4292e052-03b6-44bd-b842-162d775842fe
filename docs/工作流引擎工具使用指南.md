# 工作流引擎工具使用指南

## 概述

AI Agent Framework 现已集成完整的工作流引擎工具，提供企业级的业务流程自动化功能。该工具支持：

- **多种任务类型** - HTTP请求、脚本执行、函数调用、条件判断等
- **复杂流程控制** - 任务依赖、条件执行、并行处理、变量传递
- **工作流管理** - 创建、启动、停止、监控工作流
- **错误处理** - 任务重试、超时控制、失败恢复
- **可视化监控** - 实时状态跟踪、执行结果查看

## 功能特性

### 🚀 核心功能
- **工作流编排**：支持复杂的业务流程设计和编排
- **任务调度**：智能的任务依赖管理和执行调度
- **并行处理**：支持任务的并行执行和同步
- **状态管理**：完整的工作流和任务状态跟踪

### 🔧 高级功能
- **动态变量**：支持工作流变量的动态传递和替换
- **条件分支**：基于条件的流程分支控制
- **错误恢复**：自动重试和错误处理机制
- **实时监控**：工作流执行状态的实时监控

## 快速开始

### 基础使用示例

```python
import asyncio
from ai_agent_framework.tools import WorkflowEngineTool, WorkflowEngineConfig

async def basic_workflow_example():
    # 创建工作流引擎工具
    config = WorkflowEngineConfig(
        max_concurrent_workflows=10,
        default_task_timeout=300
    )
    
    workflow_engine = WorkflowEngineTool(config)
    
    # 创建简单工作流
    workflow_config = {
        "name": "数据处理工作流",
        "description": "获取、处理和存储数据",
        "variables": {
            "api_url": "https://api.example.com/data",
            "output_path": "/tmp/result.json"
        },
        "tasks": [
            {
                "name": "获取数据",
                "task_type": "http_request",
                "config": {
                    "url": "${api_url}",
                    "method": "GET"
                },
                "dependencies": []
            },
            {
                "name": "处理数据",
                "task_type": "script",
                "config": {
                    "type": "python",
                    "script": "print('Processing data...')"
                },
                "dependencies": ["获取数据"]
            }
        ]
    }
    
    # 创建工作流
    result = await workflow_engine.execute({
        "action": "create_workflow",
        "workflow_config": workflow_config
    })
    
    if result.success:
        workflow_id = result.result["workflow_id"]
        
        # 启动工作流
        await workflow_engine.execute({
            "action": "start_workflow",
            "workflow_id": workflow_id
        })

# 运行示例
asyncio.run(basic_workflow_example())
```

## 支持的任务类型

### 1. HTTP请求任务 (http_request)

执行HTTP请求，支持各种HTTP方法和参数：

```python
{
    "name": "API调用",
    "task_type": "http_request",
    "config": {
        "url": "https://api.example.com/users",
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer ${token}"
        },
        "data": {
            "name": "${user_name}",
            "email": "${user_email}"
        }
    },
    "timeout_seconds": 30
}
```

### 2. 脚本执行任务 (script)

执行Shell或Python脚本：

```python
# Shell脚本
{
    "name": "文件处理",
    "task_type": "script",
    "config": {
        "type": "shell",
        "script": "cp ${source_file} ${dest_file} && echo 'File copied'"
    }
}

# Python脚本
{
    "name": "数据分析",
    "task_type": "script",
    "config": {
        "type": "python",
        "script": "import pandas as pd; df = pd.read_csv('${input_file}'); print(df.shape)"
    }
}
```

### 3. 函数调用任务 (function)

调用Python函数：

```python
{
    "name": "业务逻辑处理",
    "task_type": "function",
    "config": {
        "module": "mymodule.business",
        "function": "process_data",
        "args": ["${input_data}"],
        "kwargs": {
            "mode": "batch",
            "output_format": "json"
        }
    }
}
```

### 4. 条件判断任务 (condition)

基于条件执行分支逻辑：

```python
{
    "name": "条件检查",
    "task_type": "condition",
    "config": {
        "condition": "${user_count} > 100"
    }
}
```

### 5. 延迟任务 (delay)

添加执行延迟：

```python
{
    "name": "等待处理",
    "task_type": "delay",
    "config": {
        "seconds": 30
    }
}
```

## 支持的操作

### 工作流管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `create_workflow` | 创建新工作流 | `workflow_config` |
| `get_workflow` | 获取工作流信息 | `workflow_id` |
| `list_workflows` | 列出所有工作流 | 可选 `status_filter` |
| `start_workflow` | 启动工作流 | `workflow_id` |
| `stop_workflow` | 停止工作流 | `workflow_id` |
| `get_workflow_status` | 获取工作流状态 | `workflow_id` |

### 任务管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `list_tasks` | 列出工作流任务 | `workflow_id` |
| `get_task` | 获取任务信息 | `workflow_id`, `task_id` |

## 详细使用指南

### 1. 创建工作流

#### 基础工作流创建
```python
workflow_config = {
    "name": "用户注册流程",
    "description": "处理用户注册的完整流程",
    "version": "1.0",
    "variables": {
        "notification_email": "<EMAIL>",
        "welcome_template": "welcome_new_user"
    },
    "tasks": [
        {
            "name": "验证用户信息",
            "description": "验证用户提交的注册信息",
            "task_type": "function",
            "config": {
                "module": "user_service",
                "function": "validate_user_info",
                "args": ["${user_data}"]
            },
            "dependencies": [],
            "max_retries": 2,
            "timeout_seconds": 30
        },
        {
            "name": "创建用户账户",
            "description": "在数据库中创建用户账户",
            "task_type": "http_request",
            "config": {
                "url": "https://api.example.com/users",
                "method": "POST",
                "data": "${validated_user_data}"
            },
            "dependencies": ["验证用户信息"]
        },
        {
            "name": "发送欢迎邮件",
            "description": "向新用户发送欢迎邮件",
            "task_type": "function",
            "config": {
                "module": "email_service",
                "function": "send_welcome_email",
                "kwargs": {
                    "user_email": "${user_email}",
                    "template": "${welcome_template}"
                }
            },
            "dependencies": ["创建用户账户"]
        }
    ]
}

result = await workflow_engine.execute({
    "action": "create_workflow",
    "workflow_config": workflow_config
})
```

### 2. 条件分支工作流

```python
workflow_config = {
    "name": "订单处理流程",
    "description": "根据订单金额执行不同的处理流程",
    "variables": {
        "order_amount": 1500,
        "high_value_threshold": 1000
    },
    "tasks": [
        {
            "name": "检查订单金额",
            "task_type": "condition",
            "config": {
                "condition": "${order_amount} > ${high_value_threshold}"
            },
            "dependencies": []
        },
        {
            "name": "高价值订单处理",
            "task_type": "function",
            "config": {
                "module": "order_service",
                "function": "process_high_value_order"
            },
            "dependencies": ["检查订单金额"],
            "conditions": {
                "condition_result": True
            }
        },
        {
            "name": "普通订单处理",
            "task_type": "function",
            "config": {
                "module": "order_service",
                "function": "process_normal_order"
            },
            "dependencies": ["检查订单金额"],
            "conditions": {
                "condition_result": False
            }
        },
        {
            "name": "发送确认通知",
            "task_type": "http_request",
            "config": {
                "url": "https://api.notification.com/send",
                "method": "POST",
                "data": {
                    "message": "订单处理完成",
                    "order_id": "${order_id}"
                }
            },
            "dependencies": ["高价值订单处理", "普通订单处理"]
        }
    ]
}
```

### 3. 并行处理工作流

```python
workflow_config = {
    "name": "数据同步流程",
    "description": "并行同步多个数据源",
    "tasks": [
        {
            "name": "初始化同步",
            "task_type": "script",
            "config": {
                "type": "shell",
                "script": "echo 'Starting data synchronization...'"
            },
            "dependencies": []
        },
        {
            "name": "同步用户数据",
            "task_type": "http_request",
            "config": {
                "url": "https://api.users.com/sync",
                "method": "POST"
            },
            "dependencies": ["初始化同步"]
        },
        {
            "name": "同步订单数据",
            "task_type": "http_request",
            "config": {
                "url": "https://api.orders.com/sync",
                "method": "POST"
            },
            "dependencies": ["初始化同步"]
        },
        {
            "name": "同步产品数据",
            "task_type": "http_request",
            "config": {
                "url": "https://api.products.com/sync",
                "method": "POST"
            },
            "dependencies": ["初始化同步"]
        },
        {
            "name": "生成同步报告",
            "task_type": "function",
            "config": {
                "module": "report_service",
                "function": "generate_sync_report"
            },
            "dependencies": ["同步用户数据", "同步订单数据", "同步产品数据"]
        }
    ]
}
```

### 4. 工作流执行和监控

```python
# 启动工作流
start_result = await workflow_engine.execute({
    "action": "start_workflow",
    "workflow_id": workflow_id,
    "variables": {
        "user_data": {"name": "张三", "email": "<EMAIL>"},
        "order_id": "ORD-12345"
    }
})

# 监控工作流状态
while True:
    status_result = await workflow_engine.execute({
        "action": "get_workflow_status",
        "workflow_id": workflow_id
    })
    
    if status_result.success:
        status = status_result.result
        print(f"工作流状态: {status['workflow_status']}")
        print(f"任务进度: {status['task_status_counts']}")
        
        if status['workflow_status'] in ['completed', 'failed', 'cancelled']:
            break
    
    await asyncio.sleep(2)

# 获取任务详情
tasks_result = await workflow_engine.execute({
    "action": "list_tasks",
    "workflow_id": workflow_id
})

if tasks_result.success:
    tasks = tasks_result.result["tasks"]
    for task in tasks:
        print(f"任务: {task['name']}")
        print(f"  状态: {task['status']}")
        print(f"  开始时间: {task['started_at']}")
        print(f"  完成时间: {task['completed_at']}")
        if task.get('error'):
            print(f"  错误: {task['error']}")
        if task.get('result'):
            print(f"  结果: {task['result']}")
```

### 5. 工作流管理

```python
# 列出所有工作流
all_workflows = await workflow_engine.execute({
    "action": "list_workflows"
})

# 按状态过滤工作流
running_workflows = await workflow_engine.execute({
    "action": "list_workflows",
    "status_filter": "running"
})

# 获取工作流详情
workflow_detail = await workflow_engine.execute({
    "action": "get_workflow",
    "workflow_id": workflow_id
})

# 停止工作流
stop_result = await workflow_engine.execute({
    "action": "stop_workflow",
    "workflow_id": workflow_id
})
```

## 变量系统

### 变量定义和使用

工作流支持动态变量系统，可以在任务配置中使用 `${variable_name}` 语法：

```python
# 在工作流级别定义变量
"variables": {
    "api_base_url": "https://api.example.com",
    "api_key": "your-api-key",
    "batch_size": 100
}

# 在任务中使用变量
"config": {
    "url": "${api_base_url}/users",
    "headers": {
        "Authorization": "Bearer ${api_key}"
    },
    "data": {
        "limit": "${batch_size}"
    }
}
```

### 运行时变量传递

```python
# 启动工作流时传递变量
await workflow_engine.execute({
    "action": "start_workflow",
    "workflow_id": workflow_id,
    "variables": {
        "user_id": "12345",
        "operation_mode": "production",
        "notification_enabled": True
    }
})
```

## 错误处理和重试

### 任务重试配置

```python
{
    "name": "可能失败的任务",
    "task_type": "http_request",
    "config": {
        "url": "https://unreliable-api.com/data"
    },
    "max_retries": 3,           # 最大重试次数
    "timeout_seconds": 30       # 任务超时时间
}
```

### 条件执行

```python
{
    "name": "条件任务",
    "task_type": "script",
    "config": {
        "script": "echo 'Conditional execution'"
    },
    "dependencies": ["前置任务"],
    "conditions": {
        "environment": "production",
        "feature_enabled": True
    }
}
```

## 最佳实践

### 1. 工作流设计原则

- **模块化**：将复杂流程分解为独立的任务
- **幂等性**：确保任务可以安全地重复执行
- **错误处理**：为关键任务设置适当的重试和超时
- **监控**：添加适当的日志和状态检查点

### 2. 性能优化

```python
# 配置合理的并发数量
config = WorkflowEngineConfig(
    max_concurrent_workflows=10,    # 根据系统资源调整
    default_task_timeout=300        # 设置合理的默认超时
)

# 优化任务依赖关系
# 避免不必要的串行执行，尽可能并行处理
```

### 3. 安全考虑

- **变量验证**：验证输入变量的格式和内容
- **权限控制**：确保任务执行的权限最小化
- **敏感信息**：避免在配置中硬编码敏感信息

## 故障排除

### 常见问题

1. **工作流无法启动**
   - 检查工作流状态是否为pending
   - 验证任务配置是否正确
   - 确认依赖关系是否合理

2. **任务执行失败**
   - 查看任务错误信息
   - 检查网络连接和权限
   - 验证变量替换是否正确

3. **工作流卡住**
   - 检查任务依赖关系
   - 查看是否有循环依赖
   - 确认所有依赖任务都能正常完成

### 调试技巧

```python
# 启用详细日志
from ai_agent_framework.utils.logging_system import logging_system
logging_system.configure(level="DEBUG")

# 查看工作流状态
status_result = await workflow_engine.execute({
    "action": "get_workflow_status",
    "workflow_id": workflow_id
})

# 查看任务详情
tasks_result = await workflow_engine.execute({
    "action": "list_tasks",
    "workflow_id": workflow_id
})

# 检查特定任务
for task in tasks_result.result["tasks"]:
    if task["status"] == "failed":
        print(f"失败任务: {task['name']}")
        print(f"错误信息: {task['error']}")
        print(f"重试次数: {task['retry_count']}")
```

## 更多资源

- [AI Agent Framework 完整文档](./README.md)
- [业务流程自动化最佳实践](./业务流程自动化最佳实践.md)
- [工作流模板库](./workflow_templates/)
