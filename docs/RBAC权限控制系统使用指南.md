# RBAC权限控制系统使用指南

## 概述

AI Agent Framework 现已集成完整的基于角色的访问控制（RBAC）系统，提供企业级的权限管理功能。RBAC系统支持：

- **用户管理** - 创建和管理用户账户
- **角色管理** - 定义和管理用户角色
- **权限控制** - 细粒度的权限分配和检查
- **角色继承** - 支持角色层次结构和权限继承
- **条件权限** - 基于时间、属性等条件的动态权限
- **审计日志** - 完整的权限操作审计记录

## 核心概念

### 权限（Permission）
权限定义了对特定资源执行特定操作的能力。每个权限包含：
- **资源类型**：用户、角色、工具、数据等
- **操作类型**：读取、写入、删除、执行、管理等
- **资源模式**：支持通配符的资源匹配规则
- **条件**：额外的权限条件（时间、属性等）

### 角色（Role）
角色是权限的集合，可以分配给用户。角色支持：
- **权限组合**：一个角色可以包含多个权限
- **角色继承**：子角色可以继承父角色的所有权限
- **系统角色**：预定义的不可删除角色

### 用户（User）
用户是权限系统的主体，可以：
- **分配角色**：用户可以拥有多个角色
- **直接权限**：除角色权限外，还可以直接分配权限
- **超级用户**：拥有所有权限的特殊用户

## 快速开始

### 基础使用示例

```python
import asyncio
from ai_agent_framework.tools import RBACTool
from ai_agent_framework.security.rbac import ResourceType, PermissionType

async def basic_rbac_example():
    # 创建RBAC工具
    rbac = RBACTool()
    
    # 1. 创建用户
    result = await rbac.execute({
        "action": "create_user",
        "user_id": "alice",
        "username": "Alice Smith",
        "email": "<EMAIL>",
        "roles": ["user"]  # 分配默认用户角色
    })
    
    # 2. 检查权限
    result = await rbac.execute({
        "action": "check_access",
        "user_id": "alice",
        "resource_id": "document_001",
        "resource_type": "data",
        "permission_type": "read"
    })
    
    if result.success and result.result["granted"]:
        print("用户有权限访问该资源")
    else:
        print(f"访问被拒绝: {result.result['reason']}")

# 运行示例
asyncio.run(basic_rbac_example())
```

## 支持的操作

### 用户管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `create_user` | 创建新用户 | `user_id`, `username` |
| `assign_role` | 为用户分配角色 | `user_id`, `role_id` |
| `revoke_role` | 撤销用户角色 | `user_id`, `role_id` |
| `assign_permission` | 为用户直接分配权限 | `user_id`, `permission_id` |
| `get_user_info` | 获取用户详细信息 | `user_id` |
| `list_users` | 列出所有用户 | 无 |

### 角色管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `create_role` | 创建新角色 | `role_id`, `name`, `description` |
| `assign_role_permission` | 为角色分配权限 | `role_id`, `permission_id` |
| `get_role_info` | 获取角色信息 | `role_id` |
| `list_roles` | 列出所有角色 | 无 |

### 权限管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `create_permission` | 创建新权限 | `permission_id`, `name`, `description`, `resource_type`, `permission_type` |
| `list_permissions` | 列出所有权限 | 无 |

### 访问控制操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `check_access` | 检查访问权限 | `user_id`, `resource_id`, `resource_type`, `permission_type` |

## 详细使用指南

### 1. 用户管理

#### 创建用户
```python
# 创建普通用户
result = await rbac.execute({
    "action": "create_user",
    "user_id": "john_doe",
    "username": "John Doe",
    "email": "<EMAIL>",
    "roles": ["user", "data_analyst"]
})

# 创建管理员用户
result = await rbac.execute({
    "action": "create_user",
    "user_id": "admin_user",
    "username": "Admin User",
    "email": "<EMAIL>",
    "roles": ["admin"],
    "is_superuser": False
})
```

#### 角色分配
```python
# 为用户分配新角色
result = await rbac.execute({
    "action": "assign_role",
    "user_id": "john_doe",
    "role_id": "project_manager"
})

# 撤销用户角色
result = await rbac.execute({
    "action": "revoke_role",
    "user_id": "john_doe",
    "role_id": "data_analyst"
})
```

### 2. 角色管理

#### 创建角色
```python
# 创建基础角色
result = await rbac.execute({
    "action": "create_role",
    "role_id": "content_editor",
    "name": "内容编辑者",
    "description": "可以创建和编辑内容",
    "permissions": ["read_all", "write_own_data"]
})

# 创建继承角色
result = await rbac.execute({
    "action": "create_role",
    "role_id": "senior_editor",
    "name": "高级编辑者",
    "description": "高级内容编辑者，拥有更多权限",
    "permissions": ["manage_content"],
    "parent_roles": ["content_editor"]  # 继承内容编辑者的权限
})
```

### 3. 权限管理

#### 创建权限
```python
# 创建基础权限
result = await rbac.execute({
    "action": "create_permission",
    "permission_id": "edit_articles",
    "name": "编辑文章",
    "description": "允许编辑文章内容",
    "resource_type": "data",
    "permission_type": "write",
    "resource_pattern": "article:*"
})

# 创建条件权限
result = await rbac.execute({
    "action": "create_permission",
    "permission_id": "night_maintenance",
    "name": "夜间维护",
    "description": "只允许在夜间进行系统维护",
    "resource_type": "system",
    "permission_type": "admin",
    "resource_pattern": "maintenance:*",
    "conditions": {
        "time_range": {
            "start": 22,  # 22:00
            "end": 6      # 06:00
        }
    }
})
```

### 4. 访问控制检查

#### 基础权限检查
```python
# 检查用户是否可以读取特定资源
result = await rbac.execute({
    "action": "check_access",
    "user_id": "john_doe",
    "resource_id": "article:tech_news_001",
    "resource_type": "data",
    "permission_type": "read"
})

if result.success:
    access_info = result.result
    if access_info["granted"]:
        print(f"访问允许: {access_info['reason']}")
        print(f"匹配的权限: {access_info['matched_permissions']}")
    else:
        print(f"访问拒绝: {access_info['reason']}")
```

#### 带上下文的权限检查
```python
# 检查带有上下文信息的权限
result = await rbac.execute({
    "action": "check_access",
    "user_id": "maintenance_user",
    "resource_id": "maintenance:database_backup",
    "resource_type": "system",
    "permission_type": "admin",
    "context": {
        "current_time": "2025-08-28T23:30:00",
        "user_attributes": {
            "department": "IT",
            "clearance_level": "high"
        }
    }
})
```

## 预定义角色和权限

### 系统角色

| 角色ID | 名称 | 描述 | 权限 |
|--------|------|------|------|
| `guest` | 访客 | 基础访客角色 | 只读权限 |
| `user` | 普通用户 | 标准用户角色 | 读取、写入自己的数据、执行工具 |
| `agent_manager` | 代理管理员 | AI代理管理员 | 用户权限 + 管理AI代理 |
| `admin` | 系统管理员 | 系统管理员 | 所有权限 |

### 系统权限

| 权限ID | 名称 | 资源类型 | 操作类型 | 描述 |
|--------|------|----------|----------|------|
| `read_all` | 读取所有资源 | system | read | 允许读取所有类型的资源 |
| `write_own_data` | 写入自己的数据 | data | write | 允许写入用户自己的数据 |
| `admin_all` | 管理所有资源 | system | admin | 允许管理所有类型的资源 |
| `execute_tools` | 执行工具 | tool | execute | 允许执行AI工具 |
| `manage_agents` | 管理AI代理 | agent | manage | 允许创建和管理AI代理 |

## 高级功能

### 1. 角色继承

```python
# 创建角色层次结构
# 员工 -> 经理 -> 总监

# 基础员工角色
await rbac.execute({
    "action": "create_role",
    "role_id": "employee",
    "name": "员工",
    "description": "基础员工角色",
    "permissions": ["read_all", "write_own_data"]
})

# 经理角色（继承员工权限）
await rbac.execute({
    "action": "create_role",
    "role_id": "manager",
    "name": "经理",
    "description": "部门经理角色",
    "permissions": ["execute_tools"],
    "parent_roles": ["employee"]
})

# 总监角色（继承经理权限）
await rbac.execute({
    "action": "create_role",
    "role_id": "director",
    "name": "总监",
    "description": "部门总监角色",
    "permissions": ["manage_agents"],
    "parent_roles": ["manager"]
})
```

### 2. 条件权限

```python
# 创建基于时间的权限
await rbac.execute({
    "action": "create_permission",
    "permission_id": "business_hours_access",
    "name": "工作时间访问",
    "description": "只允许在工作时间访问",
    "resource_type": "system",
    "permission_type": "read",
    "conditions": {
        "time_range": {
            "start": 9,   # 09:00
            "end": 18     # 18:00
        }
    }
})

# 创建基于用户属性的权限
await rbac.execute({
    "action": "create_permission",
    "permission_id": "high_clearance_access",
    "name": "高级权限访问",
    "description": "需要高级安全许可",
    "resource_type": "data",
    "permission_type": "read",
    "resource_pattern": "classified:*",
    "conditions": {
        "user_attributes": {
            "clearance_level": "high",
            "department": "security"
        }
    }
})
```

### 3. 权限缓存管理

```python
# 清除特定用户的权限缓存
await rbac.execute({
    "action": "clear_cache",
    "user_id": "john_doe"
})

# 清除所有权限缓存
await rbac.execute({
    "action": "clear_cache"
})
```

## 最佳实践

### 1. 权限设计原则

- **最小权限原则**：只授予完成任务所需的最小权限
- **职责分离**：将不同职责分配给不同角色
- **权限继承**：使用角色继承减少权限管理复杂性
- **定期审查**：定期审查和更新权限分配

### 2. 角色设计建议

```python
# 推荐的角色层次结构
# 基础角色
"guest" -> "user" -> "power_user" -> "admin"

# 功能角色
"content_viewer" -> "content_editor" -> "content_manager"
"data_analyst" -> "data_scientist" -> "data_admin"
"tool_user" -> "tool_developer" -> "tool_admin"
```

### 3. 安全考虑

- **审计日志**：启用审计日志记录所有权限操作
- **权限验证**：在每个关键操作前进行权限检查
- **缓存管理**：合理设置缓存TTL，平衡性能和安全性
- **错误处理**：妥善处理权限检查失败的情况

## 集成示例

### 与现有认证系统集成

```python
from ai_agent_framework.security import AuthManager
from ai_agent_framework.tools import RBACTool

class SecureService:
    def __init__(self):
        self.auth = AuthManager()
        self.rbac = RBACTool()
    
    async def secure_operation(self, token: str, resource_id: str, operation: str):
        # 1. 验证用户身份
        user_info = await self.auth.verify_token(token)
        if not user_info:
            raise PermissionError("无效的认证令牌")
        
        # 2. 检查权限
        result = await self.rbac.execute({
            "action": "check_access",
            "user_id": user_info["user_id"],
            "resource_id": resource_id,
            "resource_type": "data",
            "permission_type": operation
        })
        
        if not result.success or not result.result["granted"]:
            raise PermissionError(f"权限不足: {result.result.get('reason', '未知原因')}")
        
        # 3. 执行操作
        return await self.perform_operation(resource_id, operation)
```

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查用户是否存在且处于活跃状态
   - 验证角色和权限配置是否正确
   - 确认资源模式匹配规则

2. **角色继承不生效**
   - 检查父角色是否存在且处于活跃状态
   - 验证是否存在循环继承
   - 清除权限缓存后重试

3. **性能问题**
   - 调整权限缓存TTL设置
   - 优化角色继承层次结构
   - 减少不必要的权限检查

### 调试技巧

```python
# 启用详细日志
from ai_agent_framework.utils.logging_system import logging_system
logging_system.configure(level="DEBUG")

# 获取用户详细权限信息
result = await rbac.execute({
    "action": "get_user_info",
    "user_id": "debug_user"
})

print("用户权限详情:", result.result)
```

## 更多资源

- [AI Agent Framework 完整文档](./README.md)
- [安全最佳实践指南](./安全最佳实践指南.md)
- [API参考文档](./API参考文档.md)
