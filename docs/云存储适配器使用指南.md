# 云存储适配器使用指南

## 概述

AI Agent Framework 现已支持多种云存储服务，包括：
- **AWS S3** - 亚马逊简单存储服务
- **阿里云OSS** - 阿里云对象存储服务
- **腾讯云COS** - 腾讯云对象存储服务

通过统一的文件存储工具接口，您可以轻松地在不同云存储服务之间切换，而无需修改业务逻辑代码。

## 功能特性

### 🚀 核心功能
- **统一接口**：所有云存储服务使用相同的API接口
- **异步操作**：支持高性能的异步文件操作
- **元数据支持**：支持自定义文件元数据
- **预签名URL**：支持生成临时访问链接
- **批量操作**：支持批量文件上传、下载和管理

### 🔧 高级功能
- **自动重试**：内置网络错误重试机制
- **进度回调**：支持文件传输进度监控
- **分片上传**：自动处理大文件分片上传
- **CDN加速**：支持CDN加速访问
- **安全传输**：支持SSL/TLS加密传输

## 安装依赖

根据您要使用的云存储服务，安装相应的依赖库：

```bash
# AWS S3
pip install boto3

# 阿里云OSS
pip install oss2

# 腾讯云COS
pip install cos-python-sdk-v5
```

## 快速开始

### AWS S3 使用示例

```python
import asyncio
from ai_agent_framework.tools import FileStorageTool, StorageType

async def s3_example():
    # 配置AWS S3
    s3_config = {
        "access_key_id": "your-access-key-id",
        "secret_access_key": "your-secret-access-key",
        "bucket_name": "your-bucket-name",
        "region": "us-east-1"
    }
    
    # 创建存储工具
    storage = FileStorageTool(
        storage_type=StorageType.AWS_S3,
        connection_config=s3_config
    )
    
    # 连接到S3
    await storage.execute({"action": "connect"})
    
    # 上传文件
    result = await storage.execute({
        "action": "upload",
        "local_path": "local_file.txt",
        "remote_path": "folder/remote_file.txt",
        "metadata": {
            "author": "AI Agent",
            "version": "1.0"
        }
    })
    
    if result.success:
        print("文件上传成功！")
    
    # 断开连接
    await storage.execute({"action": "disconnect"})

# 运行示例
asyncio.run(s3_example())
```

### 阿里云OSS 使用示例

```python
import asyncio
from ai_agent_framework.tools import FileStorageTool, StorageType

async def oss_example():
    # 配置阿里云OSS
    oss_config = {
        "access_key_id": "your-access-key-id",
        "access_key_secret": "your-access-key-secret",
        "bucket_name": "your-bucket-name",
        "endpoint": "oss-cn-hangzhou.aliyuncs.com"
    }
    
    # 创建存储工具
    storage = FileStorageTool(
        storage_type=StorageType.ALIYUN_OSS,
        connection_config=oss_config
    )
    
    # 连接到OSS
    await storage.execute({"action": "connect"})
    
    # 生成预签名URL
    result = await storage.execute({
        "action": "generate_url",
        "remote_path": "folder/file.txt",
        "expiration": 3600,  # 1小时有效期
        "method": "GET"
    })
    
    if result.success:
        print(f"预签名URL: {result.result['url']}")
    
    # 断开连接
    await storage.execute({"action": "disconnect"})

# 运行示例
asyncio.run(oss_example())
```

### 腾讯云COS 使用示例

```python
import asyncio
from ai_agent_framework.tools import FileStorageTool, StorageType

async def cos_example():
    # 配置腾讯云COS
    cos_config = {
        "secret_id": "your-secret-id",
        "secret_key": "your-secret-key",
        "bucket_name": "your-bucket-name-1234567890",  # 需要包含APPID
        "region": "ap-beijing"
    }
    
    # 创建存储工具
    storage = FileStorageTool(
        storage_type=StorageType.TENCENT_COS,
        connection_config=cos_config
    )
    
    # 连接到COS
    await storage.execute({"action": "connect"})
    
    # 列出文件
    result = await storage.execute({
        "action": "list",
        "prefix": "images/",
        "max_files": 100
    })
    
    if result.success:
        files = result.result["files"]
        print(f"找到 {len(files)} 个文件")
        for file_info in files:
            print(f"- {file_info['name']} ({file_info['size']} 字节)")
    
    # 断开连接
    await storage.execute({"action": "disconnect"})

# 运行示例
asyncio.run(cos_example())
```

## 支持的操作

### 基础操作

| 操作 | 描述 | 参数 |
|------|------|------|
| `connect` | 连接到云存储服务 | 无 |
| `disconnect` | 断开连接 | 无 |
| `upload` | 上传文件 | `local_path`, `remote_path`, `metadata` |
| `download` | 下载文件 | `remote_path`, `local_path` |
| `delete` | 删除文件 | `remote_path` |
| `exists` | 检查文件是否存在 | `remote_path` |
| `info` | 获取文件信息 | `remote_path` |
| `list` | 列出文件 | `prefix`, `max_files` |

### 高级操作

| 操作 | 描述 | 参数 |
|------|------|------|
| `copy` | 复制文件 | `source_path`, `destination_path` |
| `move` | 移动文件 | `source_path`, `destination_path` |
| `generate_url` | 生成预签名URL | `remote_path`, `expiration`, `method` |
| `create_dir` | 创建目录 | `directory_path` |
| `delete_dir` | 删除目录 | `directory_path` |

## 配置说明

### AWS S3 配置参数

```python
s3_config = {
    "access_key_id": "AWS访问密钥ID",
    "secret_access_key": "AWS秘密访问密钥",
    "bucket_name": "S3存储桶名称",
    "region": "AWS区域（如：us-east-1）",
    "endpoint_url": "自定义端点URL（可选）"
}
```

### 阿里云OSS 配置参数

```python
oss_config = {
    "access_key_id": "阿里云AccessKey ID",
    "access_key_secret": "阿里云AccessKey Secret",
    "bucket_name": "OSS存储桶名称",
    "endpoint": "OSS端点地址（如：oss-cn-hangzhou.aliyuncs.com）"
}
```

### 腾讯云COS 配置参数

```python
cos_config = {
    "secret_id": "腾讯云SecretId",
    "secret_key": "腾讯云SecretKey",
    "bucket_name": "COS存储桶名称（需包含APPID）",
    "region": "COS区域（如：ap-beijing）"
}
```

## 最佳实践

### 1. 错误处理

```python
async def safe_upload(storage, local_path, remote_path):
    """安全的文件上传，包含错误处理"""
    try:
        result = await storage.execute({
            "action": "upload",
            "local_path": local_path,
            "remote_path": remote_path
        })
        
        if result.success:
            print(f"上传成功: {remote_path}")
            return True
        else:
            print(f"上传失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"上传过程中发生异常: {e}")
        return False
```

### 2. 批量操作

```python
async def batch_upload(storage, file_list):
    """批量上传文件"""
    results = []
    
    for local_path, remote_path in file_list:
        result = await storage.execute({
            "action": "upload",
            "local_path": local_path,
            "remote_path": remote_path
        })
        results.append((remote_path, result.success))
    
    return results
```

### 3. 连接管理

```python
class StorageManager:
    """存储管理器，自动处理连接生命周期"""
    
    def __init__(self, storage_type, config):
        self.storage = FileStorageTool(storage_type, config)
        self.connected = False
    
    async def __aenter__(self):
        result = await self.storage.execute({"action": "connect"})
        if result.success:
            self.connected = True
            return self.storage
        else:
            raise ConnectionError(f"连接失败: {result.error}")
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.connected:
            await self.storage.execute({"action": "disconnect"})

# 使用示例
async def with_storage():
    config = {...}  # 您的配置
    
    async with StorageManager(StorageType.AWS_S3, config) as storage:
        # 在这里执行存储操作
        result = await storage.execute({
            "action": "upload",
            "local_path": "file.txt",
            "remote_path": "remote/file.txt"
        })
    # 连接会自动断开
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证访问凭据是否正确
   - 确认存储桶名称和区域设置

2. **上传失败**
   - 检查文件是否存在
   - 验证文件权限
   - 确认存储空间是否充足

3. **依赖库问题**
   - 确保已安装正确版本的依赖库
   - 检查Python版本兼容性

### 调试技巧

启用详细日志来诊断问题：

```python
from ai_agent_framework.utils.logging_system import logging_system

# 启用DEBUG级别日志
logging_system.configure(level="DEBUG")
```

## 性能优化

### 1. 并发上传

```python
import asyncio

async def concurrent_upload(storage, file_list, max_concurrent=5):
    """并发上传多个文件"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def upload_with_semaphore(local_path, remote_path):
        async with semaphore:
            return await storage.execute({
                "action": "upload",
                "local_path": local_path,
                "remote_path": remote_path
            })
    
    tasks = [
        upload_with_semaphore(local, remote)
        for local, remote in file_list
    ]
    
    return await asyncio.gather(*tasks)
```

### 2. 连接复用

保持长连接以减少连接开销：

```python
# 创建一个长期存在的存储实例
storage = FileStorageTool(StorageType.AWS_S3, config)
await storage.execute({"action": "connect"})

# 执行多个操作...
# ...

# 最后断开连接
await storage.execute({"action": "disconnect"})
```

## 安全注意事项

1. **凭据管理**
   - 不要在代码中硬编码访问凭据
   - 使用环境变量或配置文件存储敏感信息
   - 定期轮换访问密钥

2. **权限控制**
   - 遵循最小权限原则
   - 为不同用途创建不同的访问密钥
   - 定期审查和更新权限设置

3. **数据加密**
   - 启用传输加密（HTTPS/TLS）
   - 考虑启用服务端加密
   - 对敏感数据进行客户端加密

## 更多资源

- [AWS S3 官方文档](https://docs.aws.amazon.com/s3/)
- [阿里云OSS 官方文档](https://help.aliyun.com/product/31815.html)
- [腾讯云COS 官方文档](https://cloud.tencent.com/document/product/436)
- [AI Agent Framework 完整文档](./README.md)
