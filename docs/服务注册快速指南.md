# AI Agent Framework 服务注册快速指南

## 概述

AI Agent Framework 提供了强大的服务注册功能，可以将现有的微服务或API快速注册为AI Agent可调用的工具。本指南将帮助您快速上手服务注册功能。

## 核心特性

- 🔍 **自动服务发现**：支持OpenAPI/Swagger规范自动发现
- 🔐 **多种认证方式**：API Key、OAuth2、JWT、Basic Auth等
- 🛠️ **动态工具生成**：自动创建可调用的工具
- ✅ **全面验证测试**：连接性、认证、性能等多维度验证
- 📊 **详细报告**：生成完整的验证和性能报告

## 快速开始

### 1. 基础环境准备

```python
from ai_agent_framework.tools.service_registry_tool import ServiceRegistryTool
from ai_agent_framework.tools.service_discovery import ServiceDiscoveryTool
from ai_agent_framework.tools.service_auth_manager import ServiceAuthManager
from ai_agent_framework.tools.service_adapter import ServiceToolFactory
from ai_agent_framework.utils.tool_registry import ToolRegistry

# 创建核心组件
service_registry = ServiceRegistryTool()
service_discovery = ServiceDiscoveryTool()
auth_manager = ServiceAuthManager()
tool_factory = ServiceToolFactory(auth_manager)
tool_registry = ToolRegistry()
```

### 2. 注册一个简单的REST API服务

```python
# 定义服务配置
service_config = {
    "name": "example_api",
    "description": "示例API服务",
    "base_url": "https://api.example.com",
    "service_type": "rest_api",
    "auth": {"type": "none"},  # 无需认证
    "endpoints": [
        {
            "name": "get_data",
            "path": "/data",
            "method": "GET",
            "description": "获取数据",
            "requires_auth": False,
            "parameters": {
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "integer",
                        "description": "返回数据数量限制"
                    }
                },
                "required": []
            }
        }
    ]
}

# 注册服务
result = await service_registry.execute({
    "action": "register",
    "service_config": service_config
})

if result.success:
    print("✅ 服务注册成功!")
else:
    print(f"❌ 服务注册失败: {result.error}")
```

### 3. 创建和使用动态工具

```python
# 从服务配置创建工具
from ai_agent_framework.tools.service_registry_tool import ServiceConfig

config = ServiceConfig(**service_config)
tools = tool_factory.create_tools_from_config(config)

# 注册工具到工具注册表
for tool in tools:
    tool_registry.register_tool(tool)
    print(f"已注册工具: {tool.name}")

# 使用工具
get_data_tool = tool_registry.get_tool("example_api_get_data")
if get_data_tool:
    result = await get_data_tool.execute({"limit": 10})
    if result.success:
        print(f"工具调用成功: {result.result}")
    else:
        print(f"工具调用失败: {result.error}")
```

## 服务发现

### 自动发现OpenAPI服务

```python
# 发现OpenAPI服务
discovery_result = await service_discovery.execute({
    "action": "discover_openapi",
    "target": "https://petstore.swagger.io/v2",
    "options": {
        "generate_examples": True
    }
})

if discovery_result.success:
    service_config = discovery_result.result.get("service_config", {})
    print(f"发现服务: {service_config.get('name')}")
    print(f"端点数量: {len(service_config.get('endpoints', []))}")
```

### 网络扫描发现服务

```python
# 扫描本地网络服务
scan_result = await service_discovery.execute({
    "action": "scan",
    "target": "localhost",
    "options": {
        "port_range": "8000-9000",
        "timeout": 5
    }
})

if scan_result.success:
    services = scan_result.result.get("discovered_services", [])
    print(f"发现 {len(services)} 个本地服务")
```

## 认证配置

### API Key认证

```python
# 配置API Key认证
auth_result = await auth_manager.execute({
    "action": "store",
    "service_name": "my_api",
    "auth_type": "api_key",
    "credentials": {
        "api_key": "your-api-key-here",
        "api_key_header": "X-API-Key"
    }
})

# 在服务配置中使用认证
service_config = {
    "name": "my_api",
    "base_url": "https://api.example.com",
    "auth": {
        "type": "api_key",
        "api_key": "${API_KEY}",
        "api_key_header": "X-API-Key"
    },
    # ... 其他配置
}
```

### OAuth2认证

```python
# 配置OAuth2认证
auth_result = await auth_manager.execute({
    "action": "store",
    "service_name": "oauth_api",
    "auth_type": "oauth2",
    "credentials": {
        "client_id": "your-client-id",
        "client_secret": "your-client-secret",
        "token_url": "https://api.example.com/oauth/token",
        "scope": "read write"
    }
})

# 刷新OAuth2令牌
refresh_result = await auth_manager.execute({
    "action": "refresh",
    "service_name": "oauth_api"
})
```

## 服务验证

### 基础验证

```python
from ai_agent_framework.tools.service_validator import ServiceValidator

validator = ServiceValidator(auth_manager=auth_manager)

# 验证服务
validation_result = await validator.execute({
    "action": "validate_service",
    "service_config": service_config,
    "options": {
        "timeout": 30,
        "generate_report": True
    }
})

if validation_result.success:
    summary = validation_result.result.get("summary", {})
    print(f"验证成功率: {summary.get('success_rate')}%")
    print(f"报告文件: {validation_result.result.get('report_file')}")
```

### 性能测试

```python
# 执行性能测试
performance_result = await validator.execute({
    "action": "performance_test",
    "service_config": service_config,
    "options": {
        "performance_iterations": 10
    }
})

if performance_result.success:
    metrics = performance_result.result.get("performance_metrics", {})
    print(f"平均响应时间: {metrics.get('avg_response_time')}ms")
    print(f"成功率: {metrics.get('success_rate')}%")
```

## 配置文件方式

### 创建服务配置文件

创建 `configs/services/my_service.yaml`：

```yaml
name: "my_service"
description: "我的API服务"
base_url: "https://api.example.com"
service_type: "rest_api"
version: "1.0.0"

# 认证配置
auth:
  type: "api_key"
  api_key: "${API_KEY}"
  api_key_header: "X-API-Key"

# 端点配置
endpoints:
  - name: "get_users"
    path: "/users"
    method: "GET"
    description: "获取用户列表"
    requires_auth: true
    parameters:
      type: "object"
      properties:
        page:
          type: "integer"
          description: "页码"
        limit:
          type: "integer"
          description: "每页数量"
      required: []

# 全局配置
timeout: 30.0
max_retries: 3
headers:
  "Content-Type": "application/json"
  "User-Agent": "AI-Agent-Framework/1.0"

# 健康检查
health_check_path: "/health"

# 标签和元数据
tags:
  - "api"
  - "production"
metadata:
  team: "backend-team"
  contact: "<EMAIL>"
```

### 从配置文件注册服务

```python
# 从配置文件注册
register_result = await service_registry.execute({
    "action": "register",
    "config_file": "configs/services/my_service.yaml"
})

# 从配置文件创建工具
tools = tool_factory.create_tools_from_config_file("configs/services/my_service.yaml")
```

## 最佳实践

### 1. 服务配置组织

```
configs/
└── services/
    ├── production/
    │   ├── user_service.yaml
    │   └── order_service.yaml
    ├── staging/
    │   ├── user_service.yaml
    │   └── order_service.yaml
    └── development/
        ├── user_service.yaml
        └── order_service.yaml
```

### 2. 环境变量管理

```bash
# .env 文件
API_KEY=your-api-key
OAUTH_CLIENT_ID=your-client-id
OAUTH_CLIENT_SECRET=your-client-secret
DATABASE_URL=postgresql://user:pass@localhost/db
```

### 3. 错误处理

```python
try:
    result = await service_registry.execute({
        "action": "register",
        "service_config": service_config
    })
    
    if not result.success:
        logger.error(f"服务注册失败: {result.error}")
        # 处理失败情况
        
except Exception as e:
    logger.exception(f"服务注册异常: {str(e)}")
    # 处理异常情况
```

### 4. 监控和日志

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 定期健康检查
async def periodic_health_check():
    services = await service_registry.execute({"action": "list"})
    
    for service in services.result.get("services", []):
        if not service.get("is_healthy"):
            logger.warning(f"服务不健康: {service['name']}")
```

## 常见问题

### Q: 如何处理需要复杂认证的服务？

A: 使用自定义认证类型：

```python
auth_config = {
    "type": "custom",
    "custom_headers": {
        "Authorization": "Bearer your-token",
        "X-Custom-Header": "custom-value",
        "X-Timestamp": "2024-01-01T00:00:00Z"
    }
}
```

### Q: 如何处理GraphQL服务？

A: 设置服务类型为GraphQL：

```python
service_config = {
    "service_type": "graphql",
    "endpoints": [
        {
            "name": "query_users",
            "path": "/graphql",
            "method": "POST",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "GraphQL查询"},
                    "variables": {"type": "object", "description": "查询变量"}
                },
                "required": ["query"]
            }
        }
    ]
}
```

### Q: 如何批量注册多个服务？

A: 使用工具工厂的批量注册功能：

```python
service_configs = [config1, config2, config3]
registered_count = await tool_factory.create_and_register_tools(
    service_configs, 
    tool_registry
)
print(f"成功注册 {registered_count} 个工具")
```

## 下一步

- 查看 [API参考文档](./API参考文档.md)
- 学习 [高级配置](./高级配置指南.md)
- 了解 [安全最佳实践](./安全最佳实践.md)
- 参考 [示例代码](../examples/)

## 支持

如果您遇到问题或需要帮助，请：

1. 查看 [常见问题解答](./FAQ.md)
2. 查看 [故障排除指南](./故障排除指南.md)
3. 提交 [GitHub Issue](https://github.com/aier/ai-agent/issues)
4. 联系技术支持：<EMAIL>
