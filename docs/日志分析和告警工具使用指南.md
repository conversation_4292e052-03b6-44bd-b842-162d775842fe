# 日志分析和告警工具使用指南

## 概述

AI Agent Framework 现已集成完整的智能日志分析和告警工具，提供企业级的日志处理和监控功能。该工具支持：

- **智能模式识别** - 自动识别日志中的常见模式和异常
- **实时异常检测** - 检测错误率、性能异常、重复错误等
- **性能指标提取** - 自动提取响应时间、内存使用、CPU使用等指标
- **多级告警系统** - 支持多种严重程度的告警规则和动作
- **统计分析** - 提供详细的日志统计和趋势分析
- **自定义规则** - 支持自定义日志模式和告警规则

## 功能特性

### 🚀 核心功能
- **实时日志分析**：支持高性能的实时日志处理和分析
- **模式匹配**：基于正则表达式的智能模式识别
- **异常检测**：自动检测各种类型的系统异常
- **告警管理**：完整的告警生命周期管理

### 🔧 高级功能
- **性能监控**：自动提取和分析性能相关指标
- **趋势分析**：基于历史数据的趋势分析和预测
- **多维统计**：按组件、级别、时间等多维度统计
- **告警抑制**：智能告警抑制和去重机制

## 快速开始

### 基础使用示例

```python
import asyncio
from datetime import datetime
from ai_agent_framework.tools import LogAnalyzerTool, LogAnalyzerConfig

async def basic_log_analysis():
    # 创建日志分析工具
    config = LogAnalyzerConfig(
        buffer_size=10000,
        enable_anomaly_detection=True,
        enable_pattern_matching=True
    )
    
    log_analyzer = LogAnalyzerTool(config)
    
    # 分析日志
    log_data = {
        "timestamp": datetime.now().isoformat(),
        "level": "ERROR",
        "message": "Database connection failed: timeout after 30s",
        "component": "database_service",
        "source": "api_server",
        "metadata": {"timeout": 30, "retry_count": 3}
    }
    
    result = await log_analyzer.execute({
        "action": "analyze_log",
        "log_data": log_data
    })
    
    if result.success:
        analysis = result.result["analysis"]
        alerts = result.result["triggered_alerts"]
        
        print(f"匹配模式: {len(analysis['matched_patterns'])}")
        print(f"检测异常: {len(analysis['anomalies'])}")
        print(f"触发告警: {len(alerts)}")

# 运行示例
asyncio.run(basic_log_analysis())
```

## 支持的日志级别

| 级别 | 描述 | 用途 |
|------|------|------|
| `DEBUG` | 调试信息 | 开发调试、详细跟踪 |
| `INFO` | 一般信息 | 正常操作记录 |
| `WARNING` | 警告信息 | 潜在问题提醒 |
| `ERROR` | 错误信息 | 错误事件记录 |
| `CRITICAL` | 关键错误 | 严重系统故障 |

## 支持的操作

### 日志分析操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `analyze_log` | 分析单条日志 | `log_data` |
| `get_statistics` | 获取统计信息 | 可选 `time_window` |

### 模式管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `add_pattern` | 添加日志模式 | `pattern_config` |
| `remove_pattern` | 移除日志模式 | `pattern_id` |
| `list_patterns` | 列出所有模式 | 无 |

### 告警管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `get_alerts` | 获取活跃告警 | 无 |
| `acknowledge_alert` | 确认告警 | `alert_id`, `user_id` |
| `resolve_alert` | 解决告警 | `alert_id`, `user_id` |
| `get_alert_statistics` | 获取告警统计 | 无 |

## 详细使用指南

### 1. 日志分析

#### 基础日志分析
```python
# 分析错误日志
log_data = {
    "timestamp": "2025-08-28T10:30:00Z",
    "level": "ERROR",
    "message": "HTTP 500 Internal Server Error - /api/users",
    "component": "api_server",
    "source": "nginx",
    "metadata": {
        "status_code": 500,
        "endpoint": "/api/users",
        "response_time": 5000
    }
}

result = await log_analyzer.execute({
    "action": "analyze_log",
    "log_data": log_data
})

if result.success:
    analysis = result.result["analysis"]
    
    # 查看匹配的模式
    for pattern in analysis["matched_patterns"]:
        print(f"匹配模式: {pattern['pattern_name']}")
        print(f"类别: {pattern['category']}")
    
    # 查看检测到的异常
    for anomaly in analysis["anomalies"]:
        print(f"异常类型: {anomaly['type']}")
        print(f"描述: {anomaly['description']}")
    
    # 查看提取的指标
    for metric, value in analysis["metrics"].items():
        print(f"指标 {metric}: {value}")
```

#### 批量日志分析
```python
async def analyze_log_batch(log_entries):
    """批量分析日志"""
    results = []
    
    for log_data in log_entries:
        result = await log_analyzer.execute({
            "action": "analyze_log",
            "log_data": log_data
        })
        
        if result.success:
            results.append(result.result)
    
    return results

# 使用示例
log_batch = [
    {
        "timestamp": datetime.now().isoformat(),
        "level": "ERROR",
        "message": "Database query timeout",
        "component": "database"
    },
    {
        "timestamp": datetime.now().isoformat(),
        "level": "WARNING",
        "message": "High memory usage: 85%",
        "component": "system"
    }
]

results = await analyze_log_batch(log_batch)
```

### 2. 自定义日志模式

#### 添加自定义模式
```python
# 添加支付相关的错误模式
pattern_config = {
    "name": "支付失败",
    "pattern": r"payment.*failed|transaction.*error|billing.*failed",
    "description": "检测支付相关的失败事件",
    "category": "payment",
    "severity": "ERROR"
}

result = await log_analyzer.execute({
    "action": "add_pattern",
    "pattern_config": pattern_config
})

if result.success:
    pattern_id = result.result["pattern_id"]
    print(f"模式添加成功: {pattern_id}")
```

#### 管理模式
```python
# 列出所有模式
result = await log_analyzer.execute({
    "action": "list_patterns"
})

if result.success:
    patterns = result.result["patterns"]
    for pattern in patterns:
        print(f"模式: {pattern['name']}")
        print(f"  类别: {pattern['category']}")
        print(f"  匹配次数: {pattern['count']}")
        print(f"  最后匹配: {pattern['last_seen']}")

# 删除模式
result = await log_analyzer.execute({
    "action": "remove_pattern",
    "pattern_id": "pattern_id_here"
})
```

### 3. 告警管理

#### 查看活跃告警
```python
# 获取所有活跃告警
result = await log_analyzer.execute({
    "action": "get_alerts"
})

if result.success:
    alerts = result.result["alerts"]
    print(f"当前活跃告警: {len(alerts)} 个")
    
    for alert in alerts:
        print(f"告警: {alert['rule_name']}")
        print(f"  严重程度: {alert['severity']}")
        print(f"  状态: {alert['status']}")
        print(f"  消息: {alert['message']}")
        print(f"  触发时间: {alert['triggered_at']}")
        print(f"  触发次数: {alert['count']}")
```

#### 处理告警
```python
# 确认告警
result = await log_analyzer.execute({
    "action": "acknowledge_alert",
    "alert_id": "alert_id_here",
    "user_id": "admin_user"
})

if result.success:
    print("告警已确认")

# 解决告警
result = await log_analyzer.execute({
    "action": "resolve_alert",
    "alert_id": "alert_id_here",
    "user_id": "admin_user"
})

if result.success:
    print("告警已解决")
```

### 4. 统计分析

#### 获取日志统计
```python
# 获取最近1小时的统计
result = await log_analyzer.execute({
    "action": "get_statistics",
    "time_window": 3600  # 3600秒 = 1小时
})

if result.success:
    stats = result.result
    
    print(f"总日志数: {stats['total_logs']}")
    print(f"级别分布: {stats['level_distribution']}")
    print(f"组件分布: {stats['component_distribution']}")
    
    # 性能指标统计
    if stats['performance_metrics']:
        for metric, data in stats['performance_metrics'].items():
            print(f"{metric}:")
            print(f"  平均值: {data['avg']:.2f}")
            print(f"  最小值: {data['min']}")
            print(f"  最大值: {data['max']}")
            print(f"  样本数: {data['count']}")
```

#### 获取告警统计
```python
# 获取告警统计信息
result = await log_analyzer.execute({
    "action": "get_alert_statistics"
})

if result.success:
    stats = result.result
    
    print(f"总告警数: {stats['total_alerts']}")
    print(f"活跃告警: {stats['active_alerts']}")
    print(f"最近24小时: {stats['recent_alerts_24h']}")
    print(f"严重程度分布: {stats['severity_distribution']}")
    print(f"状态分布: {stats['status_distribution']}")
```

## 预定义模式和规则

### 默认日志模式

| 模式名称 | 类别 | 描述 |
|----------|------|------|
| 数据库连接错误 | database | 检测数据库连接相关错误 |
| 内存不足 | system | 检测内存耗尽问题 |
| HTTP 5xx错误 | http | 检测服务器内部错误 |
| 认证失败 | security | 检测认证和授权失败 |
| 文件系统错误 | filesystem | 检测文件系统相关错误 |
| 网络超时 | network | 检测网络连接超时 |

### 默认告警规则

| 规则名称 | 严重程度 | 触发条件 |
|----------|----------|----------|
| 高错误率告警 | HIGH | 错误率超过阈值 |
| 重复错误告警 | MEDIUM | 检测到重复错误 |
| 关键错误告警 | CRITICAL | 出现CRITICAL级别日志 |
| 性能异常告警 | MEDIUM | 响应时间异常 |

## 高级用法

### 1. 实时监控集成

```python
class RealTimeLogMonitor:
    """实时日志监控器"""
    
    def __init__(self):
        self.log_analyzer = LogAnalyzerTool()
        self.alert_handlers = []
    
    async def process_log_stream(self, log_stream):
        """处理日志流"""
        async for log_line in log_stream:
            # 解析日志
            log_data = self.parse_log_line(log_line)
            
            # 分析日志
            result = await self.log_analyzer.execute({
                "action": "analyze_log",
                "log_data": log_data
            })
            
            if result.success:
                # 处理触发的告警
                alerts = result.result["triggered_alerts"]
                for alert in alerts:
                    await self.handle_alert(alert)
    
    def parse_log_line(self, log_line):
        """解析日志行"""
        # 实现日志解析逻辑
        pass
    
    async def handle_alert(self, alert):
        """处理告警"""
        # 实现告警处理逻辑
        pass
```

### 2. 自定义异常检测

```python
class CustomAnomalyDetector:
    """自定义异常检测器"""
    
    def __init__(self, log_analyzer):
        self.log_analyzer = log_analyzer
    
    async def detect_business_anomalies(self, log_data):
        """检测业务异常"""
        # 分析日志
        result = await self.log_analyzer.execute({
            "action": "analyze_log",
            "log_data": log_data
        })
        
        if result.success:
            analysis = result.result["analysis"]
            
            # 自定义业务逻辑检测
            business_anomalies = []
            
            # 检测订单异常
            if "order" in log_data["message"].lower():
                if log_data["level"] == "ERROR":
                    business_anomalies.append({
                        "type": "order_processing_error",
                        "severity": "high",
                        "description": "订单处理出现错误"
                    })
            
            # 检测用户行为异常
            if "user" in log_data.get("metadata", {}):
                user_id = log_data["metadata"]["user"]
                # 实现用户行为异常检测逻辑
                pass
            
            return business_anomalies
```

### 3. 告警动作扩展

```python
class AlertActionHandler:
    """告警动作处理器"""
    
    async def execute_webhook_action(self, alert, webhook_config):
        """执行Webhook动作"""
        import aiohttp
        
        payload = {
            "alert_id": alert["id"],
            "rule_name": alert["rule_name"],
            "severity": alert["severity"],
            "message": alert["message"],
            "timestamp": alert["triggered_at"]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                webhook_config["url"],
                json=payload,
                headers=webhook_config.get("headers", {})
            ) as response:
                if response.status == 200:
                    print(f"Webhook调用成功: {webhook_config['url']}")
                else:
                    print(f"Webhook调用失败: {response.status}")
    
    async def send_email_alert(self, alert, email_config):
        """发送邮件告警"""
        # 集成邮件发送功能
        from ai_agent_framework.tools import EmailTool
        
        email_tool = EmailTool()
        
        await email_tool.execute({
            "action": "send",
            "to": email_config["recipients"],
            "subject": f"告警: {alert['rule_name']}",
            "body": f"""
            告警详情:
            - 规则: {alert['rule_name']}
            - 严重程度: {alert['severity']}
            - 消息: {alert['message']}
            - 触发时间: {alert['triggered_at']}
            """,
            "is_html": False
        })
```

## 最佳实践

### 1. 日志格式标准化

```python
# 推荐的日志格式
{
    "timestamp": "2025-08-28T10:30:00Z",
    "level": "ERROR",
    "message": "具体的错误描述",
    "component": "服务组件名称",
    "source": "日志来源",
    "user_id": "用户ID（如适用）",
    "session_id": "会话ID（如适用）",
    "request_id": "请求ID（如适用）",
    "metadata": {
        "error_code": "错误代码",
        "response_time": 1500,
        "additional_info": "其他信息"
    }
}
```

### 2. 模式设计原则

- **具体性**：模式应该足够具体以避免误报
- **覆盖性**：模式应该覆盖重要的错误类型
- **可维护性**：使用清晰的命名和描述
- **性能考虑**：避免过于复杂的正则表达式

### 3. 告警策略

- **分级告警**：根据严重程度设置不同的处理策略
- **告警抑制**：避免告警风暴，设置合理的冷却时间
- **告警聚合**：将相关告警进行聚合处理
- **自动恢复**：实现告警的自动恢复机制

## 故障排除

### 常见问题

1. **模式不匹配**
   - 检查正则表达式语法
   - 验证大小写敏感性设置
   - 确认日志消息格式

2. **告警未触发**
   - 检查告警规则配置
   - 验证日志级别匹配
   - 确认冷却时间设置

3. **性能问题**
   - 调整缓冲区大小
   - 优化正则表达式
   - 限制历史记录数量

### 调试技巧

```python
# 启用详细日志
from ai_agent_framework.utils.logging_system import logging_system
logging_system.configure(level="DEBUG")

# 测试模式匹配
pattern_config = {
    "name": "测试模式",
    "pattern": "your_pattern_here",
    "category": "test"
}

result = await log_analyzer.execute({
    "action": "add_pattern",
    "pattern_config": pattern_config
})

# 测试日志分析
test_log = {
    "timestamp": datetime.now().isoformat(),
    "level": "ERROR",
    "message": "your_test_message_here",
    "component": "test"
}

result = await log_analyzer.execute({
    "action": "analyze_log",
    "log_data": test_log
})

print("分析结果:", result.result)
```

## 更多资源

- [AI Agent Framework 完整文档](./README.md)
- [监控最佳实践指南](./监控最佳实践指南.md)
- [正则表达式参考](https://regex101.com/)
