# 定时任务调度工具使用指南

## 概述

AI Agent Framework 现已集成完整的定时任务调度工具，提供企业级的任务调度和管理功能。调度工具支持：

- **Cron表达式** - 支持标准的5位Cron表达式调度
- **固定间隔** - 按固定时间间隔执行任务
- **一次性任务** - 指定时间执行的一次性任务
- **多种任务类型** - HTTP请求、Shell命令、Python函数等
- **任务管理** - 暂停、恢复、删除、状态监控
- **执行记录** - 完整的任务执行历史和状态跟踪
- **错误处理** - 重试机制和失败恢复

## 功能特性

### 🚀 核心功能
- **多种触发器**：Cron表达式、固定间隔、指定时间、一次性任务
- **异步执行**：支持高性能的异步任务处理
- **并发控制**：可配置的最大并发任务数量
- **持久化支持**：任务配置和执行记录持久化

### 🔧 高级功能
- **重试机制**：自动重试失败的任务，支持指数退避
- **超时控制**：任务执行超时保护
- **状态管理**：完整的任务生命周期管理
- **执行限制**：支持最大执行次数限制
- **实时监控**：任务状态和执行情况实时监控

## 快速开始

### 基础使用示例

```python
import asyncio
from ai_agent_framework.tools import SchedulerTool, SchedulerConfig

async def basic_scheduler_example():
    # 创建调度器工具
    config = SchedulerConfig(
        auto_start=True,
        max_concurrent_tasks=10
    )
    
    scheduler = SchedulerTool(config)
    
    # 创建每分钟执行的任务
    result = await scheduler.execute({
        "action": "create_task",
        "task_config": {
            "name": "每分钟报告",
            "description": "每分钟输出当前时间",
            "trigger_type": "cron",
            "trigger_config": {
                "expression": "* * * * *"  # 每分钟执行
            },
            "task_config": {
                "type": "custom",
                "message": "定时报告"
            },
            "max_runs": 5  # 最多执行5次
        }
    })
    
    if result.success:
        print(f"任务创建成功: {result.result['task_id']}")
    
    # 等待任务执行
    await asyncio.sleep(300)  # 等待5分钟
    
    # 停止调度器
    await scheduler.execute({"action": "stop_scheduler"})

# 运行示例
asyncio.run(basic_scheduler_example())
```

## 支持的触发器类型

### 1. Cron表达式 (cron)

使用标准的5位Cron表达式：`分 时 日 月 周`

```python
# 每分钟执行
"trigger_config": {"expression": "* * * * *"}

# 每天上午9点执行
"trigger_config": {"expression": "0 9 * * *"}

# 每周一上午10点执行
"trigger_config": {"expression": "0 10 * * 1"}

# 每5分钟执行
"trigger_config": {"expression": "*/5 * * * *"}

# 工作日每小时执行
"trigger_config": {"expression": "0 * * * 1-5"}
```

### 2. 固定间隔 (interval)

按固定时间间隔重复执行：

```python
# 每30秒执行
"trigger_config": {"seconds": 30}

# 每5分钟执行
"trigger_config": {"seconds": 300}

# 每小时执行
"trigger_config": {"seconds": 3600}
```

### 3. 指定时间 (date)

在指定的日期时间执行一次：

```python
"trigger_config": {
    "datetime": "2025-12-31 23:59:59"
}
```

### 4. 一次性任务 (once)

立即执行一次：

```python
"trigger_config": {}
```

## 支持的任务类型

### 1. HTTP请求任务

```python
"task_config": {
    "type": "http_request",
    "url": "https://api.example.com/health",
    "method": "GET",
    "headers": {
        "Authorization": "Bearer token",
        "Content-Type": "application/json"
    },
    "data": {"key": "value"}
}
```

### 2. Shell命令任务

```python
"task_config": {
    "type": "shell_command",
    "command": "python /path/to/script.py --arg value"
}
```

### 3. Python函数任务

```python
"task_config": {
    "type": "python_function",
    "module": "mymodule",
    "function": "my_function",
    "args": ["arg1", "arg2"],
    "kwargs": {"key": "value"}
}
```

### 4. 自定义任务

```python
"task_config": {
    "type": "custom",
    "action": "backup",
    "target": "/data",
    "destination": "/backup"
}
```

## 支持的操作

### 任务管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `create_task` | 创建新任务 | `task_config` |
| `update_task` | 更新任务配置 | `task_id`, `task_config` |
| `delete_task` | 删除任务 | `task_id` |
| `get_task` | 获取任务信息 | `task_id` |
| `list_tasks` | 列出所有任务 | 无 |
| `pause_task` | 暂停任务 | `task_id` |
| `resume_task` | 恢复任务 | `task_id` |
| `run_task_now` | 立即执行任务 | `task_id` |

### 执行记录操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `get_executions` | 获取执行记录 | 可选 `task_id` |

### 调度器管理操作

| 操作 | 描述 | 必需参数 |
|------|------|----------|
| `start_scheduler` | 启动调度器 | 无 |
| `stop_scheduler` | 停止调度器 | 无 |
| `scheduler_status` | 获取调度器状态 | 无 |

## 详细使用指南

### 1. 创建Cron任务

```python
# 创建每天凌晨2点执行的备份任务
result = await scheduler.execute({
    "action": "create_task",
    "task_config": {
        "name": "数据库备份",
        "description": "每天凌晨2点备份数据库",
        "trigger_type": "cron",
        "trigger_config": {
            "expression": "0 2 * * *"
        },
        "task_config": {
            "type": "shell_command",
            "command": "mysqldump -u user -p password database > /backup/db_$(date +%Y%m%d).sql"
        },
        "timeout_seconds": 1800,  # 30分钟超时
        "max_retries": 2
    }
})
```

### 2. 创建HTTP监控任务

```python
# 创建API健康检查任务
result = await scheduler.execute({
    "action": "create_task",
    "task_config": {
        "name": "API健康检查",
        "description": "每5分钟检查API服务状态",
        "trigger_type": "interval",
        "trigger_config": {
            "seconds": 300
        },
        "task_config": {
            "type": "http_request",
            "url": "https://api.myservice.com/health",
            "method": "GET",
            "headers": {
                "User-Agent": "Health-Checker/1.0"
            }
        },
        "timeout_seconds": 30,
        "max_retries": 3
    }
})
```

### 3. 任务状态管理

```python
# 获取任务信息
result = await scheduler.execute({
    "action": "get_task",
    "task_id": "task_id_here"
})

if result.success:
    task = result.result["task"]
    print(f"任务状态: {task['status']}")
    print(f"执行次数: {task['run_count']}")
    print(f"下次执行: {task['next_run']}")

# 暂停任务
await scheduler.execute({
    "action": "pause_task",
    "task_id": "task_id_here"
})

# 恢复任务
await scheduler.execute({
    "action": "resume_task",
    "task_id": "task_id_here"
})

# 删除任务
await scheduler.execute({
    "action": "delete_task",
    "task_id": "task_id_here"
})
```

### 4. 查看执行记录

```python
# 获取所有执行记录
result = await scheduler.execute({
    "action": "get_executions"
})

if result.success:
    executions = result.result["executions"]
    for exec in executions:
        print(f"任务: {exec['task_id']}")
        print(f"状态: {exec['status']}")
        print(f"开始时间: {exec['start_time']}")
        print(f"持续时间: {exec['duration_seconds']}秒")

# 获取特定任务的执行记录
result = await scheduler.execute({
    "action": "get_executions",
    "task_id": "specific_task_id"
})
```

### 5. 监控调度器状态

```python
# 获取调度器状态
result = await scheduler.execute({
    "action": "scheduler_status"
})

if result.success:
    status = result.result
    print(f"调度器运行状态: {status['running']}")
    print(f"总任务数: {status['total_tasks']}")
    print(f"运行中任务: {status['running_tasks']}")
    print(f"等待中任务: {status['pending_tasks']}")
    print(f"活跃执行: {status['active_executions']}")
```

## Cron表达式详解

### 基本格式

```
分 时 日 月 周
│ │ │ │ │
│ │ │ │ └─── 星期几 (0-6, 0=周日)
│ │ │ └───── 月份 (1-12)
│ │ └─────── 日期 (1-31)
│ └───────── 小时 (0-23)
└─────────── 分钟 (0-59)
```

### 特殊字符

- `*` : 匹配所有值
- `-` : 范围，如 `1-5` 表示1到5
- `,` : 列表，如 `1,3,5` 表示1、3、5
- `/` : 步长，如 `*/5` 表示每5个单位

### 常用示例

```bash
# 每分钟
* * * * *

# 每小时的第0分钟
0 * * * *

# 每天午夜
0 0 * * *

# 每天上午9点
0 9 * * *

# 每周一上午9点
0 9 * * 1

# 每月1号午夜
0 0 1 * *

# 工作日上午9点到下午5点，每小时执行
0 9-17 * * 1-5

# 每5分钟
*/5 * * * *

# 每周一、三、五上午10点
0 10 * * 1,3,5
```

## 高级用法

### 1. 任务依赖和链式执行

```python
# 虽然当前版本不直接支持任务依赖，但可以通过自定义任务类型实现
"task_config": {
    "type": "python_function",
    "module": "workflow",
    "function": "execute_workflow",
    "args": ["step1", "step2", "step3"]
}
```

### 2. 条件执行

```python
# 在自定义任务中实现条件逻辑
"task_config": {
    "type": "python_function",
    "module": "conditional_tasks",
    "function": "conditional_backup",
    "kwargs": {
        "condition": "disk_usage > 80%",
        "action": "cleanup_and_backup"
    }
}
```

### 3. 批量任务管理

```python
async def create_monitoring_tasks():
    """批量创建监控任务"""
    services = [
        {"name": "API服务", "url": "https://api.example.com/health"},
        {"name": "数据库", "url": "https://db.example.com/ping"},
        {"name": "缓存服务", "url": "https://cache.example.com/status"}
    ]
    
    for service in services:
        await scheduler.execute({
            "action": "create_task",
            "task_config": {
                "name": f"{service['name']}健康检查",
                "trigger_type": "interval",
                "trigger_config": {"seconds": 300},
                "task_config": {
                    "type": "http_request",
                    "url": service["url"],
                    "method": "GET"
                }
            }
        })
```

## 最佳实践

### 1. 任务设计原则

- **幂等性**：确保任务可以安全地重复执行
- **超时设置**：为所有任务设置合理的超时时间
- **错误处理**：在任务中实现适当的错误处理逻辑
- **资源管理**：避免任务占用过多系统资源

### 2. 调度策略

- **避免重叠**：确保长时间运行的任务不会重叠执行
- **错峰执行**：将资源密集型任务安排在系统负载较低的时间
- **监控告警**：为关键任务设置监控和告警机制

### 3. 性能优化

```python
# 配置合理的并发数量
config = SchedulerConfig(
    max_concurrent_tasks=5,  # 根据系统资源调整
    execution_history_limit=1000,  # 限制历史记录数量
    cleanup_interval_hours=24  # 定期清理过期记录
)
```

## 故障排除

### 常见问题

1. **任务不执行**
   - 检查调度器是否已启动
   - 验证Cron表达式是否正确
   - 确认任务状态不是暂停状态

2. **任务执行失败**
   - 查看执行记录中的错误信息
   - 检查任务配置是否正确
   - 验证网络连接和权限设置

3. **性能问题**
   - 调整最大并发任务数量
   - 优化任务执行逻辑
   - 清理过期的执行记录

### 调试技巧

```python
# 启用详细日志
from ai_agent_framework.utils.logging_system import logging_system
logging_system.configure(level="DEBUG")

# 查看任务详细信息
result = await scheduler.execute({
    "action": "get_task",
    "task_id": "problematic_task_id"
})

# 查看执行历史
result = await scheduler.execute({
    "action": "get_executions",
    "task_id": "problematic_task_id"
})

# 立即执行任务进行测试
result = await scheduler.execute({
    "action": "run_task_now",
    "task_id": "test_task_id"
})
```

## 更多资源

- [AI Agent Framework 完整文档](./README.md)
- [Cron表达式在线生成器](https://crontab.guru/)
- [任务调度最佳实践](./任务调度最佳实践.md)
