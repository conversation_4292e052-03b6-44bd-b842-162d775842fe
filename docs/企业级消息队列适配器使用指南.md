# 企业级消息队列适配器使用指南

## 概述

AI Agent Framework 现已支持企业级消息队列服务，包括：
- **RabbitMQ** - 高可靠性的消息代理
- **Apache Kafka** - 高吞吐量的分布式流处理平台

通过统一的消息队列工具接口，您可以轻松地在不同消息队列服务之间切换，实现企业级的消息处理能力。

## 功能特性

### 🚀 核心功能
- **统一接口**：所有消息队列服务使用相同的API接口
- **异步操作**：支持高性能的异步消息处理
- **持久化支持**：支持消息持久化存储
- **事务支持**：支持消息事务处理
- **集群支持**：支持高可用集群部署

### 🔧 高级功能
- **死信队列**：自动处理失败消息
- **消息重试**：内置消息重试机制
- **优先级队列**：支持消息优先级处理
- **延迟消息**：支持定时和延迟消息
- **消费者组**：支持消费者组负载均衡

## 安装依赖

根据您要使用的消息队列服务，安装相应的依赖库：

```bash
# RabbitMQ
pip install aio-pika

# Apache Kafka
pip install aiokafka

# 安装所有企业级消息队列依赖
pip install ai-agent-framework[enterprise-queue]
```

## 快速开始

### RabbitMQ 使用示例

```python
import asyncio
from ai_agent_framework.tools import MessageQueueTool, MessageQueueType, Message

async def rabbitmq_example():
    # 配置RabbitMQ
    rabbitmq_config = {
        "connection_url": "amqp://guest:guest@localhost:5672/",
        "exchange_name": "ai_agent_exchange",
        "exchange_type": "direct",
        "durable": True
    }
    
    # 创建消息队列工具
    queue = MessageQueueTool(
        queue_type=MessageQueueType.RABBITMQ,
        connection_config=rabbitmq_config
    )
    
    # 连接到RabbitMQ
    await queue.execute({"action": "connect"})
    
    # 发送消息
    message = Message(
        content={"task": "process_data", "data_id": "12345"},
        metadata={"priority": "high", "source": "api"}
    )
    
    result = await queue.execute({
        "action": "send",
        "queue_name": "task_queue",
        "message": message.to_dict(),
        "priority": 5
    })
    
    if result.success:
        print("消息发送成功！")
    
    # 断开连接
    await queue.execute({"action": "disconnect"})

# 运行示例
asyncio.run(rabbitmq_example())
```

### Apache Kafka 使用示例

```python
import asyncio
from ai_agent_framework.tools import MessageQueueTool, MessageQueueType, Message

async def kafka_example():
    # 配置Kafka
    kafka_config = {
        "bootstrap_servers": "localhost:9092",
        "client_id": "ai_agent_client",
        "group_id": "processing_group",
        "auto_offset_reset": "latest"
    }
    
    # 创建消息队列工具
    queue = MessageQueueTool(
        queue_type=MessageQueueType.KAFKA,
        connection_config=kafka_config
    )
    
    # 连接到Kafka
    await queue.execute({"action": "connect"})
    
    # 发送消息到主题
    message = Message(
        content={"event": "user_action", "user_id": "67890"},
        metadata={"timestamp": "2025-08-28T10:00:00Z"}
    )
    
    result = await queue.execute({
        "action": "send",
        "queue_name": "user_events",  # Kafka主题
        "message": message.to_dict(),
        "routing_key": "user_67890"   # 分区键
    })
    
    if result.success:
        print("消息发送成功！")
    
    # 断开连接
    await queue.execute({"action": "disconnect"})

# 运行示例
asyncio.run(kafka_example())
```

## 支持的操作

### 基础操作

| 操作 | 描述 | 参数 |
|------|------|------|
| `connect` | 连接到消息队列服务 | 无 |
| `disconnect` | 断开连接 | 无 |
| `send` | 发送消息 | `queue_name`, `message`, `routing_key`, `priority` |
| `receive` | 接收消息 | `queue_name`, `timeout` |
| `subscribe` | 订阅消息 | `queue_name`, `callback`, `routing_key` |
| `unsubscribe` | 取消订阅 | `queue_name` |

### 管理操作

| 操作 | 描述 | 参数 |
|------|------|------|
| `info` | 获取队列/主题信息 | `queue_name` |
| `purge` | 清空队列 | `queue_name` |
| `delete` | 删除队列 | `queue_name` |

## 配置说明

### RabbitMQ 配置参数

```python
rabbitmq_config = {
    "connection_url": "amqp://用户名:密码@主机:端口/虚拟主机",
    "exchange_name": "交换机名称",
    "exchange_type": "交换机类型（direct/fanout/topic/headers）",
    "durable": True,  # 是否持久化
    "auto_delete": False  # 是否自动删除
}
```

### Apache Kafka 配置参数

```python
kafka_config = {
    "bootstrap_servers": "Kafka服务器地址",
    "client_id": "客户端ID",
    "group_id": "消费者组ID",
    "auto_offset_reset": "偏移重置策略（earliest/latest）",
    "enable_auto_commit": True,  # 是否自动提交偏移量
    "security_protocol": "安全协议（PLAINTEXT/SSL/SASL_PLAINTEXT/SASL_SSL）"
}
```

## 高级用法

### 消息订阅和处理

```python
async def message_subscription_example():
    """消息订阅示例"""
    
    # 定义消息处理函数
    async def process_message(message: Message):
        print(f"处理消息: {message.content}")
        # 执行业务逻辑
        await process_business_logic(message.content)
    
    # 订阅消息
    result = await queue.execute({
        "action": "subscribe",
        "queue_name": "task_queue",
        "callback": process_message
    })
    
    if result.success:
        print("订阅成功，开始处理消息...")
        
        # 保持运行状态
        await asyncio.sleep(60)  # 运行1分钟
        
        # 取消订阅
        await queue.execute({
            "action": "unsubscribe",
            "queue_name": "task_queue"
        })
```

### 批量消息处理

```python
async def batch_processing_example():
    """批量消息处理示例"""
    
    # 批量发送消息
    messages = []
    for i in range(100):
        message = Message(
            content={"batch_id": f"batch_{i}", "data": f"data_{i}"},
            metadata={"sequence": i}
        )
        messages.append(message)
    
    # 并发发送
    tasks = []
    for message in messages:
        task = queue.execute({
            "action": "send",
            "queue_name": "batch_queue",
            "message": message.to_dict()
        })
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    success_count = sum(1 for r in results if r.success)
    print(f"成功发送 {success_count}/{len(messages)} 条消息")
```

### 错误处理和重试

```python
async def error_handling_example():
    """错误处理示例"""
    
    async def reliable_send_message(queue, message, max_retries=3):
        """可靠的消息发送，包含重试机制"""
        
        for attempt in range(max_retries):
            try:
                result = await queue.execute({
                    "action": "send",
                    "queue_name": "reliable_queue",
                    "message": message.to_dict()
                })
                
                if result.success:
                    return True
                else:
                    print(f"发送失败，尝试 {attempt + 1}/{max_retries}: {result.error}")
                    
            except Exception as e:
                print(f"发送异常，尝试 {attempt + 1}/{max_retries}: {e}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        return False
    
    # 使用可靠发送
    message = Message(content={"important": "data"})
    success = await reliable_send_message(queue, message)
    
    if success:
        print("消息发送成功")
    else:
        print("消息发送失败，已达到最大重试次数")
```

## 性能优化

### 1. 连接池管理

```python
class QueueManager:
    """消息队列管理器，支持连接复用"""
    
    def __init__(self, queue_type, config):
        self.queue = MessageQueueTool(queue_type, config)
        self.connected = False
    
    async def __aenter__(self):
        if not self.connected:
            result = await self.queue.execute({"action": "connect"})
            if result.success:
                self.connected = True
                return self.queue
            else:
                raise ConnectionError(f"连接失败: {result.error}")
        return self.queue
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.connected:
            await self.queue.execute({"action": "disconnect"})
            self.connected = False

# 使用示例
async with QueueManager(MessageQueueType.RABBITMQ, config) as queue:
    # 在这里执行消息操作
    await queue.execute({...})
```

### 2. 并发处理

```python
async def concurrent_processing():
    """并发消息处理"""
    
    # 创建多个消费者
    consumers = []
    for i in range(5):  # 5个并发消费者
        consumer_config = kafka_config.copy()
        consumer_config["client_id"] = f"consumer_{i}"
        
        consumer = MessageQueueTool(
            queue_type=MessageQueueType.KAFKA,
            connection_config=consumer_config
        )
        consumers.append(consumer)
    
    # 启动所有消费者
    tasks = []
    for consumer in consumers:
        await consumer.execute({"action": "connect"})
        
        task = asyncio.create_task(
            consumer.execute({
                "action": "subscribe",
                "queue_name": "high_volume_topic",
                "callback": process_message
            })
        )
        tasks.append(task)
    
    # 等待处理完成
    await asyncio.gather(*tasks)
```

## 监控和调试

### 启用详细日志

```python
from ai_agent_framework.utils.logging_system import logging_system

# 启用DEBUG级别日志
logging_system.configure(level="DEBUG")
```

### 队列状态监控

```python
async def monitor_queue_status():
    """监控队列状态"""
    
    result = await queue.execute({
        "action": "info",
        "queue_name": "monitored_queue"
    })
    
    if result.success:
        info = result.result["info"]
        print(f"队列信息: {info}")
        
        # 对于Kafka，还可以获取消费者滞后信息
        if hasattr(queue._adapter, 'get_consumer_lag'):
            lag_info = await queue._adapter.get_consumer_lag("monitored_queue")
            print(f"消费者滞后: {lag_info}")
```

## 部署建议

### RabbitMQ 部署

```bash
# 使用Docker部署RabbitMQ
docker run -d \
  --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=password \
  rabbitmq:3-management
```

### Kafka 部署

```bash
# 使用Docker Compose部署Kafka
version: '3'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
  
  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否运行
   - 验证连接参数
   - 检查网络连接

2. **消息丢失**
   - 启用消息持久化
   - 使用事务或确认机制
   - 检查消费者处理逻辑

3. **性能问题**
   - 调整批处理大小
   - 增加并发消费者
   - 优化消息序列化

### 调试技巧

```python
# 启用详细日志
logging_system.configure(level="DEBUG")

# 检查连接状态
if hasattr(queue._adapter, '_connected'):
    print(f"连接状态: {queue._adapter._connected}")

# 监控消息处理
async def debug_message_handler(message):
    print(f"收到消息: {message.id}")
    print(f"内容: {message.content}")
    print(f"元数据: {message.metadata}")
    # 处理消息...
```

## 更多资源

- [RabbitMQ 官方文档](https://www.rabbitmq.com/documentation.html)
- [Apache Kafka 官方文档](https://kafka.apache.org/documentation/)
- [aio-pika 文档](https://aio-pika.readthedocs.io/)
- [aiokafka 文档](https://aiokafka.readthedocs.io/)
- [AI Agent Framework 完整文档](./README.md)
